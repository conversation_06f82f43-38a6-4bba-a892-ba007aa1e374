/*
 * Qt-based reimplementation of KiCad EDA_ITEM class implementation
 * 
 * This implementation provides the core functionality for all KiCad objects
 * using Qt frameworks for better performance, type safety, and maintainability.
 */

#include "eda_object_data.h"
#include <QtCore/QUuid>
#include <QtCore/QRegularExpression>
#include <QtCore/QStringList>
#include <QtCore/QMetaEnum>
#include <QtCore/QCoreApplication>
#include <QtCore/QLoggingCategory>

// Q_LOGGING_CATEGORY(EDA_OBJECT_DATA, "qt.eda.item")

// Implementation of QtEdaSearchData constructor
QtEdaSearchData::QtEdaSearchData(const QString& searchText) 
    : m_searchText(searchText), findString(m_searchText), matchMode(QtEdaSearchMatchMode::WholeWord) {
}

// Static member initialization
QHash<QtKicadType, QString> EDA_OBJECT_DATA::s_typeNames;
QHash<QtKicadType, QString> EDA_OBJECT_DATA::s_typeDescriptions;

//=============================================================================
// STATIC TYPE SYSTEM INITIALIZATION
//=============================================================================

void EDA_OBJECT_DATA::initializeTypeSystem()
{
    static bool initialized = false;
    if (initialized) return;
    
    // Initialize type names
    s_typeNames[QtKicadType::NotUsed] = QStringLiteral("<not used>");
    s_typeNames[QtKicadType::TypeNotInit] = QStringLiteral("<not initialized>");
    s_typeNames[QtKicadType::PcbT] = QStringLiteral("PCB");
    s_typeNames[QtKicadType::ScreenT] = QStringLiteral("Screen");
    s_typeNames[QtKicadType::PcbFootprintT] = QStringLiteral("Footprint");
    s_typeNames[QtKicadType::PcbPadT] = QStringLiteral("Pad");
    s_typeNames[QtKicadType::PcbShapeT] = QStringLiteral("PCB Shape");
    s_typeNames[QtKicadType::PcbReferenceImageT] = QStringLiteral("Reference Image");
    s_typeNames[QtKicadType::PcbFieldT] = QStringLiteral("PCB Field");
    s_typeNames[QtKicadType::PcbGeneratorT] = QStringLiteral("PCB Generator");
    s_typeNames[QtKicadType::PcbTextT] = QStringLiteral("PCB Text");
    s_typeNames[QtKicadType::PcbTextboxT] = QStringLiteral("PCB Textbox");
    s_typeNames[QtKicadType::PcbTableT] = QStringLiteral("PCB Table");
    s_typeNames[QtKicadType::PcbTablecellT] = QStringLiteral("PCB Table Cell");
    s_typeNames[QtKicadType::PcbTraceT] = QStringLiteral("PCB Track");
    s_typeNames[QtKicadType::PcbViaT] = QStringLiteral("PCB Via");
    s_typeNames[QtKicadType::PcbArcT] = QStringLiteral("PCB Arc");
    s_typeNames[QtKicadType::PcbMarkerT] = QStringLiteral("PCB Marker");
    s_typeNames[QtKicadType::PcbDimensionT] = QStringLiteral("PCB Dimension");
    s_typeNames[QtKicadType::PcbZoneT] = QStringLiteral("PCB Zone");
    s_typeNames[QtKicadType::PcbGroupT] = QStringLiteral("PCB Group");
    s_typeNames[QtKicadType::PcbTargetT] = QStringLiteral("PCB Target");
    
    // Initialize translated descriptions (simplified - in real app would use QCoreApplication::translate)
    s_typeDescriptions = s_typeNames; // For now, use same strings
    
    initialized = true;
}

QString EDA_OBJECT_DATA::getTypeName(QtKicadType type)
{
    initializeTypeSystem();
    return s_typeNames.value(type, QStringLiteral("Unknown Type"));
}

//=============================================================================
// CONSTRUCTION AND DESTRUCTION
//=============================================================================

EDA_OBJECT_DATA::EDA_OBJECT_DATA(EDA_OBJECT_DATA* parent, QtKicadType itemType)
    : m_itemType(itemType)
    , m_uuid(QUuid::createUuid())
    , m_flags(EDA_OBJECT_DATAFlags())
    , m_edaParent(parent)
{
    initializeTypeSystem();
    // qCDebug(EDA_OBJECT_DATA) << "Created EDA_OBJECT_DATA of type" << getTypeName(itemType) 
    //                    << "with UUID" << m_uuid.toString();
}

EDA_OBJECT_DATA::EDA_OBJECT_DATA(QtKicadType itemType)
    : m_itemType(itemType)
    , m_uuid(QUuid::createUuid())
    , m_flags(EDA_OBJECT_DATAFlags())
    , m_edaParent(nullptr)
{
    initializeTypeSystem();
    // qCDebug(EDA_OBJECT_DATA) << "Created EDA_OBJECT_DATA of type" << getTypeName(itemType)
    //                    << "with UUID" << m_uuid.toString();
}

EDA_OBJECT_DATA::EDA_OBJECT_DATA(const EDA_OBJECT_DATA& other)
    : m_itemType(other.m_itemType)
    , m_uuid(other.m_uuid) // Copy UUID - in some cases might want to generate new one
    , m_flags(other.m_flags)
    , m_edaParent(other.m_edaParent)
{
    // qCDebug(EDA_OBJECT_DATA) << "Copied EDA_OBJECT_DATA of type" << getTypeName(m_itemType);
}

EDA_OBJECT_DATA::~EDA_OBJECT_DATA()
{
    // qCDebug(EDA_OBJECT_DATA) << "Destroying EDA_OBJECT_DATA of type" << getTypeName(m_itemType)
    //                    << "with UUID" << m_uuid.toString();
}

//=============================================================================
// TYPE IDENTIFICATION AND CLASSIFICATION
//=============================================================================

bool EDA_OBJECT_DATA::isType(const QVector<QtKicadType>& scanTypes) const
{
    for (QtKicadType scanType : scanTypes) {
        if (scanType == QtKicadType::SchLocateAnyT || scanType == m_itemType) {
            return true;
        }
    }
    return false;
}

QString EDA_OBJECT_DATA::getTypeDescription() const
{
    initializeTypeSystem();
    return s_typeDescriptions.value(m_itemType, QStringLiteral("Unknown Type"));
}

QString EDA_OBJECT_DATA::getClassName() const
{
    // Provide base implementation - should be overridden by derived classes
    return getTypeName(m_itemType);
}

QString EDA_OBJECT_DATA::getFriendlyName() const
{
    return getTypeDescription();
}

//=============================================================================
// FLAGS MANAGEMENT
//=============================================================================

void EDA_OBJECT_DATA::setFlags(EDA_OBJECT_DATAFlags flags)
{
    if (m_flags != flags) {
        EDA_OBJECT_DATAFlags oldFlags = m_flags;
        m_flags = flags;
        
        // Check for changes in selection and modified states
        if (((oldFlags & EDA_OBJECT_DATAFlag::IsSelected) != 0) != 
            ((flags & EDA_OBJECT_DATAFlag::IsSelected) != 0)) {
            if (m_selectionChangedCallback) {
                m_selectionChangedCallback((flags & EDA_OBJECT_DATAFlag::IsSelected) != 0);
            }
        }
        
        if (((oldFlags & EDA_OBJECT_DATAFlag::IsChanged) != 0) != 
            ((flags & EDA_OBJECT_DATAFlag::IsChanged) != 0)) {
            if (m_modifiedChangedCallback) {
                m_modifiedChangedCallback((flags & EDA_OBJECT_DATAFlag::IsChanged) != 0);
            }
        }
        
        if (m_flagsChangedCallback) {
            m_flagsChangedCallback(flags);
        }
    }
}

void EDA_OBJECT_DATA::setFlag(EDA_OBJECT_DATAFlag flag, bool enable)
{
    EDA_OBJECT_DATAFlags newFlags = m_flags;
    if (enable) {
        newFlags |= flag;
    } else {
        newFlags &= ~flag;
    }
    setFlags(newFlags);
}

void EDA_OBJECT_DATA::clearFlags(EDA_OBJECT_DATAFlags flags)
{
    EDA_OBJECT_DATAFlags newFlags = m_flags;
    newFlags &= ~flags;
    setFlags(newFlags);
}

void EDA_OBJECT_DATA::setModified()
{
    setFlag(EDA_OBJECT_DATAFlag::IsChanged, true);
    propagateModified();
}

EDA_OBJECT_DATAFlags EDA_OBJECT_DATA::getEditFlags() const
{
    const EDA_OBJECT_DATAFlags editMask = 
        EDA_OBJECT_DATAFlag::IsNew | EDA_OBJECT_DATAFlag::IsPasted | EDA_OBJECT_DATAFlag::IsMoving |
        EDA_OBJECT_DATAFlag::IsBroken | EDA_OBJECT_DATAFlag::IsChanged | EDA_OBJECT_DATAFlag::StructDeleted;
    
    return m_flags & editMask;
}

EDA_OBJECT_DATAFlags EDA_OBJECT_DATA::getTempFlags() const
{
    const EDA_OBJECT_DATAFlags tempMask = 
        EDA_OBJECT_DATAFlag::Candidate | EDA_OBJECT_DATAFlag::SelectedByDrag | 
        EDA_OBJECT_DATAFlag::IsLinked | EDA_OBJECT_DATAFlag::SkipStruct;
    
    return m_flags & tempMask;
}

void EDA_OBJECT_DATA::clearEditFlags()
{
    clearFlags(getEditFlags());
}

void EDA_OBJECT_DATA::clearTempFlags()
{
    clearFlags(getTempFlags());
}

//=============================================================================
// GEOMETRIC OPERATIONS
//=============================================================================

QRectF EDA_OBJECT_DATA::getBoundingBox() const
{
    // Default implementation returns zero-sized box
    // Derived classes should override this
    return QRectF(0, 0, 0, 0);
}

bool EDA_OBJECT_DATA::hitTest(const QPointF& position, double accuracy) const
{
    // Default implementation uses bounding box
    QRectF bbox = getBoundingBox();
    if (accuracy > 0.0) {
        bbox.adjust(-accuracy, -accuracy, accuracy, accuracy);
    }
    return bbox.contains(position);
}

bool EDA_OBJECT_DATA::hitTest(const QRectF& rect, bool contained, double accuracy) const
{
    QRectF bbox = getBoundingBox();
    if (accuracy > 0.0) {
        bbox.adjust(-accuracy, -accuracy, accuracy, accuracy);
    }
    
    if (contained) {
        return rect.contains(bbox);
    } else {
        return rect.intersects(bbox);
    }
}

//=============================================================================
// SEARCH AND REPLACE FUNCTIONALITY
//=============================================================================

bool EDA_OBJECT_DATA::matches(const QtEdaSearchData& searchData, void* auxData) const
{
    Q_UNUSED(searchData)
    Q_UNUSED(auxData)
    // Base class has no searchable text
    return false;
}

bool EDA_OBJECT_DATA::replace(const QtEdaSearchData& searchData, void* auxData)
{
    Q_UNUSED(searchData)
    Q_UNUSED(auxData)
    // Base class has no replaceable text
    return false;
}

bool EDA_OBJECT_DATA::matchesText(const QString& text, const QtEdaSearchData& searchData) const
{
    QString searchText = searchData.findString;
    QString targetText = text;
    
    // Don't match if searching for replaceable item and the item doesn't support replacement
    if (searchData.searchAndReplace && !isReplaceable()) {
        return false;
    }
    
    // Case sensitivity handling
    if (!searchData.matchCase()) {
        targetText = targetText.toUpper();
        searchText = searchText.toUpper();
    }
    
    switch (searchData.matchMode) {
        case QtEdaSearchMatchMode::WholeWord: {
            // Use QRegularExpression for whole word matching
            QString pattern = QStringLiteral("\\b%1\\b").arg(QRegularExpression::escape(searchText));
            QRegularExpression regex(pattern, 
                searchData.matchCase() ? QRegularExpression::NoPatternOption : 
                                     QRegularExpression::CaseInsensitiveOption);
            return regex.match(text).hasMatch();
        }
        
        case QtEdaSearchMatchMode::Wildcard: {
            QRegularExpression regex(QRegularExpression::wildcardToRegularExpression(searchText),
                searchData.matchCase() ? QRegularExpression::NoPatternOption : 
                                     QRegularExpression::CaseInsensitiveOption);
            return regex.match(text).hasMatch();
        }
        
        case QtEdaSearchMatchMode::Permissive: {
            // Simple permissive matching - contains all characters in order
            // This is a simplified version - real implementation would be more sophisticated
            int pos = 0;
            for (const QChar& ch : searchText) {
                pos = targetText.indexOf(ch, pos);
                if (pos == -1) return false;
                pos++;
            }
            return true;
        }
        
        case QtEdaSearchMatchMode::Plain:
        default:
            return targetText.contains(searchText);
    }
}

bool EDA_OBJECT_DATA::replaceText(const QtEdaSearchData& searchData, QString& text)
{
    QString searchStr = searchData.findString;
    QString replaceStr = searchData.replaceString;
    QString originalText = text;
    bool replaced = false;
    
    if (!searchData.matchCase()) {
        // For case-insensitive replacement, we need to preserve original case
        QRegularExpression regex(QRegularExpression::escape(searchStr), 
                               QRegularExpression::CaseInsensitiveOption);
        
        if (searchData.matchMode == QtEdaSearchMatchMode::WholeWord) {
            regex.setPattern(QStringLiteral("\\b%1\\b").arg(QRegularExpression::escape(searchStr)));
        }
        
        text = text.replace(regex, replaceStr);
        replaced = (text != originalText);
    } else {
        // Case-sensitive replacement
        if (searchData.matchMode == QtEdaSearchMatchMode::WholeWord) {
            QRegularExpression regex(QStringLiteral("\\b%1\\b").arg(QRegularExpression::escape(searchStr)));
            text = text.replace(regex, replaceStr);
        } else {
            text = text.replace(searchStr, replaceStr);
        }
        replaced = (text != originalText);
    }
    
    return replaced;
}

//=============================================================================
// VISITOR PATTERN FOR ITERATION
//=============================================================================

QtInspectResult EDA_OBJECT_DATA::visit(QtInspector inspector, void* testData, 
                                 const QVector<QtKicadType>& scanTypes)
{
    if (isType(scanTypes)) {
        if (inspector(this, testData) == QtInspectResult::Quit) {
            return QtInspectResult::Quit;
        }
    }
    
    return QtInspectResult::Continue;
}

//=============================================================================
// OBJECT CLONING AND COPYING
//=============================================================================

EDA_OBJECT_DATA* EDA_OBJECT_DATA::clone() const
{
    // qCWarning(EDA_OBJECT_DATA) << "Clone not implemented in derived class" << getClassName()
    //                      << "- this should be overridden!";
    return nullptr;
}

EDA_OBJECT_DATA& EDA_OBJECT_DATA::operator=(const EDA_OBJECT_DATA& other)
{
    if (this != &other) {
        // Note: m_itemType and m_uuid are const, so they can't be assigned
        m_flags = other.m_flags;
        m_edaParent = other.m_edaParent;
        
        // Qt object system removed - no parent to update
        
    }
    
    return *this;
}

bool EDA_OBJECT_DATA::operator<(const EDA_OBJECT_DATA& other) const
{
    // Default comparison by type, then by UUID
    if (m_itemType != other.m_itemType) {
        return static_cast<int>(m_itemType) < static_cast<int>(other.m_itemType);
    }
    
    return m_uuid.toString() < other.m_uuid.toString();
}

//=============================================================================
// USER INTERFACE SUPPORT
//=============================================================================

// getItemDescription is already defined inline in the header

//=============================================================================
// PROTECTED HELPER METHODS
//=============================================================================

void EDA_OBJECT_DATA::propagateModified()
{
    // Propagate modification flag to parent
    if (m_edaParent) {
        m_edaParent->setModified();
    }
}

//=============================================================================
// DEBUGGING SUPPORT
//=============================================================================

#ifdef QT_DEBUG
void EDA_OBJECT_DATA::show(int nestLevel, QDebug& debug) const
{
    showDummy(debug);
}

void EDA_OBJECT_DATA::showDummy(QDebug& debug) const
{
    QString className = getClassName().toLower();
    debug << QString("<%1> Need show() override for this class </%1>")
             .arg(className);
}

QDebug& EDA_OBJECT_DATA::nestedSpace(int nestLevel, QDebug& debug)
{
    for (int i = 0; i < nestLevel; ++i) {
        debug << "  ";
    }
    return debug;
}
#endif

//=============================================================================
// QT METATYPE REGISTRATION
//=============================================================================

// No Qt metatype registration needed - standard C++ class