/*
 * Qt-based reimplementation of KiCad PCB_TRACK class implementation
 */

#include "eda_track_data.h"
#include "eda_board_data.h"
#include "eda_footprint_data.h"
#include "qt_temporary_implementations.h"
#include <QtCore/QDebug>
#include <QtCore/QMutexLocker>
#include <QtCore/QLineF>
#include <QtMath>
#include <algorithm>

Q_LOGGING_CATEGORY(eDA_TRACK_DATA, "qt.pcbnew.track")

//=============================================================================
// CONSTRUCTION AND DESTRUCTION
//=============================================================================

EDA_TRACK_DATA::EDA_TRACK_DATA(EDA_BOARD_OBJECT_DATA* parent, QtKicadType type)
    : EDA_BOARD_CONNECTED_OBJECT(parent, type)
    , m_start(0.0, 0.0)
    , m_end(0.0, 0.0)
    , m_hasSolderMask(true)
    , m_width(0)
{
    setLayer(QtPcbLayerId::FCu);  // Default to front copper
}

EDA_TRACK_DATA::EDA_TRACK_DATA(const EDA_TRACK_DATA& other)
    : EDA_BOARD_CONNECTED_OBJECT(other.getParent(), other.getType())
    , m_start(other.m_start)
    , m_end(other.m_end)
    , m_hasSolderMask(other.m_hasSolderMask)
    , m_solderMaskMargin(other.m_solderMaskMargin)
    , m_width(other.m_width)
{
}

EDA_TRACK_DATA::~EDA_TRACK_DATA() = default;

EDA_TRACK_DATA& EDA_TRACK_DATA::operator=(const EDA_TRACK_DATA& other)
{
    if (this != &other) {
        // Copy base class data manually since operator= may be deleted
        m_start = other.m_start;
        m_end = other.m_end;
        m_hasSolderMask = other.m_hasSolderMask;
        m_solderMaskMargin = other.m_solderMaskMargin;
        m_width = other.m_width;
        clearShapeCache();
    }
    return *this;
}

//=============================================================================
// TYPE IDENTIFICATION
//=============================================================================

bool EDA_TRACK_DATA::classOf(const EDA_OBJECT_DATA* item)
{
    return item && item->getType() == QtKicadType::PCB_TRACE_T;
}

//=============================================================================
// GEOMETRY PROPERTIES
//=============================================================================

void EDA_TRACK_DATA::setStart(const QPointF& start)
{
    if (m_start != start) {
        m_start = start;
        clearShapeCache();
    }
}

void EDA_TRACK_DATA::setEnd(const QPointF& end)
{
    if (m_end != end) {
        m_end = end;
        clearShapeCache();
    }
}

const QPointF& EDA_TRACK_DATA::getEndPoint(QtEndpointType endPoint) const
{
    return (endPoint == QtEndpointType::Start) ? m_start : m_end;
}

//=============================================================================
// POSITION AND ORIENTATION
//=============================================================================

void EDA_TRACK_DATA::setPosition(const QPointF& position)
{
    setStart(position);
}

QPointF EDA_TRACK_DATA::getFocusPosition() const
{
    return (m_start + m_end) / 2.0;
}

//=============================================================================
// WIDTH PROPERTIES
//=============================================================================

void EDA_TRACK_DATA::setWidth(int width)
{
    if (m_width != width) {
        m_width = width;
        clearShapeCache();
        // Width changed
    }
}

//=============================================================================
// SOLDER MASK PROPERTIES
//=============================================================================

void EDA_TRACK_DATA::setHasSolderMask(bool hasMask)
{
    if (m_hasSolderMask != hasMask) {
        m_hasSolderMask = hasMask;
        // Solder mask changed
    }
}

void EDA_TRACK_DATA::setLocalSolderMaskMargin(std::optional<int> margin)
{
    if (m_solderMaskMargin != margin) {
        m_solderMaskMargin = margin;
        // Solder mask changed
    }
}

//int EDA_TRACK_DATA::getSolderMaskExpansion() const
//{
//    if (m_solderMaskMargin.has_value()) {
//        return m_solderMaskMargin.value();
//    }
//    
//    if (auto* board = getBoard()) {
//        return board->getDesignSettings().getSolderMaskExpansion();
//    }
//    
//    return 0;
//}

//=============================================================================
// LAYER MANAGEMENT
//=============================================================================

bool EDA_TRACK_DATA::isOnLayer(QtPcbLayerId layer) const
{
    return getLayer() == layer;
}

QtLayerSet EDA_TRACK_DATA::getLayerSet() const
{
    return QtLayerSet(getLayer());
}

void EDA_TRACK_DATA::setLayerSet(const QtLayerSet& layers)
{
    if (layers.count() == 1) {
        QVector<QtPcbLayerId> seq = layers.sequence();
        if (!seq.empty()) {
            setLayer(seq.front());
        }
    }
}

//=============================================================================
// GEOMETRIC CALCULATIONS
//=============================================================================

double EDA_TRACK_DATA::getLength() const
{
    return QLineF(m_start, m_end).length();
}

bool EDA_TRACK_DATA::isNull() const
{
    return (getType() == QtKicadType::PCB_VIA_T) || (m_start == m_end);
}

//QtEdaItemFlags EDA_TRACK_DATA::isPointOnEnds(const QPointF& point, int minDist) const
//{
//    QtEdaItemFlags result = static_cast<QtEdaItemFlags>(0);
//    
//    if (minDist < 0) {
//        minDist = m_width / 2;
//    }
//    
//    double distToStart = QLineF(point, m_start).length();
//    double distToEnd = QLineF(point, m_end).length();
//    
//    if (distToStart <= minDist) {
//        result = static_cast<QtEdaItemFlags>(static_cast<int>(result) | static_cast<int>(QtEndpointType::Start));
//    }
//    
//    if (distToEnd <= minDist) {
//        result = static_cast<QtEdaItemFlags>(static_cast<int>(result) | static_cast<int>(QtEndpointType::End));
//    }
//    
//    return result;
//}

bool EDA_TRACK_DATA::approxCollinear(const EDA_TRACK_DATA& track) const
{
    // Check if tracks share an endpoint
    bool sharedEndpoint = (m_start == track.m_start || m_start == track.m_end ||
                          m_end == track.m_start || m_end == track.m_end);
    
    if (!sharedEndpoint) {
        return false;
    }
    
    // Calculate angle between track vectors
    QLineF line1(m_start, m_end);
    QLineF line2(track.m_start, track.m_end);
    
    double angle = std::abs(line1.angleTo(line2));
    
    // Consider collinear if angle is close to 0 or 180 degrees
    return (angle < 1.0 || angle > 179.0);
}

//=============================================================================
// TRANSFORMATIONS
//=============================================================================

void EDA_TRACK_DATA::move(const QPointF& moveVector)
{
    m_start += moveVector;
    m_end += moveVector;
    clearShapeCache();
    // Geometry changed
}

void EDA_TRACK_DATA::rotate(const QPointF& rotationCenter, double angleRadians)
{
    double angleDegrees = qRadiansToDegrees(angleRadians);
    
    // Rotate start point
    QLineF startLine(rotationCenter, m_start);
    startLine.setAngle(startLine.angle() + angleDegrees);
    m_start = startLine.p2();
    
    // Rotate end point
    QLineF endLine(rotationCenter, m_end);
    endLine.setAngle(endLine.angle() + angleDegrees);
    m_end = endLine.p2();
    
    clearShapeCache();
    // Geometry changed
}

void EDA_TRACK_DATA::mirror(const QPointF& center, QtFlipDirection flipDirection)
{
    if (flipDirection == QtFlipDirection::LeftRight) {
        m_start.setX(2 * center.x() - m_start.x());
        m_end.setX(2 * center.x() - m_end.x());
    } else {
        m_start.setY(2 * center.y() - m_start.y());
        m_end.setY(2 * center.y() - m_end.y());
    }
    
    clearShapeCache();
    // Geometry changed
}

void EDA_TRACK_DATA::flip(const QPointF& center, QtFlipDirection flipDirection)
{
    mirror(center, flipDirection);
    
    // Flip layer
    if (auto* board = getBoard()) {
        setLayer(QtLayerIdUtils::flipLayer(getLayer(), board->getCopperLayerCount()));
    }
}

//=============================================================================
// SHAPE GENERATION
//=============================================================================

//QSharedPointer<QtShape> EDA_TRACK_DATA::getEffectiveShape(QtPcbLayerId layer, QtFlashing flash) const
//{
//    QMutexLocker locker(&m_shapeCacheMutex);
//    
//    if (m_shapeDirty || !m_effectiveShape) {
//        buildEffectiveShape();
//    }
//    
//    return m_effectiveShape;
//}

void EDA_TRACK_DATA::buildEffectiveShape() const
{
    m_effectiveShape = std::make_shared<QtShapeSegment>(m_start, m_end, m_width);
    m_shapeDirty = false;
}

void EDA_TRACK_DATA::transformShapeToPolygon(QtShapePolySet& buffer, QtPcbLayerId layer,
                                        double clearance, double error, QtErrorLoc errorLocation,
                                        bool ignoreLineWidth) const
{
    int width = ignoreLineWidth ? 0 : m_width;
    
    // Temporary implementation - segmentToPolygon not yet available
    // QtTransformUtils::segmentToPolygon(buffer, m_start, m_end, 
    //                                  error, width + 2 * clearance);
}

//=============================================================================
// HIT TESTING
//=============================================================================

bool EDA_TRACK_DATA::hitTest(const QPointF& position, double accuracy) const
{
    double maxDist = (m_width / 2.0) + accuracy;
    
    // Use shape for accurate hit testing
    auto shape = getEffectiveShape();
    // return shape && shape->collide(position, maxDist);
    // Temporary implementation until collide method is available
    QLineF trackLine(m_start, m_end);
    double distToLine = 0.0;
    QPointF p1 = trackLine.p1();
    QPointF p2 = trackLine.p2();
    double A = position.x() - p1.x();
    double B = position.y() - p1.y();
    double C = p2.x() - p1.x();
    double D = p2.y() - p1.y();
    double dot = A * C + B * D;
    double len_sq = C * C + D * D;
    if (len_sq != 0) {
        double param = dot / len_sq;
        if (param < 0) {
            distToLine = QLineF(position, p1).length();
        } else if (param > 1) {
            distToLine = QLineF(position, p2).length();
        } else {
            QPointF projection = p1 + QPointF(param * C, param * D);
            distToLine = QLineF(position, projection).length();
        }
    } else {
        distToLine = QLineF(position, p1).length();
    }
    return distToLine <= maxDist;
}

bool EDA_TRACK_DATA::hitTest(const QRectF& rect, bool contained, double accuracy) const
{
    QRectF arect = rect.normalized();
    
    if (contained) {
        return arect.contains(m_start) && arect.contains(m_end);
    } else {
        // Check if track intersects rectangle
        QLineF trackLine(m_start, m_end);
        
        // Check rectangle edges
        QLineF edges[4] = {
            QLineF(arect.topLeft(), arect.topRight()),
            QLineF(arect.topRight(), arect.bottomRight()),
            QLineF(arect.bottomRight(), arect.bottomLeft()),
            QLineF(arect.bottomLeft(), arect.topLeft())
        };
        
        for (const auto& edge : edges) {
            QPointF intersection;
            if (trackLine.intersects(edge, &intersection) == QLineF::BoundedIntersection) {
                return true;
            }
        }
        
        // Check if either endpoint is inside
        return arect.contains(m_start) || arect.contains(m_end);
    }
}

//=============================================================================
// BOUNDING BOX
//=============================================================================

QRectF EDA_TRACK_DATA::getBoundingBox() const
{
    QRectF bbox;
    bbox.setLeft(std::min(m_start.x(), m_end.x()) - m_width / 2.0);
    bbox.setRight(std::max(m_start.x(), m_end.x()) + m_width / 2.0);
    bbox.setTop(std::min(m_start.y(), m_end.y()) - m_width / 2.0);
    bbox.setBottom(std::max(m_start.y(), m_end.y()) + m_width / 2.0);
    return bbox;
}

QRectF EDA_TRACK_DATA::viewBBox() const
{
    QRectF bbox = getBoundingBox();
    
    // Add clearance for selection
    const double margin = m_width * 0.1;
    return bbox.adjusted(-margin, -margin, margin, margin);
}

//=============================================================================
// UI AND DESCRIPTION
//=============================================================================

void EDA_TRACK_DATA::getMsgPanelInfo(QtDrawFrame* frame, QList<QtMsgPanelItem>& list)
{
    getMsgPanelInfoBase_Common(frame, list);
    
    // Add track-specific information
    list.append(QtMsgPanelItem(QStringLiteral("Type"), QStringLiteral("Track")));
    list.append(QtMsgPanelItem(QStringLiteral("Layer"), getLayerName()));
    list.append(QtMsgPanelItem(QStringLiteral("Width"), QString::number(m_width)));
    list.append(QtMsgPanelItem(QStringLiteral("Length"), QString::number(getLength())));
    
    if (getNetCode() > 0) {
        list.append(QtMsgPanelItem(QStringLiteral("Net"), getNetName()));
        list.append(QtMsgPanelItem(QStringLiteral("Net Class"), getNetClassName()));
    }
}

QString EDA_TRACK_DATA::getFriendlyName() const
{
    return QStringLiteral("Track");
}

QString EDA_TRACK_DATA::getItemDescription(bool full) const
{
    QString desc = QString("Track on %1").arg(getLayerName());
    
    if (full) {
        desc += QString(" [%1]").arg(getNetName());
    }
    
    return desc;
}

QtBitmaps EDA_TRACK_DATA::getMenuImage() const
{
    return static_cast<QtBitmaps>(0); // Temporary placeholder for AddTracks
}

//=============================================================================
// VIEWING
//=============================================================================

QVector<int> EDA_TRACK_DATA::viewGetLayers() const
{
    QVector<int> layers;
    
    // Add track layer
    layers.append(static_cast<int>(getLayer()));
    
    // Add net name layer if track has a net
    if (isOnCopperLayer() && getBoard() && getBoard()->isElementVisible(QtLayerGalId::TracksNetnames)) {
        if (getNetCode() > 0) {
            // layers.append(static_cast<int>(getNetNameLayer(getLayer())));
            // Temporary implementation until getNetNameLayer is available
        }
    }
    
    return layers;
}

double EDA_TRACK_DATA::viewGetLOD(int layer, const QtView* view) const
{
    const double HIDE = std::numeric_limits<double>::max();
    const double SHOW = 0.0;
    
    if (!view) {
        return SHOW;
    }
    
    // Hide very small tracks at low zoom
    if (view->getScale() < 1.0) {
        if (m_width < view->toWorld(2)) {
            return HIDE;
        }
    }
    
    return SHOW;
}

//=============================================================================
// CONSTRAINTS
//=============================================================================

//QtMinOptMax<int> EDA_TRACK_DATA::getWidthConstraint(QString* source) const
//{
//    if (auto* board = getBoard()) {
//        return board->getDesignSettings().getTrackWidthConstraint(this, source);
//    }
//    
//    return QtMinOptMax<int>();
//}

//=============================================================================
// COMPARISON AND DUPLICATION
//=============================================================================

EDA_BOARD_OBJECT_DATA* EDA_TRACK_DATA::clone() const
{
    return new EDA_TRACK_DATA(*this);
}

double EDA_TRACK_DATA::similarity(const EDA_BOARD_OBJECT_DATA& other) const
{
    if (other.getType() != getType()) {
        return 0.0;
    }
    
    const EDA_TRACK_DATA* track = static_cast<const EDA_TRACK_DATA*>(&other);
    
    double similarity = 1.0;
    
    // Check endpoints
    if (m_start != track->m_start || m_end != track->m_end) {
        similarity *= 0.9;
    }
    
    // Check width
    if (m_width != track->m_width) {
        similarity *= 0.9;
    }
    
    // Check layer
    if (getLayer() != track->getLayer()) {
        similarity *= 0.8;
    }
    
    // Check net
    if (getNetCode() != track->getNetCode()) {
        similarity *= 0.5;
    }
    
    return similarity;
}

bool EDA_TRACK_DATA::operator==(const EDA_BOARD_OBJECT_DATA& other) const
{
    if (other.getType() != getType()) {
        return false;
    }
    
    return operator==(static_cast<const EDA_TRACK_DATA&>(other));
}

bool EDA_TRACK_DATA::operator==(const EDA_TRACK_DATA& other) const
{
    return m_start == other.m_start &&
           m_end == other.m_end &&
           m_width == other.m_width &&
           m_hasSolderMask == other.m_hasSolderMask &&
           m_solderMaskMargin == other.m_solderMaskMargin &&
           getLayer() == other.getLayer() &&
           getNetCode() == other.getNetCode();
}

//=============================================================================
// VISIT PATTERN
//=============================================================================

QtInspectResult EDA_TRACK_DATA::visit(QtInspector inspector, void* testData,
                                 const QVector<QtKicadType>& scanTypes)
{
    for (QtKicadType type : scanTypes) {
        if (type == QtKicadType::PCB_TRACE_T || type == getType()) {
            if (inspector(this, testData) == QtInspectResult::Quit) {
                return QtInspectResult::Quit;
            }
        }
    }
    
    return QtInspectResult::Continue;
}

//=============================================================================
// QT SERIALIZATION
//=============================================================================

QVariantMap EDA_TRACK_DATA::toVariantMap() const
{
    QVariantMap map = EDA_BOARD_CONNECTED_OBJECT::toVariantMap();
    
    map["start"] = m_start;
    map["end"] = m_end;
    map["width"] = m_width;
    map["hasSolderMask"] = m_hasSolderMask;
    
    if (m_solderMaskMargin.has_value()) {
        map["solderMaskMargin"] = m_solderMaskMargin.value();
    }
    
    return map;
}

void EDA_TRACK_DATA::fromVariantMap(const QVariantMap& map)
{
    EDA_BOARD_CONNECTED_OBJECT::fromVariantMap(map);
    
    m_start = map.value("start").toPointF();
    m_end = map.value("end").toPointF();
    m_width = map.value("width").toInt();
    m_hasSolderMask = map.value("hasSolderMask", true).toBool();
    
    if (map.contains("solderMaskMargin")) {
        m_solderMaskMargin = map.value("solderMaskMargin").toInt();
    } else {
        m_solderMaskMargin.reset();
    }
    
    clearShapeCache();
}

//=============================================================================
// PROTECTED HELPER METHODS
//=============================================================================

void EDA_TRACK_DATA::swapData(EDA_BOARD_OBJECT_DATA* other)
{
    EDA_BOARD_CONNECTED_OBJECT::swapData(other);
    
    EDA_TRACK_DATA* track = static_cast<EDA_TRACK_DATA*>(other);
    std::swap(m_start, track->m_start);
    std::swap(m_end, track->m_end);
    std::swap(m_width, track->m_width);
    std::swap(m_hasSolderMask, track->m_hasSolderMask);
    std::swap(m_solderMaskMargin, track->m_solderMaskMargin);
    
    clearShapeCache();
    track->clearShapeCache();
}

void EDA_TRACK_DATA::getMsgPanelInfoBase_Common(QtDrawFrame* frame, QList<QtMsgPanelItem>& list) const
{
    // Add common track information
    list.append(QtMsgPanelItem(QStringLiteral("Start"), QString("(%1, %2)").arg(m_start.x()).arg(m_start.y())));
    list.append(QtMsgPanelItem(QStringLiteral("End"), QString("(%1, %2)").arg(m_end.x()).arg(m_end.y())));
}

void EDA_TRACK_DATA::clearShapeCache() const
{
    QMutexLocker locker(&m_shapeCacheMutex);
    m_shapeDirty = true;
    m_effectiveShape.reset();
}

//=============================================================================
// COMPARATOR IMPLEMENTATION
//=============================================================================

bool EDA_TRACK_DATA::Comparator::operator()(const EDA_TRACK_DATA* first, const EDA_TRACK_DATA* second) const
{
    if (first->getNetCode() != second->getNetCode()) {
        return first->getNetCode() < second->getNetCode();
    }
    
    if (first->getLayer() != second->getLayer()) {
        return first->getLayer() < second->getLayer();
    }
    
    if (first->m_start != second->m_start) {
        if (first->m_start.x() != second->m_start.x()) {
            return first->m_start.x() < second->m_start.x();
        }
        return first->m_start.y() < second->m_start.y();
    }
    
    if (first->m_end != second->m_end) {
        if (first->m_end.x() != second->m_end.x()) {
            return first->m_end.x() < second->m_end.x();
        }
        return first->m_end.y() < second->m_end.y();
    }
    
    return first->m_width < second->m_width;
}

//=============================================================================
// UTILITY FUNCTIONS IMPLEMENTATION
//=============================================================================

namespace QtPcbTrackUtils {

double distanceBetweenTracks(const EDA_TRACK_DATA* track1, const EDA_TRACK_DATA* track2)
{
    QLineF line1(track1->getStart(), track1->getEnd());
    QLineF line2(track2->getStart(), track2->getEnd());
    
    // Calculate minimum distance between two line segments
    double minDist = std::numeric_limits<double>::max();
    
    // Check all endpoint combinations
    minDist = std::min(minDist, QLineF(track1->getStart(), track2->getStart()).length());
    minDist = std::min(minDist, QLineF(track1->getStart(), track2->getEnd()).length());
    minDist = std::min(minDist, QLineF(track1->getEnd(), track2->getStart()).length());
    minDist = std::min(minDist, QLineF(track1->getEnd(), track2->getEnd()).length());
    
    // Check point-to-line distances
    // This would require more complex calculations for accurate segment-to-segment distance
    
    return minDist;
}

bool areTracksConnected(const EDA_TRACK_DATA* track1, const EDA_TRACK_DATA* track2)
{
    const double tolerance = 0.001; // 1 nanometer tolerance
    
    return (QLineF(track1->getStart(), track2->getStart()).length() < tolerance ||
            QLineF(track1->getStart(), track2->getEnd()).length() < tolerance ||
            QLineF(track1->getEnd(), track2->getStart()).length() < tolerance ||
            QLineF(track1->getEnd(), track2->getEnd()).length() < tolerance);
}

std::optional<QPointF> findConnectionPoint(const EDA_TRACK_DATA* track1, const EDA_TRACK_DATA* track2)
{
    const double tolerance = 0.001;
    
    if (QLineF(track1->getStart(), track2->getStart()).length() < tolerance) {
        return track1->getStart();
    }
    if (QLineF(track1->getStart(), track2->getEnd()).length() < tolerance) {
        return track1->getStart();
    }
    if (QLineF(track1->getEnd(), track2->getStart()).length() < tolerance) {
        return track1->getEnd();
    }
    if (QLineF(track1->getEnd(), track2->getEnd()).length() < tolerance) {
        return track1->getEnd();
    }
    
    return std::nullopt;
}

bool formContinuousPath(const QList<EDA_TRACK_DATA*>& tracks)
{
    if (tracks.isEmpty()) {
        return true;
    }
    
    // Simple connectivity check - could be made more sophisticated
    for (int i = 0; i < tracks.size() - 1; ++i) {
        if (!areTracksConnected(tracks[i], tracks[i + 1])) {
            return false;
        }
    }
    
    return true;
}

EDA_TRACK_DATA* mergeCollinearTracks(const EDA_TRACK_DATA* track1, const EDA_TRACK_DATA* track2)
{
    if (!track1->approxCollinear(*track2)) {
        return nullptr;
    }
    
    if (track1->getWidth() != track2->getWidth() ||
        track1->getLayer() != track2->getLayer() ||
        track1->getNetCode() != track2->getNetCode()) {
        return nullptr;
    }
    
    // Find the extreme points
    QVector<QPointF> points = {
        track1->getStart(), track1->getEnd(),
        track2->getStart(), track2->getEnd()
    };
    
    double minX = std::numeric_limits<double>::max();
    double maxX = std::numeric_limits<double>::lowest();
    double minY = std::numeric_limits<double>::max();
    double maxY = std::numeric_limits<double>::lowest();
    
    for (const QPointF& p : points) {
        minX = std::min(minX, p.x());
        maxX = std::max(maxX, p.x());
        minY = std::min(minY, p.y());
        maxY = std::max(maxY, p.y());
    }
    
    // Create merged track
    EDA_TRACK_DATA* merged = new EDA_TRACK_DATA(track1->getParent());
    merged->setStart(QPointF(minX, minY));
    merged->setEnd(QPointF(maxX, maxY));
    merged->setWidth(track1->getWidth());
    merged->setLayer(track1->getLayer());
    merged->setNetCode(track1->getNetCode());
    
    return merged;
}

} // namespace QtPcbTrackUtils