/*
 * Qt-based reimplementation of KiCad PCB_VIA class
 * 
 * This class represents a PCB via (vertical interconnect access) using Qt frameworks.
 * Vias provide electrical connections between different PCB layers through plated holes.
 * Supports through, blind/buried, and microvia types with comprehensive layer management.
 */

#pragma once

#include "eda_track_data.h"
#include "eda_padstack_data.h"
#include <QtCore/QString>
#include <QtCore/QPointF>
#include <QtCore/QRectF>
#include <QtCore/QSharedPointer>
#include <QtCore/QLoggingCategory>
#include <QtCore/QMutex>
#include <QtCore/QVariantMap>
#include <QtCore/QHash>
#include <QtCore/QMap>

Q_DECLARE_LOGGING_CATEGORY(eDA_VIA_DATA)

// Forward declarations for dependencies not in migration scope
class QtShapeCircle;
class QtShapeCompound;
class QtLayerPair;
class QtZoneContainer;
class QtBoardDesignSettings;

// QtZoneLayerOverride is now defined in qt_temporary_implementations.h

/**
 * @brief Qt-based reimplementation of KiCad's PCB_VIA class
 * 
 * This class represents a PCB via with comprehensive layer management,
 * drill properties, padstack integration, and zone connection control.
 * Supports through, blind/buried, and microvia types.
 */
class EDA_VIA_DATA : public EDA_TRACK_DATA
{

public:
    //==========================================================================
    // CONSTRUCTION AND DESTRUCTION
    //==========================================================================
    explicit EDA_VIA_DATA(EDA_BOARD_OBJECT_DATA* parent = nullptr);
    EDA_VIA_DATA(const EDA_VIA_DATA& other);
    ~EDA_VIA_DATA() override;

    // Assignment operator
    EDA_VIA_DATA& operator=(const EDA_VIA_DATA& other);

    //==========================================================================
    // TYPE IDENTIFICATION
    //==========================================================================
    static bool classOf(const EDA_OBJECT_DATA* item);
    QString getClassName() const override { return QStringLiteral("EDA_VIA_DATA"); }
    
    bool isType(const QVector<QtKicadType>& scanTypes) const override;

    //==========================================================================
    // VIA TYPE MANAGEMENT
    //==========================================================================
    QtViaType getViaType() const { return m_viaType; }
    void setViaType(QtViaType type);
    
    bool hasValidLayerPair(int copperLayerCount) const;

    //==========================================================================
    // PADSTACK MANAGEMENT
    //==========================================================================
    const EDA_PADSTACK_DATA& getPadStack() const { return *m_padStack; }
    EDA_PADSTACK_DATA& getPadStack() { return *m_padStack; }
    void setPadStack(const EDA_PADSTACK_DATA& padstack);

    //==========================================================================
    // POSITION MANAGEMENT
    //==========================================================================
    QPointF getPosition() const override { return getStart(); }
    void setPosition(const QPointF& position) override;

    //==========================================================================
    // WIDTH MANAGEMENT (LAYER-SPECIFIC)
    //==========================================================================
    void setWidth(int width) override;
    int getWidth() const override;
    
    void setWidth(QtPcbLayerId layer, int width);
    int getWidth(QtPcbLayerId layer) const;
    
    // Properties panel helpers
    void setFrontWidth(int width) { setWidth(QtPcbLayerId::FCu, width); }
    int getFrontWidth() const { return getWidth(QtPcbLayerId::FCu); }

    //==========================================================================
    // DRILL PROPERTIES
    //==========================================================================
    void setDrill(int drill);
    int getDrill() const;
    int getDrillValue() const;
    void setDrillDefault();
    
    bool hasHole() const override { return true; }
    bool hasDrilledHole() const override;
    std::shared_ptr<QtShapeSegment> getEffectiveHoleShape() const override;

    //==========================================================================
    // LAYER MANAGEMENT
    //==========================================================================
    QtPcbLayerId getLayer() const;
    void setLayer(QtPcbLayerId layer) override;
    
    bool isOnLayer(QtPcbLayerId layer) const override;
    QtLayerSet getLayerSet() const override;
    void setLayerSet(const QtLayerSet& layers) override;
    
    void setLayerPair(QtPcbLayerId topLayer, QtPcbLayerId bottomLayer);
    void setTopLayer(QtPcbLayerId layer);
    void setBottomLayer(QtPcbLayerId layer);
    
    void getLayerPair(QtPcbLayerId* topLayer, QtPcbLayerId* bottomLayer) const;
    QtPcbLayerId getTopLayer() const;
    QtPcbLayerId getBottomLayer() const;
    
    void sanitizeLayers();

    //==========================================================================
    // TENTING MODE MANAGEMENT
    //==========================================================================
    void setFrontTentingMode(QtTentingMode mode);
    QtTentingMode getFrontTentingMode() const;
    void setBackTentingMode(QtTentingMode mode);
    QtTentingMode getBackTentingMode() const;
    
    bool isTented(QtPcbLayerId layer) const override;

    //==========================================================================
    // CONNECTION AND FLASHING
    //==========================================================================
    bool conditionallyFlashed(QtPcbLayerId layer) const;
    bool flashLayer(int layer) const;
    bool flashLayer(const QtLayerSet& layers) const;
    void getOutermostConnectedLayers(QtPcbLayerId* topmost, QtPcbLayerId* bottommost) const;

    //==========================================================================
    // FREE VIA MANAGEMENT
    //==========================================================================
    bool getIsFree() const { return m_isFree; }
    void setIsFree(bool free = true);

    //==========================================================================
    // ZONE LAYER OVERRIDES
    //==========================================================================
    void clearZoneLayerOverrides();
    //const QtZoneLayerOverride& getZoneLayerOverride(QtPcbLayerId layer) const;
    //void setZoneLayerOverride(QtPcbLayerId layer, const QtZoneLayerOverride& override);

    //==========================================================================
    // DEPRECATED COMPATIBILITY METHODS
    //==========================================================================
    void setRemoveUnconnected(bool set);
    bool getRemoveUnconnected() const;
    void setKeepStartEnd(bool set);
    bool getKeepStartEnd() const;

    //==========================================================================
    // CONSTRAINTS
    //==========================================================================
    QtMinOptMax<int> getWidthConstraint(QString* source = nullptr) const override;
    QtMinOptMax<int> getDrillConstraint(QString* source = nullptr) const;
    int getMinAnnulus(QtPcbLayerId layer, QString* source = nullptr) const;

    //==========================================================================
    // GEOMETRIC CALCULATIONS
    //==========================================================================
    QRectF getBoundingBox() const override;
    int getSolderMaskExpansion() const;

    //==========================================================================
    // SHAPE GENERATION
    //==========================================================================
    std::shared_ptr<QtShape> getEffectiveShape(QtPcbLayerId layer = QtPcbLayerId::UndefinedLayer,
                                             QtFlashing flash = QtFlashing::Default) const override;

    //==========================================================================
    // HIT TESTING
    //==========================================================================
    bool hitTest(const QPointF& position, double accuracy = 0.0) const override;
    bool hitTest(const QRectF& rect, bool contained = false, double accuracy = 0.0) const override;

    //==========================================================================
    // UI AND DESCRIPTION
    //==========================================================================
    void getMsgPanelInfo(EDA_BOARD_DATA* frame, QVector<QtMsgPanelItem>& list) const override;
    QString getItemDescription(QtUnitsProvider* unitsProvider = nullptr, bool includeType = false) const override;
    QString getClass() const override { return "VIA"; }
    QtBitmaps getMenuImage() const override;

    //==========================================================================
    // VIEWING
    //==========================================================================
    void viewGetLayers(QVector<int>& layers) const override;
    double viewGetLOD(int layer, const QtView* view) const override;

    //==========================================================================
    // TRANSFORMATIONS
    //==========================================================================
    void flip(const QPointF& center, QtFlipDirection flipDirection) override;

    //==========================================================================
    // COMPARISON AND DUPLICATION
    //==========================================================================
    EDA_BOARD_OBJECT_DATA* clone() const override;
    double similarity(const EDA_BOARD_OBJECT_DATA& other) const override;
    bool operator==(const EDA_BOARD_OBJECT_DATA& other) const override;
    bool operator==(const EDA_VIA_DATA& other) const;

    //==========================================================================
    // QT SERIALIZATION
    //==========================================================================
    QVariantMap toVariantMap() const override;
    void fromVariantMap(const QVariantMap& map) override;


protected:
    //==========================================================================
    // PROTECTED HELPER METHODS
    //==========================================================================
    void swapData(EDA_BOARD_OBJECT_DATA* other) override;
    QString layerMaskDescribe() const;
    
    // Shape caching
    void clearShapeCache() const;
    void buildEffectiveShapeForLayer(QtPcbLayerId layer) const;

private:
    //==========================================================================
    // PRIVATE DATA MEMBERS
    //==========================================================================
    QtViaType m_viaType = QtViaType::Through;    ///< Through, blind/buried or micro
    QSharedPointer<EDA_PADSTACK_DATA> m_padStack;       ///< Layer-specific properties
    bool m_isFree = false;                        ///< Free vias don't auto-update nets
    
    // Zone layer overrides with thread safety
    mutable QMutex m_zoneLayerOverridesMutex;
    QMap<QtPcbLayerId, QtZoneLayerOverride> m_zoneLayerOverrides;
    
    // Shape caching per layer
    mutable QMutex m_shapeCacheMutex;
    mutable QMap<QtPcbLayerId, std::shared_ptr<QtShape>> m_shapeCache;
    mutable QSet<QtPcbLayerId> m_shapeDirtyLayers;
    
    // Static empty override for const reference return
    //static const QtZoneLayerOverride s_emptyOverride;
};

//=============================================================================
// UTILITY FUNCTIONS AND TYPE ALIASES
//=============================================================================

// Container type aliases
using QtVias = QList<EDA_VIA_DATA*>;
using QtViaList = QVector<EDA_VIA_DATA*>;

// Hash support
//inline uint qHash(const QtZoneLayerOverride& override, uint seed = 0) {
//    return qHash(static_cast<int>(override.mode), seed);
//}

// Utility namespace for via operations
namespace QtViaUtils {
    /**
     * @brief Calculate optimal via type based on layer pair
     */
    QtViaType calculateViaType(QtPcbLayerId topLayer, QtPcbLayerId bottomLayer, int copperLayerCount);
    
    /**
     * @brief Check if via connects two adjacent layers (for microvia)
     */
    bool isAdjacentLayerPair(QtPcbLayerId layer1, QtPcbLayerId layer2);
    
    /**
     * @brief Get standard drill size for via type
     */
    int getStandardDrillSize(QtViaType type, const QtBoardDesignSettings* settings);
    
    /**
     * @brief Calculate annular ring size for via
     */
    int calculateAnnularRing(const EDA_VIA_DATA* via, QtPcbLayerId layer);
    
    /**
     * @brief Check if via spans all copper layers
     */
    bool isThruVia(const EDA_VIA_DATA* via, int copperLayerCount);
    
    /**
     * @brief Group vias by their connection properties
     */
    QMap<QString, QtViaList> groupViasByConnection(const QtViaList& vias);
    
    /**
     * @brief Find vias at a specific position
     */
    QtViaList findViasAtPosition(const QtViaList& vias, const QPointF& position, double tolerance);
}

