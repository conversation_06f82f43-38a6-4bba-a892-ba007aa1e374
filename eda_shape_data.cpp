/*
 * Qt-based reimplementation of KiCad EDA_SHAPE class implementation
 * 
 * This implementation provides comprehensive geometric shape functionality
 * using Qt frameworks for better performance, type safety, and maintainability.
 */

#include "eda_shape_data.h"
#include <QtCore/QLoggingCategory>
#include <QtCore/QJsonObject>
#include <QtCore/QJsonDocument>
#include <QtCore/QDebug>
#include <QtCore/qmath.h>
#include <QtGui/QPainterPath>
#include <QtGui/QTransform>
#include <algorithm>
#include <cmath>

Q_LOGGING_CATEGORY(qtEdaShape, "qt.eda.shape")

// Mathematical constants and utilities
namespace {
    constexpr double PI = M_PI;
    constexpr double DEG_TO_RAD = PI / 180.0;
    constexpr double RAD_TO_DEG = 180.0 / PI;
    constexpr double EPSILON = 1e-9;
    
    // Qt-optimized angle normalization
    inline double normalizeAngle(double angle) {
        while (angle < 0.0) angle += 360.0;
        while (angle >= 360.0) angle -= 360.0;
        return angle;
    }
    
    inline double normalizeAngleRadians(double angle) {
        while (angle < 0.0) angle += 2.0 * PI;
        while (angle >= 2.0 * PI) angle -= 2.0 * PI;
        return angle;
    }
    
    // Distance calculation using Qt's QPointF
    inline double distance(const QPointF& p1, const QPointF& p2) {
        QPointF delta = p2 - p1;
        return qSqrt(delta.x() * delta.x() + delta.y() * delta.y());
    }
    
    // Angle between two points
    inline double angleBetweenPoints(const QPointF& p1, const QPointF& p2) {
        QPointF delta = p2 - p1;
        return qAtan2(delta.y(), delta.x()) * RAD_TO_DEG;
    }
}



//=============================================================================
// CONSTRUCTION AND DESTRUCTION
//=============================================================================

EDA_SHAPE_DATA::EDA_SHAPE_DATA(QtShapeType shapeType, int lineWidth, QtFillType fillType)
    : m_shapeType(shapeType)
    , m_endsSwapped(false)
    , m_stroke(lineWidth, QtLineStyle::Default, QColor())
    , m_fillMode(fillType)
    , m_fillColor(QColor())
    , m_startPoint(0.0, 0.0)
    , m_endPoint(0.0, 0.0)
    , m_arcCenter(0.0, 0.0)
    , m_bezierC1(0.0, 0.0)
    , m_bezierC2(0.0, 0.0)
    , m_rectangleWidth(0.0)
    , m_rectangleHeight(0.0)
    , m_segmentLength(0.0)
    , m_segmentAngle(0.0)
    , m_editState(0)
    , m_proxyItem(false)
    , m_boundingRectValid(false)
{
    // qCDebug(qtEdaShape) << "Created EDA_SHAPE_DATA of type" << static_cast<int>(shapeType);
}

EDA_SHAPE_DATA::EDA_SHAPE_DATA(const QtGeometry::Shape& shape)
    : m_endsSwapped(false)
    , m_stroke(0, QtLineStyle::Default, QColor())
    , m_fillMode(QtFillType::NoFill)
    , m_fillColor(QColor())
    , m_rectangleWidth(0.0)
    , m_rectangleHeight(0.0)
    , m_segmentLength(0.0)
    , m_segmentAngle(0.0)
    , m_editState(0)
    , m_proxyItem(false)
    , m_boundingRectValid(false)
{
    // This would be implemented based on the actual Qt geometry shape system
    // For now, default to segment
    m_shapeType = QtShapeType::Segment;
}

EDA_SHAPE_DATA::EDA_SHAPE_DATA(const EDA_SHAPE_DATA& other)
    : m_shapeType(other.m_shapeType)
    , m_endsSwapped(other.m_endsSwapped)
    , m_stroke(other.m_stroke)
    , m_fillMode(other.m_fillMode)
    , m_fillColor(other.m_fillColor)
    , m_startPoint(other.m_startPoint)
    , m_endPoint(other.m_endPoint)
    , m_arcCenter(other.m_arcCenter)
    , m_arcMidData(other.m_arcMidData)
    , m_bezierC1(other.m_bezierC1)
    , m_bezierC2(other.m_bezierC2)
    , m_bezierPoints(other.m_bezierPoints)
    , m_polygon(other.m_polygon)
    , m_rectangleWidth(other.m_rectangleWidth)
    , m_rectangleHeight(other.m_rectangleHeight)
    , m_segmentLength(other.m_segmentLength)
    , m_segmentAngle(other.m_segmentAngle)
    , m_editState(other.m_editState)
    , m_proxyItem(other.m_proxyItem)
    , m_boundingRectValid(false)  // Force recalculation
{
}

EDA_SHAPE_DATA::~EDA_SHAPE_DATA()
{
}

//=============================================================================
// SHAPE TYPE AND BASIC PROPERTIES
//=============================================================================

void EDA_SHAPE_DATA::setShapeType(QtShapeType type)
{
    if (m_shapeType != type) {
        m_shapeType = type;
        invalidateCache();
        
        
    }
}

QString EDA_SHAPE_DATA::getShapeTypeName() const
{
    return shapeTypeToString(m_shapeType);
}

QString EDA_SHAPE_DATA::getFriendlyName() const
{
    switch (m_shapeType) {
        case QtShapeType::Segment:    return QStringLiteral("Line Segment");
        case QtShapeType::Rectangle:  return QStringLiteral("Rectangle");
        case QtShapeType::Arc:        return QStringLiteral("Arc");
        case QtShapeType::Circle:     return QStringLiteral("Circle");
        case QtShapeType::Polygon:    return QStringLiteral("Polygon");
        case QtShapeType::Bezier:     return QStringLiteral("Bezier Curve");
        default:                      return QStringLiteral("Unknown Shape");
    }
}

//=============================================================================
// GEOMETRIC PROPERTIES - POINTS AND DIMENSIONS
//=============================================================================

void EDA_SHAPE_DATA::setStartPoint(const QPointF& point)
{
    if (m_startPoint != point) {
        m_startPoint = point;
        m_endsSwapped = false;
        updateDerivedProperties();
        
    }
}

void EDA_SHAPE_DATA::setEndPoint(const QPointF& point)
{
    if (m_endPoint != point) {
        m_endPoint = point;
        m_endsSwapped = false;
        updateDerivedProperties();
        
    }
}

void EDA_SHAPE_DATA::setStartX(double x)
{
    if (!qFuzzyCompare(m_startPoint.x(), x)) {
        m_startPoint.setX(x);
        m_endsSwapped = false;
        updateDerivedProperties();
        
    }
}

void EDA_SHAPE_DATA::setStartY(double y)
{
    if (!qFuzzyCompare(m_startPoint.y(), y)) {
        m_startPoint.setY(y);
        m_endsSwapped = false;
        updateDerivedProperties();
        
    }
}

void EDA_SHAPE_DATA::setEndX(double x)
{
    if (!qFuzzyCompare(m_endPoint.x(), x)) {
        m_endPoint.setX(x);
        m_endsSwapped = false;
        updateDerivedProperties();
        
    }
}

void EDA_SHAPE_DATA::setEndY(double y)
{
    if (!qFuzzyCompare(m_endPoint.y(), y)) {
        m_endPoint.setY(y);
        m_endsSwapped = false;
        updateDerivedProperties();
        
    }
}

QPointF EDA_SHAPE_DATA::getCenter() const
{
    switch (m_shapeType) {
        case QtShapeType::Circle:
            return m_startPoint;
        case QtShapeType::Arc:
            return m_arcCenter;
        case QtShapeType::Rectangle:
            return (m_startPoint + m_endPoint) * 0.5;
        default:
            return (m_startPoint + m_endPoint) * 0.5;
    }
}

void EDA_SHAPE_DATA::setCenter(const QPointF& center)
{
    switch (m_shapeType) {
        case QtShapeType::Circle:
            if (m_startPoint != center) {
                QPointF offset = center - m_startPoint;
                m_startPoint = center;
                m_endPoint += offset;
                updateDerivedProperties();
                
            }
            break;
        case QtShapeType::Arc:
            if (m_arcCenter != center) {
                m_arcCenter = center;
                updateDerivedProperties();
                
            }
            break;
        case QtShapeType::Rectangle: {
            QPointF currentCenter = getCenter();
            if (currentCenter != center) {
                QPointF offset = center - currentCenter;
                m_startPoint += offset;
                m_endPoint += offset;
                updateDerivedProperties();
                
            }
            break;
        }
        default:
            // For other shapes, move both points to maintain relative positions
            QPointF currentCenter = getCenter();
            QPointF offset = center - currentCenter;
            m_startPoint += offset;
            m_endPoint += offset;
            updateDerivedProperties();
            
            break;
    }
}

void EDA_SHAPE_DATA::setCenterX(double x)
{
    QPointF center = getCenter();
    center.setX(x);
    setCenter(center);
}

void EDA_SHAPE_DATA::setCenterY(double y)
{
    QPointF center = getCenter();
    center.setY(y);
    setCenter(center);
}

void EDA_SHAPE_DATA::setRadius(double radius)
{
    if (m_shapeType == QtShapeType::Circle) {
        QPointF direction = (m_endPoint - m_startPoint);
        double currentRadius = qSqrt(direction.x() * direction.x() + direction.y() * direction.y());
        
        if (!qFuzzyCompare(currentRadius, radius)) {
            if (currentRadius > EPSILON) {
                direction /= currentRadius;  // Normalize
            } else {
                direction = QPointF(1.0, 0.0);  // Default direction
            }
            
            m_endPoint = m_startPoint + direction * radius;
            updateDerivedProperties();
            
        }
    }
}

double EDA_SHAPE_DATA::getRadius() const
{
    if (m_shapeType == QtShapeType::Circle) {
        return distance(m_startPoint, m_endPoint);
    }
    return 0.0;
}

void EDA_SHAPE_DATA::setRectangleSize(const QSizeF& size)
{
    if (m_shapeType == QtShapeType::Rectangle) {
        QPointF topLeft = m_startPoint;
        m_endPoint = topLeft + QPointF(size.width(), size.height());
        m_rectangleWidth = size.width();
        m_rectangleHeight = size.height();
        updateDerivedProperties();
        
    }
}

void EDA_SHAPE_DATA::setRectangleWidth(double width)
{
    if (m_shapeType == QtShapeType::Rectangle && !qFuzzyCompare(m_rectangleWidth, width)) {
        m_rectangleWidth = width;
        m_endPoint.setX(m_startPoint.x() + width);
        updateDerivedProperties();
        
    }
}

void EDA_SHAPE_DATA::setRectangleHeight(double height)
{
    if (m_shapeType == QtShapeType::Rectangle && !qFuzzyCompare(m_rectangleHeight, height)) {
        m_rectangleHeight = height;
        m_endPoint.setY(m_startPoint.y() + height);
        updateDerivedProperties();
        
    }
}

QSizeF EDA_SHAPE_DATA::getRectangleSize() const
{
    if (m_shapeType == QtShapeType::Rectangle) {
        return QSizeF(qAbs(m_endPoint.x() - m_startPoint.x()), 
                      qAbs(m_endPoint.y() - m_startPoint.y()));
    }
    return QSizeF();
}

double EDA_SHAPE_DATA::getRectangleWidth() const
{
    if (m_shapeType == QtShapeType::Rectangle) {
        return qAbs(m_endPoint.x() - m_startPoint.x());
    }
    return 0.0;
}

double EDA_SHAPE_DATA::getRectangleHeight() const
{
    if (m_shapeType == QtShapeType::Rectangle) {
        return qAbs(m_endPoint.y() - m_startPoint.y());
    }
    return 0.0;
}

QPointF EDA_SHAPE_DATA::getPosition() const
{
    switch (m_shapeType) {
        case QtShapeType::Circle:
        case QtShapeType::Arc:
            return getCenter();
        default:
            return m_startPoint;
    }
}

void EDA_SHAPE_DATA::setPosition(const QPointF& position)
{
    QPointF currentPos = getPosition();
    QPointF offset = position - currentPos;
    
    m_startPoint += offset;
    m_endPoint += offset;
    
    if (m_shapeType == QtShapeType::Arc) {
        m_arcCenter += offset;
    }
    
    // Move polygon points if applicable
    if (m_shapeType == QtShapeType::Polygon) {
        m_polygon.translate(offset);
    }
    
    // Move bezier control points
    m_bezierC1 += offset;
    m_bezierC2 += offset;
    
    updateDerivedProperties();
    
}

QRectF EDA_SHAPE_DATA::getBoundingRect() const
{
    ensureBoundingRectValid();
    return m_cachedBoundingRect;
}

QVector<QPointF> EDA_SHAPE_DATA::getRectCorners() const
{
    if (m_shapeType == QtShapeType::Rectangle) {
        QVector<QPointF> corners;
        corners.reserve(4);
        
        double left = qMin(m_startPoint.x(), m_endPoint.x());
        double right = qMax(m_startPoint.x(), m_endPoint.x());
        double top = qMin(m_startPoint.y(), m_endPoint.y());
        double bottom = qMax(m_startPoint.y(), m_endPoint.y());
        
        corners << QPointF(left, top);
        corners << QPointF(right, top);
        corners << QPointF(right, bottom);
        corners << QPointF(left, bottom);
        
        return corners;
    }
    return QVector<QPointF>();
}

//=============================================================================
// STROKE PROPERTIES AND OPERATIONS
//=============================================================================

void EDA_SHAPE_DATA::setLineWidth(int width)
{
    if (m_stroke.width != width) {
        m_stroke.width = width;
        invalidateCache();
        
        
    }
}

void EDA_SHAPE_DATA::setLineStyle(QtLineStyle style)
{
    if (m_stroke.style != style) {
        m_stroke.style = style;
        
        
    }
}

void EDA_SHAPE_DATA::setLineColor(const QColor& color)
{
    if (m_stroke.color != color) {
        m_stroke.color = color;
        
        
    }
}

void EDA_SHAPE_DATA::setStroke(const QtStrokeParams& stroke)
{
    if (m_stroke != stroke) {
        m_stroke = stroke;
        invalidateCache();
        
        
    }
}

//=============================================================================
// FILL PROPERTIES AND OPERATIONS
//=============================================================================

void EDA_SHAPE_DATA::setFillMode(QtFillType fillMode)
{
    if (m_fillMode != fillMode) {
        m_fillMode = fillMode;
        
        
    }
}

void EDA_SHAPE_DATA::setFillColor(const QColor& color)
{
    if (m_fillColor != color) {
        m_fillColor = color;
        
        
    }
}

void EDA_SHAPE_DATA::setFilled(bool filled)
{
    QtFillType newFillMode = filled ? QtFillType::FilledShape : QtFillType::NoFill;
    setFillMode(newFillMode);
}

bool EDA_SHAPE_DATA::isClosed() const
{
    switch (m_shapeType) {
        case QtShapeType::Rectangle:
        case QtShapeType::Circle:
        case QtShapeType::Polygon:
            return true;
        case QtShapeType::Arc:
            return false;  // Arcs are not closed unless they're full circles
        case QtShapeType::Segment:
        case QtShapeType::Bezier:
        default:
            return false;
    }
}

//=============================================================================
// ARC-SPECIFIC OPERATIONS
//=============================================================================

void EDA_SHAPE_DATA::setArcGeometry(const QPointF& start, const QPointF& mid, const QPointF& end)
{
    if (m_shapeType != QtShapeType::Arc) {
        qCWarning(qtEdaShape) << "setArcGeometry called on non-arc shape";
        return;
    }
    
    m_startPoint = start;
    m_endPoint = end;
    
    // Calculate arc center from three points
    // Using the perpendicular bisector method
    QPointF midStart = (start + mid) * 0.5;
    QPointF midEnd = (mid + end) * 0.5;
    
    QPointF deltaStart = mid - start;
    QPointF deltaEnd = end - mid;
    
    // Perpendicular vectors
    QPointF perpStart(-deltaStart.y(), deltaStart.x());
    QPointF perpEnd(-deltaEnd.y(), deltaEnd.x());
    
    // Find intersection of perpendicular bisectors
    double det = perpStart.x() * perpEnd.y() - perpStart.y() * perpEnd.x();
    
    if (qAbs(det) > EPSILON) {
        QPointF diff = midEnd - midStart;
        double t = (diff.x() * perpEnd.y() - diff.y() * perpEnd.x()) / det;
        m_arcCenter = midStart + perpStart * t;
    } else {
        // Fallback: use midpoint between start and end
        m_arcCenter = (start + end) * 0.5;
    }
    
    // Store midpoint data for VCS consistency
    m_arcMidData = QtArcMidData(mid, start, end, m_arcCenter);
    
    updateDerivedProperties();
    
}

void EDA_SHAPE_DATA::setArcAngleAndEnd(double angleDegrees, bool checkNegativeAngle)
{
    if (m_shapeType != QtShapeType::Arc) {
        qCWarning(qtEdaShape) << "setArcAngleAndEnd called on non-arc shape";
        return;
    }
    
    if (checkNegativeAngle && angleDegrees < 0) {
        angleDegrees = -angleDegrees;
        std::swap(m_startPoint, m_endPoint);
        m_endsSwapped = !m_endsSwapped;
    }
    
    double angleRad = angleDegrees * DEG_TO_RAD;
    double radius = distance(m_arcCenter, m_startPoint);
    
    QPointF startVector = m_startPoint - m_arcCenter;
    double startAngle = qAtan2(startVector.y(), startVector.x());
    double endAngle = startAngle + angleRad;
    
    m_endPoint = m_arcCenter + QPointF(radius * qCos(endAngle), radius * qSin(endAngle));
    
    updateDerivedProperties();
    
}

double EDA_SHAPE_DATA::getArcAngle() const
{
    if (m_shapeType != QtShapeType::Arc) {
        return 0.0;
    }
    
    QPointF startVector = m_startPoint - m_arcCenter;
    QPointF endVector = m_endPoint - m_arcCenter;
    
    double startAngle = qAtan2(startVector.y(), startVector.x());
    double endAngle = qAtan2(endVector.y(), endVector.x());
    
    double angle = endAngle - startAngle;
    
    // Normalize to [0, 360) degrees
    if (angle < 0) {
        angle += 2.0 * PI;
    }
    
    return angle * RAD_TO_DEG;
}

QPointF EDA_SHAPE_DATA::getArcMid() const
{
    if (m_shapeType != QtShapeType::Arc) {
        return QPointF();
    }
    
    // Use cached midpoint if available
    if (!m_arcMidData.mid.isNull()) {
        return m_arcMidData.mid;
    }
    
    // Calculate midpoint
    QPointF startVector = m_startPoint - m_arcCenter;
    QPointF endVector = m_endPoint - m_arcCenter;
    
    double startAngle = qAtan2(startVector.y(), startVector.x());
    double endAngle = qAtan2(endVector.y(), endVector.x());
    
    // Handle angle wrapping
    if (endAngle < startAngle) {
        endAngle += 2.0 * PI;
    }
    
    double midAngle = (startAngle + endAngle) * 0.5;
    double radius = distance(m_arcCenter, m_startPoint);
    
    return m_arcCenter + QPointF(radius * qCos(midAngle), radius * qSin(midAngle));
}

bool EDA_SHAPE_DATA::isClockwiseArc() const
{
    if (m_shapeType != QtShapeType::Arc) {
        return false;
    }
    
    QPointF startVector = m_startPoint - m_arcCenter;
    QPointF endVector = m_endPoint - m_arcCenter;
    
    // Cross product determines direction
    double crossProduct = startVector.x() * endVector.y() - startVector.y() * endVector.x();
    
    return crossProduct < 0;  // Negative cross product means clockwise
}

void EDA_SHAPE_DATA::calcArcAngles(double& startAngle, double& endAngle) const
{
    if (m_shapeType != QtShapeType::Arc) {
        startAngle = endAngle = 0.0;
        return;
    }
    
    QPointF startVector = m_startPoint - m_arcCenter;
    QPointF endVector = m_endPoint - m_arcCenter;
    
    startAngle = qAtan2(startVector.y(), startVector.x()) * RAD_TO_DEG;
    endAngle = qAtan2(endVector.y(), endVector.x()) * RAD_TO_DEG;
    
    // Normalize angles to [0, 360)
    startAngle = normalizeAngle(startAngle);
    endAngle = normalizeAngle(endAngle);
    
    // Ensure endAngle > startAngle
    if (endAngle <= startAngle) {
        endAngle += 360.0;
    }
}

void EDA_SHAPE_DATA::setCachedArcData(const QPointF& start, const QPointF& mid, 
                                  const QPointF& end, const QPointF& center)
{
    m_arcMidData = QtArcMidData(mid, start, end, center);
}

//=============================================================================
// BEZIER CURVE OPERATIONS
//=============================================================================

void EDA_SHAPE_DATA::rebuildBezierToSegments(double maxError)
{
    if (m_shapeType != QtShapeType::Bezier) {
        return;
    }
    
    m_bezierPoints = buildBezierToSegments(maxError);
    invalidateCache();
    
}

//=============================================================================
// POLYGON OPERATIONS
//=============================================================================

void EDA_SHAPE_DATA::setPolygonPoints(const QVector<QPointF>& points)
{
    if (m_shapeType == QtShapeType::Polygon) {
        m_polygon = QPolygonF(points);
        invalidateCache();
        
    }
}

void EDA_SHAPE_DATA::setPolygonPoints(const QPolygonF& polygon)
{
    if (m_shapeType == QtShapeType::Polygon) {
        m_polygon = polygon;
        invalidateCache();
        
    }
}

void EDA_SHAPE_DATA::copyPolygonPoints(QVector<QPointF>& buffer) const
{
    if (m_shapeType == QtShapeType::Polygon) {
        buffer.clear();
        buffer.reserve(m_polygon.size());
        for (const QPointF& point : m_polygon) {
            buffer.append(point);
        }
    }
}

//=============================================================================
// GEOMETRIC CALCULATIONS AND MEASUREMENTS
//=============================================================================

double EDA_SHAPE_DATA::getLength() const
{
    switch (m_shapeType) {
        case QtShapeType::Segment:
            return distance(m_startPoint, m_endPoint);
            
        case QtShapeType::Arc: {
            double radius = distance(m_arcCenter, m_startPoint);
            double angle = getArcAngle() * DEG_TO_RAD;
            return radius * angle;
        }
        
        case QtShapeType::Circle: {
            double radius = distance(m_startPoint, m_endPoint);
            return 2.0 * PI * radius;
        }
        
        case QtShapeType::Rectangle: {
            double width = getRectangleWidth();
            double height = getRectangleHeight();
            return 2.0 * (width + height);
        }
        
        case QtShapeType::Polygon: {
            if (m_polygon.size() < 2) return 0.0;
            
            double totalLength = 0.0;
            for (int i = 0; i < m_polygon.size(); ++i) {
                int nextIndex = (i + 1) % m_polygon.size();
                totalLength += distance(m_polygon[i], m_polygon[nextIndex]);
            }
            return totalLength;
        }
        
        case QtShapeType::Bezier: {
            if (m_bezierPoints.size() < 2) return 0.0;
            
            double totalLength = 0.0;
            for (int i = 0; i < m_bezierPoints.size() - 1; ++i) {
                totalLength += distance(m_bezierPoints[i], m_bezierPoints[i + 1]);
            }
            return totalLength;
        }
        
        default:
            return 0.0;
    }
}

double EDA_SHAPE_DATA::getSegmentAngle() const
{
    if (m_shapeType == QtShapeType::Segment) {
        return angleBetweenPoints(m_startPoint, m_endPoint);
    }
    return 0.0;
}

void EDA_SHAPE_DATA::setSegmentAngle(double angleDegrees)
{
    if (m_shapeType == QtShapeType::Segment) {
        double length = distance(m_startPoint, m_endPoint);
        double angleRad = angleDegrees * DEG_TO_RAD;
        
        m_endPoint = m_startPoint + QPointF(length * qCos(angleRad), length * qSin(angleRad));
        m_segmentAngle = angleDegrees;
        
        updateDerivedProperties();
        
    }
}

void EDA_SHAPE_DATA::setLength(double length)
{
    switch (m_shapeType) {
        case QtShapeType::Segment: {
            QPointF direction = m_endPoint - m_startPoint;
            double currentLength = qSqrt(direction.x() * direction.x() + direction.y() * direction.y());
            
            if (currentLength > EPSILON) {
                direction /= currentLength;  // Normalize
                m_endPoint = m_startPoint + direction * length;
                m_segmentLength = length;
                updateDerivedProperties();
                
            }
            break;
        }
        
        case QtShapeType::Circle: {
            double circumference = length;
            double radius = circumference / (2.0 * PI);
            setRadius(radius);
            break;
        }
        
        default:
            qCWarning(qtEdaShape) << "setLength not supported for shape type" << static_cast<int>(m_shapeType);
            break;
    }
}

double EDA_SHAPE_DATA::getAngleDegrees() const
{
    switch (m_shapeType) {
        case QtShapeType::Segment:
            return getSegmentAngle();
        case QtShapeType::Arc:
            return getArcAngle();
        default:
            return 0.0;
    }
}

double EDA_SHAPE_DATA::getAngleRadians() const
{
    return getAngleDegrees() * DEG_TO_RAD;
}

//=============================================================================
// SHAPE TRANSFORMATIONS
//=============================================================================

void EDA_SHAPE_DATA::move(const QPointF& moveVector)
{
    m_startPoint += moveVector;
    m_endPoint += moveVector;
    
    if (m_shapeType == QtShapeType::Arc) {
        m_arcCenter += moveVector;
        m_arcMidData.start += moveVector;
        m_arcMidData.mid += moveVector;
        m_arcMidData.end += moveVector;
        m_arcMidData.center += moveVector;
    }
    
    if (m_shapeType == QtShapeType::Polygon) {
        m_polygon.translate(moveVector);
    }
    
    if (m_shapeType == QtShapeType::Bezier) {
        m_bezierC1 += moveVector;
        m_bezierC2 += moveVector;
        for (QPointF& point : m_bezierPoints) {
            point += moveVector;
        }
    }
    
    invalidateCache();
    
}

void EDA_SHAPE_DATA::rotate(const QPointF& rotCenter, double angleDegrees)
{
    QTransform transform;
    transform.translate(rotCenter.x(), rotCenter.y());
    transform.rotate(angleDegrees);
    transform.translate(-rotCenter.x(), -rotCenter.y());
    
    m_startPoint = transform.map(m_startPoint);
    m_endPoint = transform.map(m_endPoint);
    
    if (m_shapeType == QtShapeType::Arc) {
        m_arcCenter = transform.map(m_arcCenter);
        m_arcMidData.start = transform.map(m_arcMidData.start);
        m_arcMidData.mid = transform.map(m_arcMidData.mid);
        m_arcMidData.end = transform.map(m_arcMidData.end);
        m_arcMidData.center = transform.map(m_arcMidData.center);
    }
    
    if (m_shapeType == QtShapeType::Polygon) {
        for (int i = 0; i < m_polygon.size(); ++i) {
            m_polygon[i] = transform.map(m_polygon[i]);
        }
    }
    
    if (m_shapeType == QtShapeType::Bezier) {
        m_bezierC1 = transform.map(m_bezierC1);
        m_bezierC2 = transform.map(m_bezierC2);
        for (QPointF& point : m_bezierPoints) {
            point = transform.map(point);
        }
    }
    
    invalidateCache();
    
}

void EDA_SHAPE_DATA::flip(const QPointF& center, bool horizontal)
{
    QTransform transform;
    transform.translate(center.x(), center.y());
    
    if (horizontal) {
        transform.scale(-1.0, 1.0);
    } else {
        transform.scale(1.0, -1.0);
    }
    
    transform.translate(-center.x(), -center.y());
    
    m_startPoint = transform.map(m_startPoint);
    m_endPoint = transform.map(m_endPoint);
    
    if (m_shapeType == QtShapeType::Arc) {
        m_arcCenter = transform.map(m_arcCenter);
        // Flipping an arc reverses its direction
        std::swap(m_startPoint, m_endPoint);
        m_endsSwapped = !m_endsSwapped;
    }
    
    if (m_shapeType == QtShapeType::Polygon) {
        for (int i = 0; i < m_polygon.size(); ++i) {
            m_polygon[i] = transform.map(m_polygon[i]);
        }
    }
    
    if (m_shapeType == QtShapeType::Bezier) {
        m_bezierC1 = transform.map(m_bezierC1);
        m_bezierC2 = transform.map(m_bezierC2);
        for (QPointF& point : m_bezierPoints) {
            point = transform.map(point);
        }
    }
    
    invalidateCache();
    
}

void EDA_SHAPE_DATA::scale(double scaleFactor)
{
    scale(scaleFactor, scaleFactor);
}

void EDA_SHAPE_DATA::scale(double scaleX, double scaleY)
{
    QTransform transform;
    transform.scale(scaleX, scaleY);
    
    m_startPoint = transform.map(m_startPoint);
    m_endPoint = transform.map(m_endPoint);
    
    if (m_shapeType == QtShapeType::Arc) {
        m_arcCenter = transform.map(m_arcCenter);
    }
    
    if (m_shapeType == QtShapeType::Polygon) {
        for (int i = 0; i < m_polygon.size(); ++i) {
            m_polygon[i] = transform.map(m_polygon[i]);
        }
    }
    
    if (m_shapeType == QtShapeType::Bezier) {
        m_bezierC1 = transform.map(m_bezierC1);
        m_bezierC2 = transform.map(m_bezierC2);
        for (QPointF& point : m_bezierPoints) {
            point = transform.map(point);
        }
    }
    
    // Scale line width
    m_stroke.width = static_cast<int>(m_stroke.width * qMax(qAbs(scaleX), qAbs(scaleY)));
    
    invalidateCache();
    
}

//=============================================================================
// HIT TESTING AND COLLISION DETECTION
//=============================================================================

bool EDA_SHAPE_DATA::hitTest(const QPointF& position, double accuracy) const
{
    QRectF testRect(position.x() - accuracy, position.y() - accuracy, 
                    2.0 * accuracy, 2.0 * accuracy);
    
    switch (m_shapeType) {
        case QtShapeType::Segment: {
            // Line segment hit test
            QPointF delta = m_endPoint - m_startPoint;
            double length = qSqrt(delta.x() * delta.x() + delta.y() * delta.y());
            
            if (length < EPSILON) {
                return distance(position, m_startPoint) <= accuracy;
            }
            
            // Project point onto line
            QPointF toPoint = position - m_startPoint;
            double t = (toPoint.x() * delta.x() + toPoint.y() * delta.y()) / (length * length);
            
            if (t < 0.0) {
                return distance(position, m_startPoint) <= accuracy;
            } else if (t > 1.0) {
                return distance(position, m_endPoint) <= accuracy;
            } else {
                QPointF projection = m_startPoint + delta * t;
                return distance(position, projection) <= accuracy;
            }
        }
        
        case QtShapeType::Rectangle: {
            QRectF rect = getBoundingRect();
            if (isFilled()) {
                return rect.contains(position);
            } else {
                // Check if point is on the border
                return rect.adjusted(-accuracy, -accuracy, accuracy, accuracy).contains(position) &&
                       !rect.adjusted(accuracy, accuracy, -accuracy, -accuracy).contains(position);
            }
        }
        
        case QtShapeType::Circle: {
            double radius = getRadius();
            double dist = distance(position, m_startPoint);
            
            if (isFilled()) {
                return dist <= radius + accuracy;
            } else {
                return qAbs(dist - radius) <= accuracy;
            }
        }
        
        case QtShapeType::Arc: {
            double radius = distance(m_arcCenter, m_startPoint);
            double dist = distance(position, m_arcCenter);
            
            // Check if point is at correct distance from center
            if (qAbs(dist - radius) > accuracy) {
                return false;
            }
            
            // Check if point is within arc angle range
            QPointF pointVector = position - m_arcCenter;
            double pointAngle = qAtan2(pointVector.y(), pointVector.x());
            
            double startAngle, endAngle;
            calcArcAngles(startAngle, endAngle);
            
            double pointAngleDeg = normalizeAngle(pointAngle * RAD_TO_DEG);
            
            return (pointAngleDeg >= startAngle && pointAngleDeg <= endAngle) ||
                   (startAngle > endAngle && (pointAngleDeg >= startAngle || pointAngleDeg <= endAngle));
        }
        
        case QtShapeType::Polygon: {
            if (isFilled()) {
                return m_polygon.containsPoint(position, Qt::OddEvenFill);
            } else {
                // Check polygon edges
                for (int i = 0; i < m_polygon.size(); ++i) {
                    int nextIndex = (i + 1) % m_polygon.size();
                    
                    EDA_SHAPE_DATA edgeSegment(QtShapeType::Segment);
                    edgeSegment.setStartPoint(m_polygon[i]);
                    edgeSegment.setEndPoint(m_polygon[nextIndex]);
                    
                    if (edgeSegment.hitTest(position, accuracy)) {
                        return true;
                    }
                }
                return false;
            }
        }
        
        case QtShapeType::Bezier: {
            // Approximate bezier with line segments for hit testing
            if (m_bezierPoints.size() < 2) {
                return false;
            }
            
            for (int i = 0; i < m_bezierPoints.size() - 1; ++i) {
                EDA_SHAPE_DATA segment(QtShapeType::Segment);
                segment.setStartPoint(m_bezierPoints[i]);
                segment.setEndPoint(m_bezierPoints[i + 1]);
                
                if (segment.hitTest(position, accuracy)) {
                    return true;
                }
            }
            return false;
        }
        
        default:
            return false;
    }
}

bool EDA_SHAPE_DATA::hitTest(const QRectF& rect, bool contained, double accuracy) const
{
    QRectF adjustedRect = rect.adjusted(-accuracy, -accuracy, accuracy, accuracy);
    QRectF boundingRect = getBoundingRect();
    
    if (contained) {
        return adjustedRect.contains(boundingRect);
    } else {
        return adjustedRect.intersects(boundingRect);
    }
}

//=============================================================================
// PROTECTED HELPER METHODS
//=============================================================================

void EDA_SHAPE_DATA::updateDerivedProperties()
{
    // Update cached properties based on current geometry
    switch (m_shapeType) {
        case QtShapeType::Rectangle:
            m_rectangleWidth = qAbs(m_endPoint.x() - m_startPoint.x());
            m_rectangleHeight = qAbs(m_endPoint.y() - m_startPoint.y());
            break;
            
        case QtShapeType::Segment:
            m_segmentLength = distance(m_startPoint, m_endPoint);
            m_segmentAngle = angleBetweenPoints(m_startPoint, m_endPoint);
            break;
            
        default:
            break;
    }
    
    invalidateCache();
}



void EDA_SHAPE_DATA::invalidateCache()
{
    m_boundingRectValid = false;
}

void EDA_SHAPE_DATA::ensureBoundingRectValid() const
{
    if (m_boundingRectValid) {
        return;
    }
    
    switch (m_shapeType) {
        case QtShapeType::Segment: {
            double halfWidth = m_stroke.width * 0.5;
            QRectF rect = QRectF(m_startPoint, m_endPoint).normalized();
            m_cachedBoundingRect = rect.adjusted(-halfWidth, -halfWidth, halfWidth, halfWidth);
            break;
        }
        
        case QtShapeType::Rectangle: {
            double halfWidth = m_stroke.width * 0.5;
            QRectF rect = QRectF(m_startPoint, m_endPoint).normalized();
            m_cachedBoundingRect = rect.adjusted(-halfWidth, -halfWidth, halfWidth, halfWidth);
            break;
        }
        
        case QtShapeType::Circle: {
            double radius = getRadius();
            double halfWidth = m_stroke.width * 0.5;
            double totalRadius = radius + halfWidth;
            m_cachedBoundingRect = QRectF(m_startPoint.x() - totalRadius, m_startPoint.y() - totalRadius,
                                         2.0 * totalRadius, 2.0 * totalRadius);
            break;
        }
        
        case QtShapeType::Arc: {
            computeArcBoundingBox(m_cachedBoundingRect);
            break;
        }
        
        case QtShapeType::Polygon: {
            if (!m_polygon.isEmpty()) {
                m_cachedBoundingRect = m_polygon.boundingRect();
                double halfWidth = m_stroke.width * 0.5;
                m_cachedBoundingRect.adjust(-halfWidth, -halfWidth, halfWidth, halfWidth);
            } else {
                m_cachedBoundingRect = QRectF();
            }
            break;
        }
        
        case QtShapeType::Bezier: {
            if (!m_bezierPoints.isEmpty()) {
                QPolygonF bezierPoly(m_bezierPoints);
                m_cachedBoundingRect = bezierPoly.boundingRect();
                double halfWidth = m_stroke.width * 0.5;
                m_cachedBoundingRect.adjust(-halfWidth, -halfWidth, halfWidth, halfWidth);
            } else {
                m_cachedBoundingRect = QRectF();
            }
            break;
        }
        
        default:
            m_cachedBoundingRect = QRectF();
            break;
    }
    
    m_boundingRectValid = true;
}

void EDA_SHAPE_DATA::computeArcBoundingBox(QRectF& bbox) const
{
    if (m_shapeType != QtShapeType::Arc) {
        bbox = QRectF();
        return;
    }
    
    double radius = distance(m_arcCenter, m_startPoint);
    double halfWidth = m_stroke.width * 0.5;
    double totalRadius = radius + halfWidth;
    
    // Start with arc endpoints
    bbox = QRectF(m_startPoint, m_endPoint).normalized();
    
    // Check if arc crosses axis-aligned extremes
    double startAngle, endAngle;
    calcArcAngles(startAngle, endAngle);
    
    // Check 0°, 90°, 180°, 270° crossings
    QVector<double> checkAngles = {0.0, 90.0, 180.0, 270.0};
    
    for (double angle : checkAngles) {
        if ((angle >= startAngle && angle <= endAngle) ||
            (startAngle > endAngle && (angle >= startAngle || angle <= endAngle))) {
            
            double angleRad = angle * DEG_TO_RAD;
            QPointF extremePoint = m_arcCenter + QPointF(totalRadius * qCos(angleRad), 
                                                        totalRadius * qSin(angleRad));
            
            bbox = bbox.united(QRectF(extremePoint, QSizeF(0, 0)));
        }
    }
    
    // Expand by line width
    bbox.adjust(-halfWidth, -halfWidth, halfWidth, halfWidth);
}

QVector<QPointF> EDA_SHAPE_DATA::buildBezierToSegments(double maxError) const
{
    QVector<QPointF> points;
    
    if (m_shapeType != QtShapeType::Bezier) {
        return points;
    }
    
    // Recursive subdivision algorithm for bezier approximation
    std::function<void(const QPointF&, const QPointF&, const QPointF&, const QPointF&, double)> 
        subdivideBezier = [&](const QPointF& p0, const QPointF& p1, const QPointF& p2, const QPointF& p3, double error) {
        
        // Check if current segment is flat enough
        QPointF line = p3 - p0;
        double lineLength = qSqrt(line.x() * line.x() + line.y() * line.y());
        
        if (lineLength < EPSILON) {
            points.append(p0);
            return;
        }
        
        // Calculate deviation of control points from straight line
        QPointF lineUnit = line / lineLength;
        QPointF perp(-lineUnit.y(), lineUnit.x());
        
        double dev1 = qAbs((p1 - p0).x() * perp.x() + (p1 - p0).y() * perp.y());
        double dev2 = qAbs((p2 - p0).x() * perp.x() + (p2 - p0).y() * perp.y());
        
        if (dev1 + dev2 < error) {
            points.append(p0);
            return;
        }
        
        // Subdivide curve
        QPointF p01 = (p0 + p1) * 0.5;
        QPointF p12 = (p1 + p2) * 0.5;
        QPointF p23 = (p2 + p3) * 0.5;
        QPointF p012 = (p01 + p12) * 0.5;
        QPointF p123 = (p12 + p23) * 0.5;
        QPointF p0123 = (p012 + p123) * 0.5;
        
        subdivideBezier(p0, p01, p012, p0123, error);
        subdivideBezier(p0123, p123, p23, p3, error);
    };
    
    points.clear();
    subdivideBezier(m_startPoint, m_bezierC1, m_bezierC2, m_endPoint, maxError);
    points.append(m_endPoint);  // Ensure end point is included
    
    return points;
}

//=============================================================================
// STATIC UTILITY METHODS
//=============================================================================

QString EDA_SHAPE_DATA::shapeTypeToString(QtShapeType type)
{
    switch (type) {
        case QtShapeType::Segment:    return QStringLiteral("Segment");
        case QtShapeType::Rectangle:  return QStringLiteral("Rectangle");
        case QtShapeType::Arc:        return QStringLiteral("Arc");
        case QtShapeType::Circle:     return QStringLiteral("Circle");
        case QtShapeType::Polygon:    return QStringLiteral("Polygon");
        case QtShapeType::Bezier:     return QStringLiteral("Bezier");
        default:                      return QStringLiteral("Undefined");
    }
}

QtShapeType EDA_SHAPE_DATA::shapeTypeFromString(const QString& str)
{
    QString lower = str.toLower();
    if (lower == QStringLiteral("segment")) return QtShapeType::Segment;
    if (lower == QStringLiteral("rectangle")) return QtShapeType::Rectangle;
    if (lower == QStringLiteral("arc")) return QtShapeType::Arc;
    if (lower == QStringLiteral("circle")) return QtShapeType::Circle;
    if (lower == QStringLiteral("polygon")) return QtShapeType::Polygon;
    if (lower == QStringLiteral("bezier")) return QtShapeType::Bezier;
    return QtShapeType::Undefined;
}

QString EDA_SHAPE_DATA::fillTypeToString(QtFillType type)
{
    switch (type) {
        case QtFillType::NoFill:                return QStringLiteral("None");
        case QtFillType::FilledShape:           return QStringLiteral("Filled");
        case QtFillType::FilledWithBgBodyColor: return QStringLiteral("Background");
        case QtFillType::FilledWithColor:       return QStringLiteral("Color");
        default:                                return QStringLiteral("None");
    }
}

QtFillType EDA_SHAPE_DATA::fillTypeFromString(const QString& str)
{
    QString lower = str.toLower();
    if (lower == QStringLiteral("filled")) return QtFillType::FilledShape;
    if (lower == QStringLiteral("background")) return QtFillType::FilledWithBgBodyColor;
    if (lower == QStringLiteral("color")) return QtFillType::FilledWithColor;
    return QtFillType::NoFill;
}

QString EDA_SHAPE_DATA::lineStyleToString(QtLineStyle style)
{
    switch (style) {
        case QtLineStyle::Default:    return QStringLiteral("Default");
        case QtLineStyle::Solid:      return QStringLiteral("Solid");
        case QtLineStyle::Dash:       return QStringLiteral("Dash");
        case QtLineStyle::Dot:        return QStringLiteral("Dot");
        case QtLineStyle::DashDot:    return QStringLiteral("DashDot");
        case QtLineStyle::DashDotDot: return QStringLiteral("DashDotDot");
        default:                      return QStringLiteral("Default");
    }
}

QtLineStyle EDA_SHAPE_DATA::lineStyleFromString(const QString& str)
{
    QString lower = str.toLower();
    if (lower == QStringLiteral("solid")) return QtLineStyle::Solid;
    if (lower == QStringLiteral("dash")) return QtLineStyle::Dash;
    if (lower == QStringLiteral("dot")) return QtLineStyle::Dot;
    if (lower == QStringLiteral("dashdot")) return QtLineStyle::DashDot;
    if (lower == QStringLiteral("dashdotdot")) return QtLineStyle::DashDotDot;
    return QtLineStyle::Default;
}

EDA_SHAPE_DATA* EDA_SHAPE_DATA::clone() const
{
    return new EDA_SHAPE_DATA(*this);
}

//=============================================================================
// COMPARISON AND SIMILARITY
//=============================================================================

bool EDA_SHAPE_DATA::operator==(const EDA_SHAPE_DATA& other) const
{
    if (m_shapeType != other.m_shapeType) return false;
    if (m_stroke != other.m_stroke) return false;
    if (m_fillMode != other.m_fillMode) return false;
    if (m_fillColor != other.m_fillColor) return false;
    
    // Compare geometric data based on shape type
    switch (m_shapeType) {
        case QtShapeType::Segment:
        case QtShapeType::Rectangle:
        case QtShapeType::Circle:
            return m_startPoint == other.m_startPoint && m_endPoint == other.m_endPoint;
            
        case QtShapeType::Arc:
            return m_startPoint == other.m_startPoint && 
                   m_endPoint == other.m_endPoint && 
                   m_arcCenter == other.m_arcCenter;
            
        case QtShapeType::Bezier:
            return m_startPoint == other.m_startPoint && 
                   m_endPoint == other.m_endPoint &&
                   m_bezierC1 == other.m_bezierC1 && 
                   m_bezierC2 == other.m_bezierC2;
            
        case QtShapeType::Polygon:
            return m_polygon == other.m_polygon;
            
        default:
            return true;
    }
}

int EDA_SHAPE_DATA::compare(const EDA_SHAPE_DATA& other) const
{
    // Compare by type first
    if (m_shapeType != other.m_shapeType) {
        return static_cast<int>(m_shapeType) - static_cast<int>(other.m_shapeType);
    }
    
    // Then by start point
    if (m_startPoint.x() != other.m_startPoint.x()) {
        return m_startPoint.x() < other.m_startPoint.x() ? -1 : 1;
    }
    
    if (m_startPoint.y() != other.m_startPoint.y()) {
        return m_startPoint.y() < other.m_startPoint.y() ? -1 : 1;
    }
    
    // Then by end point
    if (m_endPoint.x() != other.m_endPoint.x()) {
        return m_endPoint.x() < other.m_endPoint.x() ? -1 : 1;
    }
    
    if (m_endPoint.y() != other.m_endPoint.y()) {
        return m_endPoint.y() < other.m_endPoint.y() ? -1 : 1;
    }
    
    return 0;  // Equal
}

double EDA_SHAPE_DATA::similarity(const EDA_SHAPE_DATA& other) const
{
    if (m_shapeType != other.m_shapeType) {
        return 0.0;  // Different types are not similar
    }
    
    // Calculate similarity based on geometric properties
    double geometricSimilarity = 1.0;
    
    // Compare positions
    double startDistance = distance(m_startPoint, other.m_startPoint);
    double endDistance = distance(m_endPoint, other.m_endPoint);
    double avgDistance = (startDistance + endDistance) * 0.5;
    
    // Normalize distance (assuming max reasonable distance of 1000 units)
    geometricSimilarity *= qMax(0.0, 1.0 - avgDistance / 1000.0);
    
    // Compare stroke properties
    double strokeSimilarity = 1.0;
    if (m_stroke.width != other.m_stroke.width) {
        strokeSimilarity *= 0.8;
    }
    if (m_stroke.style != other.m_stroke.style) {
        strokeSimilarity *= 0.9;
    }
    
    // Compare fill properties
    double fillSimilarity = (m_fillMode == other.m_fillMode) ? 1.0 : 0.7;
    
    return geometricSimilarity * strokeSimilarity * fillSimilarity;
}

