#include "eda_brd_ttbox_data.h"
#include "eda_board_data.h"
#include "eda_footprint_data.h"
#include "qt_temporary_implementations.h"
#include <QRegularExpression>
#include <QTransform>
#include <QtMath>

// 构造函数
EDA_BRD_TTBOX_DATA::EDA_BRD_TTBOX_DATA(EDA_BOARD_OBJECT_DATA* aParent, QtKicadType aType) :
        EDA_BRD_SHAPE_DATA( aParent, aType, QtShapeType::Rectangle )
    , EDA_TEXT_DATA()
    , m_borderEnabled(true)
{
    // 设置默认文本对齐方式
    // setHorizJustify(GrTextHAlignT::LEFT);
    // setVertJustify(GrTextVAlignT::CENTER);
    // setMultilineAllowed(true);
    
    // 设置默认边距
    int defaultMargin = getLegacyTextMargin();
    m_marginLeft = defaultMargin;
    m_marginTop = defaultMargin;
    m_marginRight = defaultMargin;
    m_marginBottom = defaultMargin;
}

EDA_BRD_TTBOX_DATA::~EDA_BRD_TTBOX_DATA()
{
}

// 类型识别
bool EDA_BRD_TTBOX_DATA::classOf(const EDA_OBJECT_DATA* aItem)
{
    return aItem && aItem->getType() == PCB_TEXTBOX_T;
}

bool EDA_BRD_TTBOX_DATA::isType(const QVector<QtKicadType>& aScanTypes) const
{
    if (EDA_BOARD_OBJECT_DATA::isType(aScanTypes))
        return true;
        
    for (QtKicadType scanType : aScanTypes)
    {
        if (scanType == PCB_LOCATE_TEXT_T)
            return true;
    }
    
    return false;
}



// 从设计设置中应用样式
void EDA_BRD_TTBOX_DATA::styleFromSettings(const QtBoardDesignSettings& settings)
{
    EDA_BRD_SHAPE_DATA::styleFromSettings(settings);
    
    setTextSize(settings.getTextSize(static_cast<int>(getLayer())));
    // setTextThickness(settings.getTextThickness(static_cast<int>(getLayer())));
    setItalic(settings.getTextItalic(static_cast<int>(getLayer())));
    setKeepUpright(settings.getTextUpright(static_cast<int>(getLayer())));
    setMirrored(isBackLayer(getLayer()));
}

// 获取默认文本边距
int EDA_BRD_TTBOX_DATA::getLegacyTextMargin() const
{
    int strokeWidth = getStroke().width;
    return qRound(strokeWidth / 2.0) + qRound(getTextSize().height() * 0.75);
}

// 获取左上角位置
QPoint EDA_BRD_TTBOX_DATA::getTopLeft() const
{
    // QtEdaAngle rotation = getDrawRotation();
    // Simplified implementation - assume no rotation for now
    return getStartPoint().toPoint();
}

// 获取右下角位置
QPoint EDA_BRD_TTBOX_DATA::getBotRight() const
{
    // QtEdaAngle rotation = getDrawRotation();
    // Simplified implementation - assume no rotation for now
    return getEndPoint().toPoint();
}

// 设置位置
void EDA_BRD_TTBOX_DATA::setTop(int aVal)
{
    // QtEdaAngle rotation = getDrawRotation();
    // Simplified implementation - assume no rotation for now
    setStartY(aVal);
}

void EDA_BRD_TTBOX_DATA::setLeft(int aVal)
{
    // QtEdaAngle rotation = getDrawRotation();
    // Simplified implementation - assume no rotation for now
    setStartX(aVal);
}

void EDA_BRD_TTBOX_DATA::setRight(int aVal)
{
    // QtEdaAngle rotation = getDrawRotation();
    // Simplified implementation - assume no rotation for now
    setEndX(aVal);
}

void EDA_BRD_TTBOX_DATA::setBottom(int aVal)
{
    // QtEdaAngle rotation = getDrawRotation();
    // Simplified implementation - assume no rotation for now
    setEndY(aVal);
}

// 边距管理
void EDA_BRD_TTBOX_DATA::setMarginLeft(int aLeft)
{
    if (m_marginLeft != aLeft) {
        m_marginLeft = aLeft;
    }
}

void EDA_BRD_TTBOX_DATA::setMarginTop(int aTop)
{
    if (m_marginTop != aTop) {
        m_marginTop = aTop;
    }
}

void EDA_BRD_TTBOX_DATA::setMarginRight(int aRight)
{
    if (m_marginRight != aRight) {
        m_marginRight = aRight;
    }
}

void EDA_BRD_TTBOX_DATA::setMarginBottom(int aBottom)
{
    if (m_marginBottom != aBottom) {
        m_marginBottom = aBottom;
    }
}

// 获取绘制位置
QPoint EDA_BRD_TTBOX_DATA::getDrawPos() const
{
    return getDrawPos(false);
}

QPoint EDA_BRD_TTBOX_DATA::getDrawPos(bool aIsFlipped) const
{
    QPoint topLeft = getTopLeft();
    QPoint botRight = getBotRight();
    
    // 计算考虑边距后的文本位置
    int x = topLeft.x() + m_marginLeft;
    int y = topLeft.y() + m_marginTop;
    int width = botRight.x() - topLeft.x() - m_marginLeft - m_marginRight;
    int height = botRight.y() - topLeft.y() - m_marginTop - m_marginBottom;
    
    // 根据对齐方式调整位置 - simplified for now
    // if (getHorizJustify() == GrTextHAlignT::CENTER)
    //     x += width / 2;
    // else if (getHorizJustify() == GrTextHAlignT::RIGHT)
    //     x += width;
        
    // if (getVertJustify() == GrTextVAlignT::CENTER)
    //     y += height / 2;
    // else if (getVertJustify() == GrTextVAlignT::BOTTOM)
    //     y += height;
        
    return QPoint(x, y);
}

// 设置文本角度
void EDA_BRD_TTBOX_DATA::setTextAngle(const QtEdaAngle& aAngle)
{
    EDA_TEXT_DATA::setAngleDegrees(aAngle.value);
}

// 获取显示文本
QString EDA_BRD_TTBOX_DATA::getShownText(bool aAllowExtraText, int aDepth) const
{
    const EDA_BOARD_DATA* board = getBoard();
    
    return EDA_TEXT_DATA::getShownText(aAllowExtraText, aDepth);
}

// 搜索匹配
bool EDA_BRD_TTBOX_DATA::matches(const QtEdaSearchData& aSearchData, void* aAuxData) const
{
    return getText().contains(aSearchData.getSearchText(), Qt::CaseInsensitive);
}

// 获取角点序列
QVector<QPoint> EDA_BRD_TTBOX_DATA::getCornersInSequence() const
{
    QVector<QPoint> corners;
    corners.reserve(4);
    
    // QtEdaAngle rotation = getDrawRotation();
    // Simplified implementation - assume no rotation for now
    corners.append(getStartPoint().toPoint());
    corners.append(QPoint(getEndPoint().x(), getStartPoint().y()));
    corners.append(getEndPoint().toPoint());
    corners.append(QPoint(getStartPoint().x(), getEndPoint().y()));
    
    return corners;
}

// 移动
void EDA_BRD_TTBOX_DATA::move(const QPoint& aMoveVector)
{
    EDA_BRD_SHAPE_DATA::move(aMoveVector);
    EDA_TEXT_DATA::offset(aMoveVector);
}

// 旋转
void EDA_BRD_TTBOX_DATA::rotate(const QPoint& aRotCentre, const QtEdaAngle& aAngle)
{
    EDA_BRD_SHAPE_DATA::rotate(aRotCentre, aAngle);
    
    // 旋转文本位置和角度 - simplified for now
    // QPoint textPos = getTextPos();
    // textPos = rotatePoint(textPos, aRotCentre, aAngle);
    // setTextPos(textPos);
    
    // QtEdaAngle newAngle = getTextAngle() + aAngle;
    // setTextAngle(newAngle);
}

// 镜像
void EDA_BRD_TTBOX_DATA::mirror(const QPoint& aCentre, FlipDirection aFlipDirection)
{
    EDA_BRD_SHAPE_DATA::mirror(aCentre, aFlipDirection);
    
    // 镜像文本位置 - simplified for now
    // QPoint textPos = getTextPos();
    // if (aFlipDirection == FlipDirection::LEFT_RIGHT)
    //     textPos.setX(2 * aCentre.x() - textPos.x());
    // else
    //     textPos.setY(2 * aCentre.y() - textPos.y());
    // setTextPos(textPos);
    
    // 镜像文本角度
    // if (aFlipDirection == FlipDirection::LEFT_RIGHT)
    // {
    //     QtEdaAngle angle = getTextAngle();
    //     angle = -angle;
    //     setTextAngle(angle);
    // }
}

// 翻转
void EDA_BRD_TTBOX_DATA::flip(const QPoint& aCentre, FlipDirection aFlipDirection)
{
    mirror(aCentre, aFlipDirection);
    
    // 切换到对应层
    setLayer(flipLayer(getLayer()));
    setMirrored(!isMirrored());
}

// 获取消息面板信息
void EDA_BRD_TTBOX_DATA::getMsgPanelInfo(EDA_BOARD_DATA* aFrame, QVector<QtMsgPanelItem>& aList) const
{
    // 添加基本信息
    aList.append(QtMsgPanelItem("Text Box", getText()));
    
    if (aFrame)
    {
        // TODO: 添加更多信息
    }
    
    // EDA_BOARD_OBJECT_DATA::getMsgPanelInfo(aFrame, aList);
}

// 碰撞测试
bool EDA_BRD_TTBOX_DATA::hitTest(const QPoint& aPosition, int aAccuracy) const
{
    QPoint topLeft = getTopLeft();
    QPoint botRight = getBotRight();
    QSize size = QSize(botRight.x() - topLeft.x(), botRight.y() - topLeft.y());
    QtBox2I rect(topLeft, size);
    rect.inflate(aAccuracy);
    return rect.contains(aPosition);
}

bool EDA_BRD_TTBOX_DATA::hitTest(const QtBox2I& aRect, bool aContained, int aAccuracy) const
{
    QtBox2I rect = getBoundingBox();
    
    if (aAccuracy)
        rect.inflate(aAccuracy);
        
    if (aContained)
        return aRect.contains(rect);
    else
        return aRect.intersects(rect);
}

// 文本转多边形
void EDA_BRD_TTBOX_DATA::transformTextToPolySet(QtShapePolySet& aBuffer, int aClearance, int aMaxError,
                                          ErrorLoc aErrorLoc) const
{
    // TODO: 实现文本到多边形的转换
}

void EDA_BRD_TTBOX_DATA::transformShapeToPolygon(QtShapePolySet& aBuffer, PcbLayerId aLayer, int aClearance,
                                           int aMaxError, ErrorLoc aErrorLoc,
                                           bool aIgnoreLineWidth) const
{
    // 首先转换边框
    if (isBorderEnabled())
    {
        EDA_BRD_SHAPE_DATA::transformShapeToPolygon(aBuffer, aLayer, aClearance, aMaxError, aErrorLoc, aIgnoreLineWidth);
    }
    
    // 然后转换文本
    transformTextToPolySet(aBuffer, aClearance, aMaxError, aErrorLoc);
}

// 获取有效形状
std::shared_ptr<QtShape> EDA_BRD_TTBOX_DATA::getEffectiveShape(QtPcbLayerId aLayer, QtFlashing aFlash) const
{
    // TODO: 实现有效形状的创建
    return EDA_BRD_SHAPE_DATA::getEffectiveShape(aLayer, aFlash);
}

// 获取项目描述
QString EDA_BRD_TTBOX_DATA::getItemDescription(QtUnitsProvider* aUnitsProvider, bool aFull) const
{
    return QString("PCB Text Box '%1' on layer")
        .arg(getText().left(40));
}

// 获取菜单图标
Bitmaps EDA_BRD_TTBOX_DATA::getMenuImage() const
{
    return QtBitmaps::TextDefault;
}

// 视图相关
double EDA_BRD_TTBOX_DATA::viewGetLOD(int aLayer, const QtView* aView) const
{
    // TODO: 实现LOD计算
    return 0.0;
}

void EDA_BRD_TTBOX_DATA::viewGetLayers(QVector<int>& layers) const
{
    layers.append(static_cast<int>(getLayer()));
}

// 克隆
EDA_OBJECT_DATA* EDA_BRD_TTBOX_DATA::clone() const
{
    // Create new instance without copy constructor to avoid QMutex copying issues
    EDA_BRD_TTBOX_DATA* newItem = new EDA_BRD_TTBOX_DATA(getParent(), getType());
    
    // Copy basic shape data
    newItem->setLayer(getLayer());
    newItem->setStroke(getStroke());
    newItem->setFilled(isFilled());
    
    // Copy text data - only use methods that exist
    newItem->setText(getText());
    newItem->setTextSize(getTextSize());
    
    // Copy textbox specific data
    newItem->m_borderEnabled = m_borderEnabled;
    newItem->m_marginLeft = m_marginLeft;
    newItem->m_marginTop = m_marginTop;
    newItem->m_marginRight = m_marginRight;
    newItem->m_marginBottom = m_marginBottom;
    
    return newItem;
}

// 边框管理
bool EDA_BRD_TTBOX_DATA::isBorderEnabled() const
{
    return m_borderEnabled;
}

void EDA_BRD_TTBOX_DATA::setBorderEnabled(bool enabled)
{
    if (m_borderEnabled != enabled) {
        m_borderEnabled = enabled;
    }
}

void EDA_BRD_TTBOX_DATA::setBorderWidth(int aSize)
{
    // Create new stroke with desired width - simplified implementation
    QtStrokeParams newStroke;
    newStroke.width = aSize;
    setStroke(newStroke);
}

int EDA_BRD_TTBOX_DATA::getBorderWidth() const
{
    return getStroke().width;
}

// 相似度比较
double EDA_BRD_TTBOX_DATA::similarity(const EDA_BOARD_OBJECT_DATA& aBoardItem) const
{
    if (aBoardItem.getType() != getType())
        return 0.0;
        
    const EDA_BRD_TTBOX_DATA& other = static_cast<const EDA_BRD_TTBOX_DATA&>(aBoardItem);
    
    double similarity = 1.0;
    
    if (m_marginLeft != other.m_marginLeft ||
        m_marginTop != other.m_marginTop ||
        m_marginRight != other.m_marginRight ||
        m_marginBottom != other.m_marginBottom)
        similarity *= 0.9;
        
    if (m_borderEnabled != other.m_borderEnabled)
        similarity *= 0.9;
        
    if (getText() != other.getText())
        similarity *= 0.9;
        
    similarity *= EDA_BRD_SHAPE_DATA::similarity(other);
    
    return similarity;
}

// 比较运算符
bool EDA_BRD_TTBOX_DATA::operator==(const EDA_BRD_TTBOX_DATA& aOther) const
{
    return EDA_BRD_SHAPE_DATA::operator==(aOther) &&
           EDA_TEXT_DATA::operator==(aOther) &&
           m_borderEnabled == aOther.m_borderEnabled &&
           m_marginLeft == aOther.m_marginLeft &&
           m_marginTop == aOther.m_marginTop &&
           m_marginRight == aOther.m_marginRight &&
           m_marginBottom == aOther.m_marginBottom;
}

bool EDA_BRD_TTBOX_DATA::operator==(const EDA_BOARD_OBJECT_DATA& aBoardItem) const
{
    if (aBoardItem.getType() != getType())
        return false;
        
    return operator==(static_cast<const EDA_BRD_TTBOX_DATA&>(aBoardItem));
}

// 交换数据
void EDA_BRD_TTBOX_DATA::swapData(EDA_BOARD_OBJECT_DATA* aImage)
{
    EDA_BRD_TTBOX_DATA* textbox = dynamic_cast<EDA_BRD_TTBOX_DATA*>(aImage);
    if (!textbox)
        return;
        
    EDA_BRD_SHAPE_DATA::swapData(aImage);
    EDA_TEXT_DATA::swapText(*textbox);
    
    std::swap(m_borderEnabled, textbox->m_borderEnabled);
    std::swap(m_marginLeft, textbox->m_marginLeft);
    std::swap(m_marginTop, textbox->m_marginTop);
    std::swap(m_marginRight, textbox->m_marginRight);
    std::swap(m_marginBottom, textbox->m_marginBottom);
}

// 获取字体度量
QFontMetrics EDA_BRD_TTBOX_DATA::getFontMetrics() const
{
    // TODO: 实现字体度量获取
    return QFontMetrics(QFont());
}