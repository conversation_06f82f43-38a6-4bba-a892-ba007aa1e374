# Qt-based KiCad BOARD Class Implementation

## Overview

This is a complete Qt-based reimplementation of KiCad's BOARD class, replacing wxWidgets dependencies with Qt equivalents. The implementation focuses on better performance, maintainability, and modern C++ practices.

## Key Features and Improvements

### 1. Qt Container Usage
- **QList** instead of std::deque/std::vector for better Qt integration
- **QHash** instead of std::unordered_map for better performance with Qt objects
- **QSet** instead of std::set for layer collections
- **QMap** instead of std::map for ordered associations

### 2. Qt String Handling
- **QString** instead of wxString for all text operations
- **QStringList** instead of wxArrayString for string collections
- Better Unicode support and internationalization

### 3. Signals and Slots Architecture
- **Qt signals** replace callback mechanisms for board change notifications
- Type-safe event handling with compile-time checking
- Automatic connection management

### 4. Memory Management
- **QSharedPointer** for shared resources (design settings, connectivity data)
- **QWeakPointer** for avoiding circular references
- Automatic parent-child relationship management via QObject hierarchy
- **deleteLater()** for safer asynchronous object deletion

### 5. Thread Safety
- **QReadWriteLock** instead of std::shared_mutex for better Qt integration
- **QMutexLocker** for RAII-style locking
- Thread-safe cache access patterns

### 6. Performance Optimizations
- Template-based container operations for type safety
- Improved cache key structures with Qt hash functions
- Bulk operations support for better performance
- Lazy evaluation for expensive computations

### 7. Modern C++ Features
- Range-based for loops
- Lambda expressions for callbacks
- Smart pointers for memory management
- constexpr for compile-time constants
- Structured bindings where appropriate

## File Structure

```
qt_pcbnew/
├── eda_board_data.h          # Header file with class definition
├── eda_board_data.cpp        # Implementation file
└── README.md           # This documentation
```

## Class Organization

The EDA_BOARD_DATA class is organized into logical sections:

### Construction and Destruction
- Thread-safe initialization
- Proper resource cleanup
- Qt parent-child relationships

### Item Management
```cpp
void addItem(EDA_BOARD_OBJECT_DATA* item, QtAddMode mode = QtAddMode::Insert, bool skipConnectivity = false);
void removeItem(EDA_BOARD_OBJECT_DATA* item, QtRemoveMode mode = QtRemoveMode::Normal);
EDA_BOARD_OBJECT_DATA* getItem(const QUuid& id) const;
```

### Layer Management
```cpp
int getCopperLayerCount() const;
void setCopperLayerCount(int count);
bool isLayerVisible(QtPcbLayerId layer) const;
```

### Network and Connectivity
```cpp
bool buildConnectivity(QtProgressReporter* reporter = nullptr);
QtNetInfo* findNet(const QString& netname) const;
```

### Geometric Operations
```cpp
QRectF computeBoundingBox(bool boardEdgesOnly = false) const;
void move(const QPointF& moveVector);
bool getBoardPolygonOutlines(QList<QPolygonF>& outlines, ...);
```

### Project and Settings
```cpp
void setProject(QtProject* project, bool referenceOnly = false);
QtBoardDesignSettings& getDesignSettings() const;
```

## Key Differences from Original

### 1. Container Types
```cpp
// Original KiCad
std::deque<BOARD_ITEM*> m_drawings;
std::vector<ZONE*> m_zones;

// Qt Version  
QList<EDA_BOARD_OBJECT_DATA*> m_drawings;
QList<EDA_ZONE_DATA*> m_zones;
```

### 2. String Handling
```cpp
// Original KiCad
wxString GetLayerName(PCB_LAYER_ID aLayer) const;

// Qt Version
QString getLayerName(QtPcbLayerId layer) const;
```

### 3. Event Handling
```cpp
// Original KiCad - Callback based
void AddListener(BOARD_LISTENER* aListener);

// Qt Version - Signals/Slots
signals:
    void itemAdded(EDA_BOARD_OBJECT_DATA* item);
    void itemChanged(EDA_BOARD_OBJECT_DATA* item);
```

### 4. Thread Safety
```cpp
// Original KiCad
mutable std::shared_mutex m_CachesMutex;

// Qt Version
mutable QReadWriteLock cachesMutex;
```

## Usage Example

```cpp
#include "eda_board_data.h"

// Create a board
auto board = new EDA_BOARD_DATA(this);

// Connect to signals
connect(board, &EDA_BOARD_DATA::itemAdded, this, &MyWidget::onItemAdded);
connect(board, &EDA_BOARD_DATA::itemChanged, this, &MyWidget::onItemChanged);

// Add items
auto footprint = new EDA_FOOTPRINT_DATA();
board->addItem(footprint);

// Layer operations
board->setCopperLayerCount(4);
bool visible = board->isLayerVisible(QtPcbLayerId::F_Cu);

// Geometric operations
QRectF bbox = board->getBoundingBox();
board->move(QPointF(10.0, 20.0));
```

## Benefits of Qt Implementation

1. **Better Integration**: Seamless integration with Qt-based applications
2. **Performance**: Qt containers are optimized for Qt object types
3. **Thread Safety**: Built-in thread safety with Qt's locking mechanisms
4. **Memory Management**: Automatic memory management with QObject hierarchy
5. **Type Safety**: Better type safety with Qt's meta-object system
6. **Debugging**: Better debugging support with Qt Creator and Qt debugging tools
7. **Cross-Platform**: Consistent behavior across platforms with Qt
8. **Future-Proof**: Built on modern Qt framework with active development

## Qt Object System Removal Progress

This section tracks the systematic removal of Qt object system while preserving functionality:

### Completed Classes ✅
1. **EDA_OBJECT_DATA** (Root base class) - 2025-08-05
   - ✅ Removed QObject inheritance
   - ✅ Removed Q_OBJECT macro  
   - ✅ Replaced Q_PROPERTY with standard getter/setter methods
   - ✅ Replaced signals/slots with callback pattern using std::function
   - ✅ Updated qobject_cast to dynamic_cast
   - ✅ Removed Q_DECLARE_METATYPE declarations
   - ✅ Preserved Qt containers and geometry classes
   - ✅ Preserved QFlags system (independent of Qt object system)

2. **EDA_SHAPE_DATA** (Geometry base class) - 2025-08-05
   - ✅ Removed QObject inheritance
   - ✅ Removed Q_OBJECT macro
   - ✅ Removed all Q_PROPERTY declarations
   - ✅ Deleted signals/slots sections entirely (simplified approach)
   - ✅ Removed Qt meta-object system registrations
   - ✅ Updated constructors to remove QObject parent parameters
   - ✅ Cleaned up all emit calls and notification functions
   - ✅ Preserved Qt containers (QVector, QPointF, QRectF, etc.)
   - ✅ Maintained all geometric functionality without Qt object system

3. **EDA_TEXT_DATA** (Text base class) - 2025-08-05
   - ✅ Removed QObject inheritance from class declaration
   - ✅ Removed Q_OBJECT macro from header file
   - ✅ Removed all Q_PROPERTY declarations (20+ properties removed)
   - ✅ Deleted entire signals: section with all text-related signals
   - ✅ Removed Q_DECLARE_METATYPE declarations for text types
   - ✅ Updated constructors to remove QObject parent parameters
   - ✅ Cleaned up all emit calls throughout implementation file
   - ✅ Removed emitGeometryChanged() helper method
   - ✅ Preserved Qt containers (QString, QFont, QVector, QHash, etc.)
   - ✅ Preserved Qt geometry classes (QPointF, QRectF, QSizeF)
   - ✅ Maintained comprehensive text functionality without Qt object system

### In Progress Classes 🔄
*(None currently)*

### Pending Classes 📋
4. **EDA_BOARD_OBJECT_DATA** (PCB object base)
5. **EDA_BOARD_CONNECTED_OBJECT** (Connected items base)
6. **EDA_TRACK_DATA** (Track implementation)
7. **EDA_VIA_DATA** (Via implementation)
8. **EDA_PAD_DATA** (Pad implementation)
9. **EDA_ZONE_DATA** (Zone implementation)
10. **EDA_FOOTPRINT_DATA** (Footprint implementation)
11. **EDA_BRD_SHAPE_DATA** (PCB shape with multiple inheritance)
12. **EDA_BRD_TEXT_DATA** (PCB text with multiple inheritance)
13. **EDA_BOARD_DATA** (Top-level board container)

### Removal Strategy Applied
- Remove QObject inheritance → Standard C++ classes
- Remove Q_OBJECT macro → No meta-object system
- Replace Q_PROPERTY → Standard getter/setter methods
- Replace signals/slots → Callback pattern with std::function
- Replace emit calls → Direct callback invocations
- Keep Qt containers (QVector, QList, QSet, QHash, QMap)
- Keep Qt geometry classes (QPointF, QRectF, QSizeF)
- Keep Qt strings (QString) for Unicode support

## Potential Extensions

1. **Qt Graphics**: Integration with Qt's graphics scene for 2D rendering
2. **Qt SQL**: Database integration for component libraries
3. **Qt Network**: Network capabilities for collaborative editing
4. **Qt Multimedia**: Support for multimedia content in documentation
5. **Qt Quick**: QML bindings for rapid UI development

## Compatibility Notes

This implementation maintains the same logical interface as the original KiCad BOARD class while using Qt types and conventions. It should be straightforward to adapt existing KiCad code to use this Qt version with appropriate type conversions.

## Building and Dependencies

Required Qt modules:
- Qt Core (containers, strings, objects)
- Qt GUI (geometric types, fonts)
- Qt Widgets (if UI integration needed)

The implementation is designed to be header-only compatible with modern Qt versions (5.15+/6.x).