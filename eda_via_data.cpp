/*
 * Qt-based reimplementation of KiCad PCB_VIA class implementation
 */

#include "eda_via_data.h"
#include "eda_board_data.h"
#include "eda_net_data.h"
#include "eda_padstack_data.h"
#include "qt_temporary_implementations.h"
#include <QtCore/QDebug>
#include <QtCore/QMutexLocker>
#include <QtMath>
#include <algorithm>

Q_LOGGING_CATEGORY(eDA_VIA_DATA, "qt.pcbnew.via")

// Static member initialization
//const QtZoneLayerOverride EDA_VIA_DATA::s_emptyOverride;

//=============================================================================
// CONSTRUCTION AND DESTRUCTION
//=============================================================================

EDA_VIA_DATA::EDA_VIA_DATA(EDA_BOARD_OBJECT_DATA* parent)
    : EDA_TRACK_DATA(parent, QtKicadType::PCB_VIA_T)
    , m_viaType(QtViaType::Through)
    , m_padStack(new EDA_PADSTACK_DATA())
    , m_isFree(false)
{
    // Vias are circles, so start = end
    setEnd(getStart());
    
    // Initialize padstack for via
    m_padStack->setMode(QtPadStackMode::Normal);
    m_padStack->setShape(QtPadShape::Circle);
}

EDA_VIA_DATA::EDA_VIA_DATA(const EDA_VIA_DATA& other)
    : EDA_TRACK_DATA(other)
    , m_viaType(other.m_viaType)
    , m_padStack(new EDA_PADSTACK_DATA(*other.m_padStack))
    , m_isFree(other.m_isFree)
{
    // Copy zone layer overrides with thread safety
    QMutexLocker locker(&other.m_zoneLayerOverridesMutex);
    //m_zoneLayerOverrides = other.m_zoneLayerOverrides;
}

EDA_VIA_DATA::~EDA_VIA_DATA() = default;

//EDA_VIA_DATA& EDA_VIA_DATA::operator=(const EDA_VIA_DATA& other)
//{
//    if (this != &other) {
//        EDA_TRACK_DATA::operator=(other);
//        m_viaType = other.m_viaType;
//        *m_padStack = *other.m_padStack;
//        m_isFree = other.m_isFree;
//        
//        QMutexLocker locker(&other.m_zoneLayerOverridesMutex);
//        QMutexLocker thisLocker(&m_zoneLayerOverridesMutex);
//        m_zoneLayerOverrides = other.m_zoneLayerOverrides;
//        
//        clearShapeCache();
//    }
//    return *this;
//}

//=============================================================================
// TYPE IDENTIFICATION
//=============================================================================

bool EDA_VIA_DATA::classOf(const EDA_OBJECT_DATA* item)
{
    return item && item->getType() == QtKicadType::PCB_VIA_T;
}

bool EDA_VIA_DATA::isType(const QVector<QtKicadType>& scanTypes) const
{
    if (EDA_BOARD_CONNECTED_OBJECT::isType(scanTypes)) {
        return true;
    }
    
    for (QtKicadType scanType : scanTypes) {
        if (scanType == QtKicadType::PCB_LOCATE_STDVIA_T && m_viaType == QtViaType::Through) {
            return true;
        } else if (scanType == QtKicadType::PCB_LOCATE_UVIA_T && m_viaType == QtViaType::MicroVia) {
            return true;
        } else if (scanType == QtKicadType::PCB_LOCATE_BBVIA_T && m_viaType == QtViaType::BlindBuried) {
            return true;
        }
    }
    
    return false;
}

//=============================================================================
// VIA TYPE MANAGEMENT
//=============================================================================

void EDA_VIA_DATA::setViaType(QtViaType type)
{
    if (m_viaType != type) {
        m_viaType = type;
        clearShapeCache();
        // Via type changed
    }
}

//bool EDA_VIA_DATA::hasValidLayerPair(int copperLayerCount) const
//{
//    QtPcbLayerId top = getTopLayer();
//    QtPcbLayerId bottom = getBottomLayer();
//    
//    if (!QtLayerIdUtils::isCopperLayer(top) || !QtLayerIdUtils::isCopperLayer(bottom)) {
//        return false;
//    }
//    
//    if (top == bottom) {
//        return false;
//    }
//    
//    // Check layer ordering
//    if (QtLayerIdUtils::getLayerOrder(top) > QtLayerIdUtils::getLayerOrder(bottom)) {
//        return false;
//    }
//    
//    // Check microvia constraints
//    if (m_viaType == QtViaType::MicroVia) {
//        return QtViaUtils::isAdjacentLayerPair(top, bottom);
//    }
//    
//    return true;
//}

//=============================================================================
// PADSTACK MANAGEMENT
//=============================================================================

void EDA_VIA_DATA::setPadStack(const EDA_PADSTACK_DATA& padstack)
{
    *m_padStack = padstack;
    clearShapeCache();
    // Padstack changed
}

//=============================================================================
// POSITION MANAGEMENT
//=============================================================================

void EDA_VIA_DATA::setPosition(const QPointF& position)
{
    setStart(position);
    setEnd(position);  // Vias are circles
}

//=============================================================================
// WIDTH MANAGEMENT (LAYER-SPECIFIC)
//=============================================================================

void EDA_VIA_DATA::setWidth(int width)
{
    m_padStack->setSize(QSizeF(width, width));
    clearShapeCache();
    // Width changed
}

int EDA_VIA_DATA::getWidth() const
{
    return qRound(m_padStack->size().width());
}

void EDA_VIA_DATA::setWidth(QtPcbLayerId layer, int width)
{
    m_padStack->setSize(QSizeF(width, width), layer);
    m_shapeDirtyLayers.insert(layer);
    // Width changed
}

int EDA_VIA_DATA::getWidth(QtPcbLayerId layer) const
{
    return qRound(m_padStack->size(layer).width());
}

//=============================================================================
// DRILL PROPERTIES
//=============================================================================

void EDA_VIA_DATA::setDrill(int drill)
{
    m_padStack->drill().size = QSizeF(drill, drill);
    clearShapeCache();
    // Drill changed
}

int EDA_VIA_DATA::getDrill() const
{
    return qRound(m_padStack->drill().size.width());
}

//int EDA_VIA_DATA::getDrillValue() const
//{
//    int drill = getDrill();
//    
//    if (drill > 0) {
//        return drill;
//    }
//    
//    // Get default drill from board
//    if (auto* board = getBoard()) {
//        return board->getDesignSettings()->getViaDrill();
//    }
//    
//    return 0;
//}

void EDA_VIA_DATA::setDrillDefault()
{
    setDrill(QT_UNDEFINED_DRILL_DIAMETER);
}

bool EDA_VIA_DATA::hasDrilledHole() const
{
    return m_viaType == QtViaType::Through || m_viaType == QtViaType::BlindBuried;
}

std::shared_ptr<QtShapeSegment> EDA_VIA_DATA::getEffectiveHoleShape() const
{
    int drillSize = getDrillValue();
    return std::make_shared<QtShapeSegment>(getStart(), getStart(), drillSize);
}

//=============================================================================
// LAYER MANAGEMENT
//=============================================================================

QtPcbLayerId EDA_VIA_DATA::getLayer() const
{
    return m_padStack->drill().startLayer;
}

void EDA_VIA_DATA::setLayer(QtPcbLayerId layer)
{
    m_padStack->drill().startLayer = layer;
    clearShapeCache();
}

//bool EDA_VIA_DATA::isOnLayer(QtPcbLayerId layer) const
//{
//    return getLayerSet().contains(layer);
//}

//QtLayerSet EDA_VIA_DATA::getLayerSet() const
//{
//    QtLayerSet layers;
//    
//    // Add all copper layers between top and bottom
//    QtPcbLayerId top = getTopLayer();
//    QtPcbLayerId bottom = getBottomLayer();
//    
//    if (QtLayerIdUtils::isCopperLayer(top) && QtLayerIdUtils::isCopperLayer(bottom)) {
//        int topOrder = QtLayerIdUtils::getLayerOrder(top);
//        int bottomOrder = QtLayerIdUtils::getLayerOrder(bottom);
//        
//        for (int order = topOrder; order <= bottomOrder; ++order) {
//            QtPcbLayerId layer = QtLayerIdUtils::getLayerFromOrder(order);
//            if (QtLayerIdUtils::isCopperLayer(layer)) {
//                layers.set(layer);
//            }
//        }
//    }
//    
//    return layers;
//}

//void EDA_VIA_DATA::setLayerSet(const QtLayerSet& layers)
//{
//    // Find top and bottom copper layers
//    QtPcbLayerId top = QtPcbLayerId::UndefinedLayer;
//    QtPcbLayerId bottom = QtPcbLayerId::UndefinedLayer;
//    
//    for (QtPcbLayerId layer : layers.seq()) {
//        if (QtLayerIdUtils::isCopperLayer(layer)) {
//            if (top == QtPcbLayerId::UndefinedLayer || 
//                QtLayerIdUtils::getLayerOrder(layer) < QtLayerIdUtils::getLayerOrder(top)) {
//                top = layer;
//            }
//            if (bottom == QtPcbLayerId::UndefinedLayer || 
//                QtLayerIdUtils::getLayerOrder(layer) > QtLayerIdUtils::getLayerOrder(bottom)) {
//                bottom = layer;
//            }
//        }
//    }
//    
//    if (top != QtPcbLayerId::UndefinedLayer && bottom != QtPcbLayerId::UndefinedLayer) {
//        setLayerPair(top, bottom);
//    }
//}

void EDA_VIA_DATA::setLayerPair(QtPcbLayerId topLayer, QtPcbLayerId bottomLayer)
{
    m_padStack->drill().startLayer = topLayer;
    m_padStack->drill().endLayer = bottomLayer;
    clearShapeCache();
    // Layer pair changed
}

void EDA_VIA_DATA::setTopLayer(QtPcbLayerId layer)
{
    m_padStack->drill().startLayer = layer;
    clearShapeCache();
    // Layer pair changed
}

void EDA_VIA_DATA::setBottomLayer(QtPcbLayerId layer)
{
    m_padStack->drill().endLayer = layer;
    clearShapeCache();
    // Layer pair changed
}

void EDA_VIA_DATA::getLayerPair(QtPcbLayerId* topLayer, QtPcbLayerId* bottomLayer) const
{
    if (topLayer) {
        *topLayer = m_padStack->drill().startLayer;
    }
    if (bottomLayer) {
        *bottomLayer = m_padStack->drill().endLayer;
    }
}

QtPcbLayerId EDA_VIA_DATA::getTopLayer() const
{
    return m_padStack->drill().startLayer;
}

QtPcbLayerId EDA_VIA_DATA::getBottomLayer() const
{
    return m_padStack->drill().endLayer;
}

//void EDA_VIA_DATA::sanitizeLayers()
//{
//    QtPcbLayerId top = getTopLayer();
//    QtPcbLayerId bottom = getBottomLayer();
//    
//    // Ensure proper layer ordering
//    if (QtLayerIdUtils::getLayerOrder(top) > QtLayerIdUtils::getLayerOrder(bottom)) {
//        setLayerPair(bottom, top);
//    }
//    
//    // Validate via type constraints
//    if (m_viaType == QtViaType::MicroVia) {
//        if (!QtViaUtils::isAdjacentLayerPair(top, bottom)) {
//            // Force to through via if not adjacent
//            setViaType(QtViaType::Through);
//        }
//    }
//}

//=============================================================================
// TENTING MODE MANAGEMENT
//=============================================================================

void EDA_VIA_DATA::setFrontTentingMode(QtTentingMode mode)
{
    // Tenting mode mapping - simplified for Qt migration
    m_padStack->frontOuterLayers().hasSolderMask = (mode != QtTentingMode::NotTented);
    // Tenting mode changed
}

QtTentingMode EDA_VIA_DATA::getFrontTentingMode() const
{
    // Simplified tenting mode retrieval for Qt migration
    return m_padStack->frontOuterLayers().hasSolderMask.value_or(true) ? QtTentingMode::Tented : QtTentingMode::NotTented;
}

void EDA_VIA_DATA::setBackTentingMode(QtTentingMode mode)
{
    // Tenting mode mapping - simplified for Qt migration
    m_padStack->backOuterLayers().hasSolderMask = (mode != QtTentingMode::NotTented);
    // Tenting mode changed
}

QtTentingMode EDA_VIA_DATA::getBackTentingMode() const
{
    // Simplified tenting mode retrieval for Qt migration
    return m_padStack->backOuterLayers().hasSolderMask.value_or(true) ? QtTentingMode::Tented : QtTentingMode::NotTented;
}

//bool EDA_VIA_DATA::isTented(QtPcbLayerId layer) const
//{
//    if (layer == QtPcbLayerId::FSilkMask) {
//        return getFrontTentingMode() != QtTentingMode::NotTented;
//    } else if (layer == QtPcbLayerId::BSilkMask) {
//        return getBackTentingMode() != QtTentingMode::NotTented;
//    }
//    
//    return false;
//}

//=============================================================================
// CONNECTION AND FLASHING
//=============================================================================

bool EDA_VIA_DATA::conditionallyFlashed(QtPcbLayerId layer) const
{
    switch (m_padStack->unconnectedLayerMode()) {
    case QtUnconnectedLayerMode::KeepAll:
        return false;
        
    case QtUnconnectedLayerMode::RemoveAll:
        return true;
        
    case QtUnconnectedLayerMode::RemoveExceptStartAndEnd:
        if (layer == m_padStack->drill().startLayer || layer == m_padStack->drill().endLayer) {
            return false;
        }
        return true;
    }
    
    return true;
}

//bool EDA_VIA_DATA::flashLayer(int layer) const
//{
//    // Check if layer is within via span
//    if (!isOnLayer(static_cast<QtPcbLayerId>(layer))) {
//        return false;
//    }
//    
//    // Check zone layer overrides
//    {
//        QMutexLocker locker(&m_zoneLayerOverridesMutex);
//        auto it = m_zoneLayerOverrides.find(static_cast<QtPcbLayerId>(layer));
//        if (it != m_zoneLayerOverrides.end()) {
//            if (it->mode == QtZoneLayerOverride::Mode::ForceFlashed) {
//                return true;
//            } else if (it->mode == QtZoneLayerOverride::Mode::ForceNotFlashed) {
//                return false;
//            }
//        }
//    }
//    
//    // Check connectivity
//    if (conditionallyFlashed(static_cast<QtPcbLayerId>(layer))) {
//        // Would need connectivity check here
//        return true;  // Simplified for now
//    }
//    
//    return true;
//}

//bool EDA_VIA_DATA::flashLayer(const QtLayerSet& layers) const
//{
//    for (QtPcbLayerId layer : layers.seq()) {
//        if (flashLayer(static_cast<int>(layer))) {
//            return true;
//        }
//    }
//    return false;
//}

void EDA_VIA_DATA::getOutermostConnectedLayers(QtPcbLayerId* topmost, QtPcbLayerId* bottommost) const
{
    // This would require connectivity analysis
    // For now, return the via's layer pair
    if (topmost) {
        *topmost = getTopLayer();
    }
    if (bottommost) {
        *bottommost = getBottomLayer();
    }
}

//=============================================================================
// FREE VIA MANAGEMENT
//=============================================================================

void EDA_VIA_DATA::setIsFree(bool free)
{
    if (m_isFree != free) {
        m_isFree = free;
        // Free status changed
    }
}

//=============================================================================
// ZONE LAYER OVERRIDES
//=============================================================================

//void EDA_VIA_DATA::clearZoneLayerOverrides()
//{
//    QMutexLocker locker(&m_zoneLayerOverridesMutex);
//    m_zoneLayerOverrides.clear();
//}

//const QtZoneLayerOverride& EDA_VIA_DATA::getZoneLayerOverride(QtPcbLayerId layer) const
//{
//    QMutexLocker locker(&m_zoneLayerOverridesMutex);
//    auto it = m_zoneLayerOverrides.find(layer);
//    if (it != m_zoneLayerOverrides.end()) {
//        return it.value();
//    }
//    return s_emptyOverride;
//}

//void EDA_VIA_DATA::setZoneLayerOverride(QtPcbLayerId layer, const QtZoneLayerOverride& override)
//{
//    QMutexLocker locker(&m_zoneLayerOverridesMutex);
//    if (override.mode == QtZoneLayerOverride::Mode::None) {
//        m_zoneLayerOverrides.remove(layer);
//    } else {
//        m_zoneLayerOverrides[layer] = override;
//    }
//    // Zone override changed
//}

//=============================================================================
// DEPRECATED COMPATIBILITY METHODS
//=============================================================================

void EDA_VIA_DATA::setRemoveUnconnected(bool set)
{
    m_padStack->setUnconnectedLayerMode(set
        ? QtUnconnectedLayerMode::RemoveAll
        : QtUnconnectedLayerMode::KeepAll);
}

bool EDA_VIA_DATA::getRemoveUnconnected() const
{
    return m_padStack->unconnectedLayerMode() != QtUnconnectedLayerMode::KeepAll;
}

void EDA_VIA_DATA::setKeepStartEnd(bool set)
{
    m_padStack->setUnconnectedLayerMode(set
        ? QtUnconnectedLayerMode::RemoveExceptStartAndEnd
        : QtUnconnectedLayerMode::RemoveAll);
}

bool EDA_VIA_DATA::getKeepStartEnd() const
{
    return m_padStack->unconnectedLayerMode() == 
           QtUnconnectedLayerMode::RemoveExceptStartAndEnd;
}

//=============================================================================
// CONSTRAINTS
//=============================================================================

//QtMinOptMax<int> EDA_VIA_DATA::getWidthConstraint(QString* source) const
//{
//    if (auto* board = getBoard()) {
//        return board->getDesignSettings()->getViaWidthConstraint(this, source);
//    }
//    
//    return QtMinOptMax<int>();
//}

//QtMinOptMax<int> EDA_VIA_DATA::getDrillConstraint(QString* source) const
//{
//    if (auto* board = getBoard()) {
//        return board->getDesignSettings()->getViaDrillConstraint(this, source);
//    }
//    
//    return QtMinOptMax<int>();
//}

//int EDA_VIA_DATA::getMinAnnulus(QtPcbLayerId layer, QString* source) const
//{
//    if (auto* board = getBoard()) {
//        return board->getDesignSettings()->getViaMinAnnulus(this, layer, source);
//    }
//    
//    return 0;
//}

//=============================================================================
// GEOMETRIC CALCULATIONS
//=============================================================================

//QRectF EDA_VIA_DATA::getBoundingBox() const
//{
//    int maxWidth = getWidth();
//    
//    // Check all layers for maximum width
//    for (QtPcbLayerId layer : getLayerSet().seq()) {
//        maxWidth = std::max(maxWidth, getWidth(layer));
//    }
//    
//    double radius = maxWidth / 2.0;
//    QPointF center = getPosition();
//    
//    return QRectF(center.x() - radius, center.y() - radius,
//                  2 * radius, 2 * radius);
//}

//int EDA_VIA_DATA::getSolderMaskExpansion() const
//{
//    // Via-specific solder mask expansion logic
//    if (isTented(QtPcbLayerId::FSilkMask) && isTented(QtPcbLayerId::BSilkMask)) {
//        return 0;
//    }
//    
//    return EDA_TRACK_DATA::getSolderMaskExpansion();
//}

//=============================================================================
// SHAPE GENERATION
//=============================================================================

std::shared_ptr<QtShape> EDA_VIA_DATA::getEffectiveShape(QtPcbLayerId layer, QtFlashing flash) const
{
    QMutexLocker locker(&m_shapeCacheMutex);
    
    // Check cache
    if (!m_shapeDirtyLayers.contains(layer)) {
        auto it = m_shapeCache.find(layer);
        if (it != m_shapeCache.end()) {
            return it.value();
        }
    }
    
    // Build shape for layer
    buildEffectiveShapeForLayer(layer);
    m_shapeDirtyLayers.remove(layer);
    
    return m_shapeCache[layer];
}

void EDA_VIA_DATA::buildEffectiveShapeForLayer(QtPcbLayerId layer) const
{
    int width = getWidth(layer);
    QtFlashing flash = QtFlashing::Default; // Default flashing behavior
    
    if (flash == QtFlashing::Never || (flash == QtFlashing::Default && !flashLayer(static_cast<int>(layer)))) {
        // Return hole shape
        m_shapeCache[layer] = std::static_pointer_cast<QtShape>(getEffectiveHoleShape());
    } else {
        // Return full via shape
        m_shapeCache[layer] = std::static_pointer_cast<QtShape>(std::make_shared<QtShapeCircle>(getPosition().toPoint(), width / 2));
    }
}

//=============================================================================
// HIT TESTING
//=============================================================================

bool EDA_VIA_DATA::hitTest(const QPointF& position, double accuracy) const
{
    double maxRadius = getWidth() / 2.0 + accuracy;
    double distance = QLineF(getPosition(), position).length();
    
    return distance <= maxRadius;
}

bool EDA_VIA_DATA::hitTest(const QRectF& rect, bool contained, double accuracy) const
{
    QRectF bbox = getBoundingBox();
    bbox.adjust(-accuracy, -accuracy, accuracy, accuracy);
    
    if (contained) {
        return rect.contains(bbox);
    } else {
        return rect.intersects(bbox);
    }
}

//=============================================================================
// UI AND DESCRIPTION
//=============================================================================

//void EDA_VIA_DATA::getMsgPanelInfo(QtDrawFrame* frame, QList<QtMsgPanelItem>& list)
//{
//    QString viaTypeName;
//    switch (m_viaType) {
//    case QtViaType::Through:
//        viaTypeName = tr("Through");
//        break;
//    case QtViaType::BlindBuried:
//        viaTypeName = tr("Blind/Buried");
//        break;
//    case QtViaType::MicroVia:
//        viaTypeName = tr("Micro");
//        break;
//    default:
//        viaTypeName = tr("Unknown");
//        break;
//    }
//    
//    list.append(QtMsgPanelItem(tr("Type"), tr("Via (%1)").arg(viaTypeName)));
//    list.append(QtMsgPanelItem(tr("Position"), QtUnitsUtils::formatPoint(getPosition())));
//    list.append(QtMsgPanelItem(tr("Diameter"), QtUnitsUtils::formatLength(getWidth())));
//    list.append(QtMsgPanelItem(tr("Drill"), QtUnitsUtils::formatLength(getDrillValue())));
//    
//    QString layers = QString("%1 - %2").arg(getLayerName(getTopLayer()))
//                                       .arg(getLayerName(getBottomLayer()));
//    list.append(QtMsgPanelItem(tr("Layers"), layers));
//    
//    if (getNetCode() > 0) {
//        list.append(QtMsgPanelItem(tr("Net"), getNetname()));
//        list.append(QtMsgPanelItem(tr("Net Class"), getNetClassName()));
//    }
//    
//    if (m_isFree) {
//        list.append(QtMsgPanelItem(tr("Status"), tr("Free Via")));
//    }
//}

QString EDA_VIA_DATA::getItemDescription(QtUnitsProvider* unitsProvider, bool includeType) const
{
    QString desc;
    
    switch (m_viaType) {
    case QtViaType::Through:
        desc = QString("Through Via");
        break;
    case QtViaType::BlindBuried:
        // Get layer names via board - getLayerName needs board context
        // For now, use layer IDs directly
        desc = QString("Blind/Buried Via %1:%2").arg(static_cast<int>(this->getTopLayer()))
                                           .arg(static_cast<int>(this->getBottomLayer()));
        break;
    case QtViaType::MicroVia:
        // Get layer names via board - getLayerName needs board context
        // For now, use layer IDs directly
        desc = QString("Micro Via %1:%2").arg(static_cast<int>(this->getTopLayer()))
                                    .arg(static_cast<int>(this->getBottomLayer()));
        break;
    default:
        desc = QString("Via");
        break;
    }
    
    if (includeType && this->getNetCode() > 0) {
        desc += QString(" [%1]").arg(this->getNetName());
    }
    
    return desc;
}

QtBitmaps EDA_VIA_DATA::getMenuImage() const
{
    switch (m_viaType) {
    case QtViaType::Through:
        return QtBitmaps::Via;
    case QtViaType::BlindBuried:
        return QtBitmaps::ViaBuried;
    case QtViaType::MicroVia:
        return QtBitmaps::ViaMicro;
    default:
        return QtBitmaps::Via;
    }
}

//=============================================================================
// VIEWING
//=============================================================================

void EDA_VIA_DATA::viewGetLayers(QVector<int>& layers) const
{
    layers.clear();
    
    // Add copper layers
    QVector<QtPcbLayerId> layerSeq = getLayerSet().sequence();
    for (QtPcbLayerId layer : layerSeq) {
        layers.append(static_cast<int>(layer));
    }
    
    // Add via holes layer
    int holeLayers[2] = {
        static_cast<int>(QtLayerGalId::ViaHolesTop),
        static_cast<int>(QtLayerGalId::ViaHolesBottom)
    };
    
    for (int layer : holeLayers) {
        layers.append(layer);
    }
    
    // Add net name layer if applicable
    if (getBoard() && getBoard()->isElementVisible(QtLayerGalId::ViasNetnames)) {
        if (getNetCode() > 0) {
            layers.append(static_cast<int>(getNetNameLayer(getLayer())));
        }
    }
}

double EDA_VIA_DATA::viewGetLOD(int layer, const QtView* view) const
{
    const double HIDE = std::numeric_limits<double>::max();
    const double SHOW = 0.0;
    
    if (!view) {
        return SHOW;
    }
    
    // Hide very small vias at low zoom
    if (view->getScale() < 2.0) {
        if (getWidth() < view->toWorld(3)) {
            return HIDE;
        }
    }
    
    // Always show holes at reasonable zoom
    if (layer == static_cast<int>(QtLayerGalId::ViaHolesTop) ||
        layer == static_cast<int>(QtLayerGalId::ViaHolesBottom)) {
        if (getDrillValue() > 0 && view->getScale() > 0.5) {
            return SHOW;
        }
    }
    
    return SHOW;
}

//=============================================================================
// TRANSFORMATIONS
//=============================================================================

void EDA_VIA_DATA::flip(const QPointF& center, QtFlipDirection flipDirection)
{
    EDA_TRACK_DATA::flip(center, flipDirection);
    
    // Flip layer pair
    if (auto* board = getBoard()) {
        QtPcbLayerId top = getTopLayer();
        QtPcbLayerId bottom = getBottomLayer();
        
        top = QtLayerIdUtils::flipLayer(top, board->getCopperLayerCount());
        bottom = QtLayerIdUtils::flipLayer(bottom, board->getCopperLayerCount());
        
        setLayerPair(top, bottom);
    }
}

//=============================================================================
// COMPARISON AND DUPLICATION
//=============================================================================

EDA_BOARD_OBJECT_DATA* EDA_VIA_DATA::clone() const
{
    return new EDA_VIA_DATA(*this);
}

double EDA_VIA_DATA::similarity(const EDA_BOARD_OBJECT_DATA& other) const
{
    if (other.getType() != getType()) {
        return 0.0;
    }
    
    const EDA_VIA_DATA* via = static_cast<const EDA_VIA_DATA*>(&other);
    
    double similarity = 1.0;
    
    // Check position
    if (getPosition() != via->getPosition()) {
        similarity *= 0.9;
    }
    
    // Check via type
    if (m_viaType != via->m_viaType) {
        similarity *= 0.8;
    }
    
    // Check layer pair
    if (getTopLayer() != via->getTopLayer() || getBottomLayer() != via->getBottomLayer()) {
        similarity *= 0.8;
    }
    
    // Check dimensions
    if (getWidth() != via->getWidth() || getDrill() != via->getDrill()) {
        similarity *= 0.9;
    }
    
    // Check net
    if (getNetCode() != via->getNetCode()) {
        similarity *= 0.5;
    }
    
    return similarity;
}

bool EDA_VIA_DATA::operator==(const EDA_BOARD_OBJECT_DATA& other) const
{
    if (other.getType() != getType()) {
        return false;
    }
    
    return operator==(static_cast<const EDA_VIA_DATA&>(other));
}

bool EDA_VIA_DATA::operator==(const EDA_VIA_DATA& other) const
{
    return EDA_TRACK_DATA::operator==(other) &&
           m_viaType == other.m_viaType &&
           *m_padStack == *other.m_padStack &&
           m_isFree == other.m_isFree &&
           m_zoneLayerOverrides == other.m_zoneLayerOverrides;
}

//=============================================================================
// QT SERIALIZATION
//=============================================================================

QVariantMap EDA_VIA_DATA::toVariantMap() const
{
    QVariantMap map = EDA_TRACK_DATA::toVariantMap();
    
    map["viaType"] = static_cast<int>(m_viaType);
    map["padStack"] = m_padStack->toVariantMap();
    map["isFree"] = m_isFree;
    
    // Serialize zone layer overrides
    QVariantMap overrides;
    {
        QMutexLocker locker(&m_zoneLayerOverridesMutex);
        for (auto it = m_zoneLayerOverrides.begin(); it != m_zoneLayerOverrides.end(); ++it) {
            overrides[QString::number(static_cast<int>(it.key()))] = 
                static_cast<int>(it.value().mode);
        }
    }
    if (!overrides.isEmpty()) {
        map["zoneLayerOverrides"] = overrides;
    }
    
    return map;
}

void EDA_VIA_DATA::fromVariantMap(const QVariantMap& map)
{
    EDA_TRACK_DATA::fromVariantMap(map);
    
    m_viaType = static_cast<QtViaType>(map.value("viaType", 
                                                 static_cast<int>(QtViaType::Through)).toInt());
    
    if (map.contains("padStack")) {
        m_padStack->fromVariantMap(map.value("padStack").toMap());
    }
    
    m_isFree = map.value("isFree", false).toBool();
    
    // Deserialize zone layer overrides
    if (map.contains("zoneLayerOverrides")) {
        QMutexLocker locker(&m_zoneLayerOverridesMutex);
        m_zoneLayerOverrides.clear();
        
        QVariantMap overrides = map.value("zoneLayerOverrides").toMap();
        for (auto it = overrides.begin(); it != overrides.end(); ++it) {
            QtPcbLayerId layer = static_cast<QtPcbLayerId>(it.key().toInt());
            QtZoneLayerOverride override;
            override.mode = static_cast<QtZoneLayerOverride::Mode>(it.value().toInt());
            m_zoneLayerOverrides[layer] = override;
        }
    }
    
    clearShapeCache();
}

//=============================================================================
// PROTECTED HELPER METHODS
//=============================================================================

void EDA_VIA_DATA::swapData(EDA_BOARD_OBJECT_DATA* other)
{
    EDA_TRACK_DATA::swapData(other);
    
    EDA_VIA_DATA* via = static_cast<EDA_VIA_DATA*>(other);
    std::swap(m_viaType, via->m_viaType);
    m_padStack.swap(via->m_padStack);
    std::swap(m_isFree, via->m_isFree);
    
    QMutexLocker locker1(&m_zoneLayerOverridesMutex);
    QMutexLocker locker2(&via->m_zoneLayerOverridesMutex);
    m_zoneLayerOverrides.swap(via->m_zoneLayerOverrides);
    
    clearShapeCache();
    via->clearShapeCache();
}

QString EDA_VIA_DATA::layerMaskDescribe() const
{
    if (auto* board = getBoard()) {
        QtPcbLayerId top = getTopLayer();
        QtPcbLayerId bottom = getBottomLayer();
        
        if (top == QtPcbLayerId::FCu && bottom == QtPcbLayerId::BCu) {
            return "all copper layers";
        }
        
        return QString("copper layers %1 to %2").arg(board->getLayerName(top))
                                          .arg(board->getLayerName(bottom));
    }
    
    return QString();
}

void EDA_VIA_DATA::clearShapeCache() const
{
    QMutexLocker locker(&m_shapeCacheMutex);
    m_shapeCache.clear();
    m_shapeDirtyLayers.clear();
}

//=============================================================================
// UTILITY FUNCTIONS IMPLEMENTATION
//=============================================================================

namespace QtViaUtils {

QtViaType calculateViaType(QtPcbLayerId topLayer, QtPcbLayerId bottomLayer, int copperLayerCount)
{
    if (topLayer == QtPcbLayerId::FCu && bottomLayer == QtPcbLayerId::BCu) {
        return QtViaType::Through;
    }
    
    if (isAdjacentLayerPair(topLayer, bottomLayer)) {
        if (topLayer == QtPcbLayerId::FCu || bottomLayer == QtPcbLayerId::BCu) {
            return QtViaType::MicroVia;
        }
    }
    
    return QtViaType::BlindBuried;
}

bool isAdjacentLayerPair(QtPcbLayerId layer1, QtPcbLayerId layer2)
{
    // Simple adjacency check for copper layers
    int l1 = static_cast<int>(layer1);
    int l2 = static_cast<int>(layer2);
    
    // FCu (0) and In1Cu (4) are adjacent, as are BCu (2) and In1Cu (4), etc.
    // The copper layers have IDs: FCu=0, BCu=2, In1Cu=4, In2Cu=6, etc.
    if ((l1 == 0 && l2 == 4) || (l2 == 0 && l1 == 4)) return true;  // FCu <-> In1Cu
    if ((l1 == 2 && l2 == 4) || (l2 == 2 && l1 == 4)) return true;  // BCu <-> In1Cu
    
    // For inner layers, they're adjacent if their IDs differ by 2
    if (l1 >= 4 && l2 >= 4) {
        return std::abs(l1 - l2) == 2;
    }
    
    return false;
}

int getStandardDrillSize(QtViaType type, const QtBoardDesignSettings* settings)
{
    if (!settings) {
        return 0;
    }
    
    switch (type) {
    case QtViaType::Through:
        return settings->getViaDrill();
    case QtViaType::MicroVia:
        return settings->getViaDrill();  // Use standard via drill for now
    case QtViaType::BlindBuried:
        return settings->getViaDrill();
    default:
        return 0;
    }
}

int calculateAnnularRing(const EDA_VIA_DATA* via, QtPcbLayerId layer)
{
    int diameter = via->getWidth(layer);
    int drill = via->getDrillValue();
    
    return (diameter - drill) / 2;
}

bool isThruVia(const EDA_VIA_DATA* via, int copperLayerCount)
{
    return via->getTopLayer() == QtPcbLayerId::FCu && 
           via->getBottomLayer() == QtPcbLayerId::BCu;
}

QMap<QString, QtViaList> groupViasByConnection(const QtViaList& vias)
{
    QMap<QString, QtViaList> groups;
    
    for (EDA_VIA_DATA* via : vias) {
        QString key = QString("%1_%2_%3")
            .arg(static_cast<int>(via->getTopLayer()))
            .arg(static_cast<int>(via->getBottomLayer()))
            .arg(static_cast<int>(via->getViaType()));
        
        groups[key].append(via);
    }
    
    return groups;
}

QtViaList findViasAtPosition(const QtViaList& vias, const QPointF& position, double tolerance)
{
    QtViaList result;
    
    for (EDA_VIA_DATA* via : vias) {
        if (via->hitTest(position, tolerance)) {
            result.append(via);
        }
    }
    
    return result;
}

} // namespace QtViaUtils

// No MOC file needed - standard C++ class