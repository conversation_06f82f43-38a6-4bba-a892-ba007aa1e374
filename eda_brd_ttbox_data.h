#ifndef QT_PCB_TEXTBOX_H
#define QT_PCB_TEXTBOX_H

#include "eda_brd_shape_data.h"
#include "eda_text_data.h"
#include "qt_temporary_implementations.h"
#include <QObject>
#include <QVector>
#include <QSharedPointer>

class EDA_FOOTPRINT_DATA;
class QtKigfxView;
class MsgPanelItem;

/**
 * EDA_BRD_TTBOX_DATA - Qt版本的PCB文本框类
 * 继承自EDA_BRD_SHAPE_DATA和EDA_TEXT_DATA，提供带边框的文本功能
 */
class EDA_BRD_TTBOX_DATA : public EDA_BRD_SHAPE_DATA, public virtual EDA_TEXT_DATA
{

public:
    explicit EDA_BRD_TTBOX_DATA( EDA_BOARD_OBJECT_DATA* aParent,
                                 QtKicadType aType = PCB_TEXTBOX_T );
    virtual ~EDA_BRD_TTBOX_DATA();
    
    // 类型识别
    static bool classOf(const EDA_OBJECT_DATA* aItem);
    bool isType(const QVector<QtKicadType>& aScanTypes) const;
    

    
    // 获取友好名称
    QString getFriendlyName() const override { return "Text Box"; }
    
    // 位置和尺寸管理
    QPoint getTopLeft() const;
    QPoint getBotRight() const;
    void setTop(int aVal);
    void setLeft(int aVal);
    void setRight(int aVal);
    void setBottom(int aVal);
    
    // 从设计设置中应用样式
    void styleFromSettings(const QtBoardDesignSettings& settings) override;
    
    // 边距管理
    int getLegacyTextMargin() const;
    void setMarginLeft(int aLeft);
    void setMarginTop(int aTop);
    void setMarginRight(int aRight);
    void setMarginBottom(int aBottom);
    int getMarginLeft() const { return m_marginLeft; }
    int getMarginTop() const { return m_marginTop; }
    int getMarginRight() const { return m_marginRight; }
    int getMarginBottom() const { return m_marginBottom; }
    
    // 绘制位置
    QPoint getDrawPos() const;
    QPoint getDrawPos(bool aIsFlipped) const;
    
    // 文本角度
    void setTextAngle(const QtEdaAngle& aAngle);
    
    // 显示文本
    QString getShownText(bool aAllowExtraText, int aDepth = 0) const;
    
    // 搜索匹配
    bool matches(const QtEdaSearchData& aSearchData, void* aAuxData) const;
    
    // 获取角点序列
    QVector<QPoint> getCornersInSequence() const;
    
    // 变换操作
    void move(const QPoint& aMoveVector);
    void rotate(const QPoint& aRotCentre, const QtEdaAngle& aAngle);
    void mirror(const QPoint& aCentre, FlipDirection aFlipDirection);
    void flip(const QPoint& aCentre, FlipDirection aFlipDirection);
    
    
    // 碰撞测试
    bool hitTest(const QPoint& aPosition, int aAccuracy) const;
    bool hitTest(const QtBox2I& aRect, bool aContained, int aAccuracy = 0) const;
    
    QString getClass() const override { return QStringLiteral("PCB_TEXTBOX"); }
    
    // 文本转多边形
    void transformTextToPolySet(QtShapePolySet& aBuffer, int aClearance, int aMaxError,
                                ErrorLoc aErrorLoc) const;
    
    void transformShapeToPolygon(QtShapePolySet& aBuffer, PcbLayerId aLayer, int aClearance,
                                 int aMaxError, ErrorLoc aErrorLoc,
                                 bool aIgnoreLineWidth = false) const;
    
    // 获取有效形状
    std::shared_ptr<QtShape> getEffectiveShape(QtPcbLayerId aLayer = QtPcbLayerId::UndefinedLayer,
                                               QtFlashing aFlash = QtFlashing::Default) const;
    
    // 项目描述
    QString getItemDescription(QtUnitsProvider* aUnitsProvider, bool aFull) const;
    
    // 菜单图标
    Bitmaps getMenuImage() const;
    
    
    // 克隆
    EDA_OBJECT_DATA* clone() const;
    
    // 边框管理
    bool isBorderEnabled() const;
    void setBorderEnabled(bool enabled);
    void setBorderWidth(int aSize);
    int getBorderWidth() const;
    
    // 相似度和比较
    double similarity(const EDA_BOARD_OBJECT_DATA& aBoardItem) const;
    bool operator==(const EDA_BRD_TTBOX_DATA& aOther) const;
    bool operator==(const EDA_BOARD_OBJECT_DATA& aBoardItem) const;
    
    // Override methods from base classes
    void getMsgPanelInfo(EDA_BOARD_DATA* aFrame, QVector<QtMsgPanelItem>& aList) const override;
    void viewGetLayers(QVector<int>& layers) const override;
    double viewGetLOD(int aLayer, const QtView* aView) const override;

    
protected:
    // 交换数据
    void swapData(EDA_BOARD_OBJECT_DATA* aImage);
    
    // 获取字体度量
    QFontMetrics getFontMetrics() const;
    
private:
    bool m_borderEnabled;   ///< 是否绘制边框
    int m_marginLeft;       ///< 左边距
    int m_marginTop;        ///< 上边距
    int m_marginRight;      ///< 右边距
    int m_marginBottom;     ///< 下边距
};

#endif // QT_PCB_TEXTBOX_H