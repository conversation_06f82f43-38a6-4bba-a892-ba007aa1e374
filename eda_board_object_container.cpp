/*
 * This program source code file is part of KiCad, a free EDA CAD application.
 *
 * Copyright (C) 2016 CERN
 * Copyright The KiCad Developers, see AUTHORS.txt for contributors.
 * Copyright (C) 2024 KiCad to Qt Migration Project
 *
 * <AUTHOR> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */

#include "eda_board_object_container.h"
#include <QtCore/QVariantMap>
#include <QtCore/QJsonDocument>
#include <QtCore/QJsonObject>
#include <QtCore/QStringBuilder>
#include <algorithm>

Q_LOGGING_CATEGORY(edaBoardObjectContainer, "qt.pcbnew.board.container")

// Forward declaration stubs for classes not yet migrated
struct QtZoneSettings {
    virtual ~QtZoneSettings() = default;
};

// EDA_BOARD_OBJECT_CONTAINER implementation
EDA_BOARD_OBJECT_CONTAINER::EDA_BOARD_OBJECT_CONTAINER(EDA_BOARD_OBJECT_DATA* parent, QtKicadType itemType)
    : EDA_BOARD_OBJECT_DATA(dynamic_cast<EDA_BOARD_OBJECT_CONTAINER*>(parent), itemType)
    , m_isModified(false)
    , m_inBulkOperation(false)
    , m_bulkOperationCounter(0)
{
}

EDA_BOARD_OBJECT_CONTAINER::~EDA_BOARD_OBJECT_CONTAINER() {
}

bool EDA_BOARD_OBJECT_CONTAINER::classOf(const EDA_OBJECT_DATA* item) {
    if (!item) return false;
    
    switch (item->getType()) {
    case QtKicadType::Board:
    case QtKicadType::PcbFootprintT:
    case QtKicadType::PcbTable:
    case QtKicadType::BoardItemContainer:
        return true;
    default:
        return false;
    }
}

void EDA_BOARD_OBJECT_CONTAINER::setModified(bool modified) {
    if (m_isModified == modified) return;
    
    m_isModified = modified;
    if (m_modifiedChangedCallback) {
        m_modifiedChangedCallback(modified);
    }
    
}

void EDA_BOARD_OBJECT_CONTAINER::addItems(const QList<EDA_BOARD_OBJECT_DATA*>& items, QtAddMode mode) {
    if (items.isEmpty()) return;
    
    beginBulkOperation();
    
    // Use bulk mode for efficiency
    QtAddMode bulkMode = (mode == QtAddMode::Insert) ? QtAddMode::BulkInsert : QtAddMode::BulkAppend;
    
    for (EDA_BOARD_OBJECT_DATA* item : items) {
        if (item) {
            addItem(item, bulkMode);
        }
    }
    
    endBulkOperation();
    
}

void EDA_BOARD_OBJECT_CONTAINER::removeItems(const QList<EDA_BOARD_OBJECT_DATA*>& items, QtRemoveMode mode) {
    if (items.isEmpty()) return;
    
    beginBulkOperation();
    
    for (EDA_BOARD_OBJECT_DATA* item : items) {
        if (item && contains(item)) {
            removeItem(item, QtRemoveMode::Bulk);
        }
    }
    
    endBulkOperation();
    
}

// Qt serialization
QVariantMap EDA_BOARD_OBJECT_CONTAINER::toVariantMap() const {
    QVariantMap map = EDA_BOARD_OBJECT_DATA::toVariantMap();
    
    // Container state
    map[QStringLiteral("isModified")] = m_isModified;
    map[QStringLiteral("itemCount")] = getItemCount();
    map[QStringLiteral("isEmpty")] = isEmpty();
    
    return map;
}

void EDA_BOARD_OBJECT_CONTAINER::fromVariantMap(const QVariantMap& map) {
    EDA_BOARD_OBJECT_DATA::fromVariantMap(map);
    
    // Container state
    if (map.contains(QStringLiteral("isModified"))) {
        setModified(map.value(QStringLiteral("isModified")).toBool());
    }
}

QString EDA_BOARD_OBJECT_CONTAINER::toString() const {
    return QStringLiteral("EDA_BOARD_OBJECT_CONTAINER(type=%1, items=%2, modified=%3)")
           .arg(static_cast<int>(getType()))
           .arg(getItemCount())
           .arg(m_isModified);
}

// Protected helper methods
void EDA_BOARD_OBJECT_CONTAINER::notifyItemAdded(EDA_BOARD_OBJECT_DATA* item) {
    if (!item) return;
    
    updateModifiedState();
    
    if (!m_inBulkOperation) {
        if (m_itemAddedCallback) m_itemAddedCallback(item);
        if (m_itemCountChangedCallback) m_itemCountChangedCallback(getItemCount());
    }
    
}

void EDA_BOARD_OBJECT_CONTAINER::notifyItemRemoved(EDA_BOARD_OBJECT_DATA* item) {
    if (!item) return;
    
    updateModifiedState();
    
    if (!m_inBulkOperation) {
        if (m_itemRemovedCallback) m_itemRemovedCallback(item);
        if (m_itemCountChangedCallback) m_itemCountChangedCallback(getItemCount());
    }
    
}

void EDA_BOARD_OBJECT_CONTAINER::updateModifiedState() {
    if (!m_isModified) {
        setModified(true);
    }
}

// Item management (callbacks replace signals/slots)
void EDA_BOARD_OBJECT_CONTAINER::onChildItemModified() {
    updateModifiedState();
}

// Utility functions implementation
namespace QtContainerUtils {

QString getAddModeCanonicalName(QtAddMode mode) {
    switch (mode) {
    case QtAddMode::Insert:     return QStringLiteral("insert");
    case QtAddMode::Append:     return QStringLiteral("append");
    case QtAddMode::BulkAppend: return QStringLiteral("bulk_append");
    case QtAddMode::BulkInsert: return QStringLiteral("bulk_insert");
    default:                    return QStringLiteral("unknown");
    }
}

QtAddMode getAddModeFromCanonicalName(const QString& name) {
    QString lowerName = name.toLower();
    
    if (lowerName == QStringLiteral("insert")) {
        return QtAddMode::Insert;
    } else if (lowerName == QStringLiteral("append")) {
        return QtAddMode::Append;
    } else if (lowerName == QStringLiteral("bulk_append")) {
        return QtAddMode::BulkAppend;
    } else if (lowerName == QStringLiteral("bulk_insert")) {
        return QtAddMode::BulkInsert;
    } else {
        return QtAddMode::Insert; // Default fallback
    }
}

QString getRemoveModeCanonicalName(QtRemoveMode mode) {
    switch (mode) {
    case QtRemoveMode::Normal: return QStringLiteral("normal");
    case QtRemoveMode::Bulk:   return QStringLiteral("bulk");
    default:                   return QStringLiteral("unknown");
    }
}

QtRemoveMode getRemoveModeFromCanonicalName(const QString& name) {
    QString lowerName = name.toLower();
    
    if (lowerName == QStringLiteral("normal")) {
        return QtRemoveMode::Normal;
    } else if (lowerName == QStringLiteral("bulk")) {
        return QtRemoveMode::Bulk;
    } else {
        return QtRemoveMode::Normal; // Default fallback
    }
}

QStringList getAllAddModeNames() {
    return QStringList() 
           << getAddModeCanonicalName(QtAddMode::Insert)
           << getAddModeCanonicalName(QtAddMode::Append)
           << getAddModeCanonicalName(QtAddMode::BulkAppend)
           << getAddModeCanonicalName(QtAddMode::BulkInsert);
}

QStringList getAllRemoveModeNames() {
    return QStringList() 
           << getRemoveModeCanonicalName(QtRemoveMode::Normal)
           << getRemoveModeCanonicalName(QtRemoveMode::Bulk);
}

bool validateContainerOperation(const EDA_BOARD_OBJECT_CONTAINER* container,
                               const EDA_BOARD_OBJECT_DATA* item,
                               QString* error) {
    if (!container) {
        if (error) *error = QStringLiteral("Container is null");
        return false;
    }
    
    if (!item) {
        if (error) *error = QStringLiteral("Item is null");
        return false;
    }
    
    // Check if item can be added to this container type
    // This would be extended based on specific container rules
    if (item->getParent() && item->getParent() != container) {
        if (error) *error = QStringLiteral("Item already belongs to another container");
        return false;
    }
    
    return true;
}

} // namespace QtContainerUtils

