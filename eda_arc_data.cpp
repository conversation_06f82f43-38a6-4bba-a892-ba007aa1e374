/*
 * Qt-based reimplementation of KiCad PCB_ARC class implementation
 */

#include "eda_arc_data.h"
#include "eda_board_data.h"
#include <QtCore/QLoggingCategory>
#include <QtCore/QtMath>
#include <QtCore/QDebug>
#include <QtCore/QJsonObject>
#include <QtCore/QJsonDocument>
#include <QtGui/QTransform>
#include <limits>

// Logging category for PCB arc operations
Q_LOGGING_CATEGORY(qtPcbArc, "qt.pcbnew.pcb_arc")

// Forward declarations removed - using implementations from qt_temporary_implementations.h

//=============================================================================
// CONSTRUCTION AND DESTRUCTION
//=============================================================================

EDA_ARC_DATA::EDA_ARC_DATA(EDA_BOARD_OBJECT_DATA* parent)
    : EDA_TRACK_DATA(parent, QtKicadType::PcbArcT)
    , m_mid()
{
}

EDA_ARC_DATA::EDA_ARC_DATA(EDA_BOARD_OBJECT_DATA* parent, const QtShapeArc* arc)
    : EDA_TRACK_DATA(parent, QtKicadType::PcbArcT)
{
    if (arc) {
        // QtShapeArc uses different interface - basic implementation
        setStart(arc->getStart());
        // Calculate end point from center, start, and angle
        QPointF center = arc->getCenter();
        QPointF start = arc->getStart();
        double angle = arc->getAngle();
        
        double rad = qDegreesToRadians(angle);
        QPointF offset = start - center;
        double cosA = qCos(rad);
        double sinA = qSin(rad);
        QPointF rotated(offset.x() * cosA - offset.y() * sinA,
                       offset.x() * sinA + offset.y() * cosA);
        setEnd(center + rotated);
        
        // Calculate mid point - approximate as midpoint of arc
        double midAngle = angle / 2.0;
        double midRad = qDegreesToRadians(midAngle);
        double cosMid = qCos(midRad);
        double sinMid = qSin(midRad);
        QPointF midRotated(offset.x() * cosMid - offset.y() * sinMid,
                          offset.x() * sinMid + offset.y() * cosMid);
        m_mid = center + midRotated;
    }
}

EDA_ARC_DATA::EDA_ARC_DATA(const EDA_ARC_DATA& other, QObject* parent)
    : EDA_TRACK_DATA(other)
    , m_mid(other.m_mid)
{
}

EDA_ARC_DATA::~EDA_ARC_DATA()
{
}

EDA_ARC_DATA& EDA_ARC_DATA::operator=(const EDA_ARC_DATA& other)
{
    if (this != &other) {
        EDA_TRACK_DATA::operator=(other);
        m_mid = other.m_mid;
        m_geometryDirty = true;
    }
    return *this;
}

//=============================================================================
// TYPE IDENTIFICATION
//=============================================================================

bool EDA_ARC_DATA::classOf(const EDA_OBJECT_DATA* item)
{
    return item && item->getType() == QtKicadType::PcbArcT;
}

//=============================================================================
// GEOMETRIC PROPERTIES
//=============================================================================

void EDA_ARC_DATA::setMid(const QPointF& mid)
{
    if (m_mid != mid) {
        m_mid = mid;
        m_geometryDirty = true;
        clearShapeCache();
    }
}

QPointF EDA_ARC_DATA::getPosition() const
{
    return calcArcCenter(getStart(), m_mid, getEnd());
}

//=============================================================================
// ARC SPECIFIC CALCULATIONS
//=============================================================================

qreal EDA_ARC_DATA::getRadius() const
{
    updateCachedGeometry();
    QMutexLocker locker(&m_geometryCacheMutex);
    return m_cachedGeometry.radius;
}

qreal EDA_ARC_DATA::getAngle() const
{
    updateCachedGeometry();
    QMutexLocker locker(&m_geometryCacheMutex);
    return m_cachedGeometry.arcAngle;
}

qreal EDA_ARC_DATA::getArcAngleStart() const
{
    updateCachedGeometry();
    QMutexLocker locker(&m_geometryCacheMutex);
    return m_cachedGeometry.startAngle;
}

qreal EDA_ARC_DATA::getArcAngleEnd() const
{
    updateCachedGeometry();
    QMutexLocker locker(&m_geometryCacheMutex);
    return m_cachedGeometry.endAngle;
}

bool EDA_ARC_DATA::isCCW() const
{
    updateCachedGeometry();
    QMutexLocker locker(&m_geometryCacheMutex);
    return m_cachedGeometry.isCCW;
}

bool EDA_ARC_DATA::isDegenerated(int threshold) const
{
    // Check if arc is too small to be meaningful
    qreal dist1 = QLineF(getStart(), m_mid).length();
    qreal dist2 = QLineF(m_mid, getEnd()).length();
    
    return dist1 < threshold || dist2 < threshold;
}

QtArcGeometry EDA_ARC_DATA::getArcGeometry() const
{
    updateCachedGeometry();
    QMutexLocker locker(&m_geometryCacheMutex);
    return m_cachedGeometry;
}

//=============================================================================
// LENGTH CALCULATION
//=============================================================================

double EDA_ARC_DATA::getLength() const
{
    updateCachedGeometry();
    QMutexLocker locker(&m_geometryCacheMutex);
    
    if (!m_cachedGeometry.isValid()) {
        return 0.0;
    }
    
    // Arc length = radius * angle (in radians)
    return m_cachedGeometry.radius * qAbs(qDegreesToRadians(m_cachedGeometry.arcAngle));
}

//=============================================================================
// TRANSFORMATIONS
//=============================================================================

void EDA_ARC_DATA::move(const QPointF& moveVector)
{
    EDA_TRACK_DATA::move(moveVector);
    m_mid += moveVector;
    m_geometryDirty = true;
}

void EDA_ARC_DATA::rotate(const QPointF& rotCenter, qreal angle)
{
    EDA_TRACK_DATA::rotate(rotCenter, angle);
    
    // Rotate mid point
    QTransform transform;
    transform.translate(rotCenter.x(), rotCenter.y());
    transform.rotate(angle);
    transform.translate(-rotCenter.x(), -rotCenter.y());
    
    m_mid = transform.map(m_mid);
    m_geometryDirty = true;
}

void EDA_ARC_DATA::mirror(const QPointF& center, QtFlipDirection flipDirection)
{
    EDA_TRACK_DATA::mirror(center, flipDirection);
    
    // Mirror mid point
    if (flipDirection == QtFlipDirection::LeftRight) {
        m_mid.setX(2 * center.x() - m_mid.x());
    } else {
        m_mid.setY(2 * center.y() - m_mid.y());
    }
    
    m_geometryDirty = true;
}

void EDA_ARC_DATA::flip(const QPointF& center, QtFlipDirection flipDirection)
{
    mirror(center, flipDirection);
    
    // Flip layer if board is available
    if (getBoard()) {
        QtPcbLayerId flippedLayer = getBoard()->flipLayer(getLayer());
        setLayer(flippedLayer);
    }
}

//=============================================================================
// SHAPE GENERATION
//=============================================================================

std::shared_ptr<QtShape> EDA_ARC_DATA::getEffectiveShape(QtPcbLayerId layer, QtFlashing flash) const
{
    int width = getWidth();
    
    if (EDA_BOARD_OBJECT_DATA::isSolderMaskLayer(layer)) {
        width += 2 * getSolderMaskExpansion();
    }
    
    // Create arc shape with the calculated width
    updateCachedGeometry();
    QMutexLocker locker(&m_geometryCacheMutex);
    
    if (!m_cachedGeometry.isValid()) {
        // Fallback to straight segment if arc is degenerated
        return std::static_pointer_cast<QtShape>(std::make_shared<QtShapeSegment>(getStart(), getEnd(), width));
    }
    
    // TODO: Create QtShapeArc when available
    // For now, approximate with segments
    return std::static_pointer_cast<QtShape>(std::make_shared<QtShapeSegment>(getStart(), getEnd(), width));
}

//=============================================================================
// HIT TESTING
//=============================================================================

bool EDA_ARC_DATA::hitTest(const QPointF& position, double accuracy) const
{
    double maxDist = accuracy + (getWidth() / 2.0);
    
    // Quick check: endpoints
    if (getStart().manhattanLength() <= maxDist || getEnd().manhattanLength() <= maxDist) {
        return true;
    }
    
    // Check if point is on the arc
    updateCachedGeometry();
    QMutexLocker locker(&m_geometryCacheMutex);
    
    if (!m_cachedGeometry.isValid()) {
        // Fallback to line segment test
        return EDA_TRACK_DATA::hitTest(position, accuracy);
    }
    
    return isPointOnArc(position, m_cachedGeometry, maxDist);
}

bool EDA_ARC_DATA::hitTest(const QRectF& rect, bool contained, int accuracy) const
{
    QRectF testRect = rect.adjusted(-accuracy, -accuracy, accuracy, accuracy);
    
    if (contained) {
        // Check if entire arc is contained
        QRectF bbox = getBoundingBox();
        return testRect.contains(bbox);
    } else {
        // Check if arc intersects with rectangle
        // Start with simple bounding box test
        if (!testRect.intersects(getBoundingBox())) {
            return false;
        }
        
        // TODO: Implement precise arc-rectangle intersection
        // For now, check endpoints and midpoint
        return testRect.contains(getStart()) || 
               testRect.contains(m_mid) || 
               testRect.contains(getEnd());
    }
}

//=============================================================================
// BOUNDING BOX
//=============================================================================

QRectF EDA_ARC_DATA::getBoundingBox() const
{
    updateCachedGeometry();
    QMutexLocker locker(&m_geometryCacheMutex);
    
    if (!m_cachedGeometry.isValid()) {
        return EDA_TRACK_DATA::getBoundingBox();
    }
    
    // Start with endpoints
    qreal minX = qMin(getStart().x(), getEnd().x());
    qreal maxX = qMax(getStart().x(), getEnd().x());
    qreal minY = qMin(getStart().y(), getEnd().y());
    qreal maxY = qMax(getStart().y(), getEnd().y());
    
    // Include mid point
    minX = qMin(minX, m_mid.x());
    maxX = qMax(maxX, m_mid.x());
    minY = qMin(minY, m_mid.y());
    maxY = qMax(maxY, m_mid.y());
    
    // Check if arc includes cardinal points (0°, 90°, 180°, 270°)
    QPointF center = m_cachedGeometry.center;
    qreal radius = m_cachedGeometry.radius;
    qreal startAngle = m_cachedGeometry.startAngle;
    qreal endAngle = m_cachedGeometry.endAngle;
    
    auto includesAngle = [=](qreal angle) {
        if (m_cachedGeometry.isCCW) {
            if (startAngle <= endAngle) {
                return angle >= startAngle && angle <= endAngle;
            } else {
                return angle >= startAngle || angle <= endAngle;
            }
        } else {
            if (startAngle >= endAngle) {
                return angle <= startAngle && angle >= endAngle;
            } else {
                return angle <= startAngle || angle >= endAngle;
            }
        }
    };
    
    if (includesAngle(0.0)) {
        maxX = qMax(maxX, center.x() + radius);
    }
    if (includesAngle(90.0)) {
        maxY = qMax(maxY, center.y() + radius);
    }
    if (includesAngle(180.0)) {
        minX = qMin(minX, center.x() - radius);
    }
    if (includesAngle(270.0)) {
        minY = qMin(minY, center.y() - radius);
    }
    
    // Add track width
    int halfWidth = getWidth() / 2;
    return QRectF(minX - halfWidth, minY - halfWidth,
                  maxX - minX + getWidth(), maxY - minY + getWidth());
}

//=============================================================================
// UI AND DESCRIPTION
//=============================================================================

QString EDA_ARC_DATA::getItemDescription(bool full) const
{
    QString desc = QStringLiteral("Track (arc)");
    
    if (full) {
        updateCachedGeometry();
        QMutexLocker locker(&m_geometryCacheMutex);
        
        desc += QString(" [R:%1, ∠:%2°]")
                .arg(m_cachedGeometry.radius, 0, 'f', 2)
                .arg(m_cachedGeometry.arcAngle, 0, 'f', 1);
    }
    
    return desc;
}

//=============================================================================
// COMPARISON AND DUPLICATION
//=============================================================================

EDA_BOARD_OBJECT_DATA* EDA_ARC_DATA::clone() const
{
    return new EDA_ARC_DATA(*this);
}

double EDA_ARC_DATA::similarity(const EDA_BOARD_OBJECT_DATA& other) const
{
    if (other.getType() != getType()) {
        return 0.0;
    }
    
    const EDA_ARC_DATA& otherArc = static_cast<const EDA_ARC_DATA&>(other);
    
    double similarity = EDA_TRACK_DATA::similarity(other);
    
    // Add arc-specific similarity checks
    if (m_mid == otherArc.m_mid) {
        similarity *= 1.0;
    } else {
        qreal distance = QLineF(m_mid, otherArc.m_mid).length();
        similarity *= qExp(-distance / 1000.0); // Decay factor
    }
    
    return similarity;
}

bool EDA_ARC_DATA::operator==(const EDA_BOARD_OBJECT_DATA& other) const
{
    if (other.getType() != getType()) {
        return false;
    }
    
    return operator==(static_cast<const EDA_ARC_DATA&>(other));
}

bool EDA_ARC_DATA::operator==(const EDA_ARC_DATA& other) const
{
    return EDA_TRACK_DATA::operator==(other) && m_mid == other.m_mid;
}

//=============================================================================
// QT SERIALIZATION
//=============================================================================

QVariantMap EDA_ARC_DATA::toVariantMap() const
{
    QVariantMap map = EDA_TRACK_DATA::toVariantMap();
    
    // Add arc-specific data
    map["mid"] = QVariantMap{
        {"x", m_mid.x()},
        {"y", m_mid.y()}
    };
    
    // Include calculated geometry for debugging
    updateCachedGeometry();
    QMutexLocker locker(&m_geometryCacheMutex);
    
    QVariantMap geometry;
    geometry["center"] = QVariantMap{
        {"x", m_cachedGeometry.center.x()},
        {"y", m_cachedGeometry.center.y()}
    };
    geometry["radius"] = m_cachedGeometry.radius;
    geometry["startAngle"] = m_cachedGeometry.startAngle;
    geometry["endAngle"] = m_cachedGeometry.endAngle;
    geometry["arcAngle"] = m_cachedGeometry.arcAngle;
    geometry["isCCW"] = m_cachedGeometry.isCCW;
    
    map["geometry"] = geometry;
    
    return map;
}

void EDA_ARC_DATA::fromVariantMap(const QVariantMap& map)
{
    EDA_TRACK_DATA::fromVariantMap(map);
    
    // Restore arc-specific data
    if (map.contains("mid")) {
        QVariantMap midMap = map["mid"].toMap();
        m_mid = QPointF(midMap["x"].toDouble(), midMap["y"].toDouble());
    }
    
    m_geometryDirty = true;
}

//=============================================================================
// PROTECTED HELPER METHODS
//=============================================================================

void EDA_ARC_DATA::swapData(EDA_BOARD_OBJECT_DATA* other)
{
    EDA_ARC_DATA* otherArc = dynamic_cast<EDA_ARC_DATA*>(other);
    if (!otherArc) {
        qCWarning(qtPcbArc) << "Cannot swap data with non-arc item";
        return;
    }
    
    EDA_TRACK_DATA::swapData(other);
    qSwap(m_mid, otherArc->m_mid);
    m_geometryDirty = true;
    otherArc->m_geometryDirty = true;
}

QPointF EDA_ARC_DATA::calcArcCenter(const QPointF& start, const QPointF& mid, const QPointF& end)
{
    return QtPcbArcUtils::calculateArcCenter(start, mid, end);
}

bool EDA_ARC_DATA::isPointOnArc(const QPointF& point, const QtArcGeometry& geometry, double tolerance)
{
    return QtPcbArcUtils::isPointOnArc(point, geometry, tolerance);
}

void EDA_ARC_DATA::updateCachedGeometry() const
{
    QMutexLocker locker(&m_geometryCacheMutex);
    
    if (!m_geometryDirty) {
        return;
    }
    
    m_cachedGeometry = QtPcbArcUtils::calculateArcGeometry(getStart(), m_mid, getEnd());
    m_geometryDirty = false;
}

//=============================================================================
// UTILITY NAMESPACE IMPLEMENTATION
//=============================================================================

namespace QtPcbArcUtils {

QPointF calculateArcCenter(const QPointF& start, const QPointF& mid, const QPointF& end)
{
    // Calculate arc center using three points
    // Based on circumcenter calculation
    
    qreal ax = start.x();
    qreal ay = start.y();
    qreal bx = mid.x();
    qreal by = mid.y();
    qreal cx = end.x();
    qreal cy = end.y();
    
    qreal d = 2 * (ax * (by - cy) + bx * (cy - ay) + cx * (ay - by));
    
    if (qFuzzyIsNull(d)) {
        // Points are collinear
        qCWarning(qtPcbArc) << "Cannot calculate arc center: points are collinear";
        return QPointF();
    }
    
    qreal ux = ((ax * ax + ay * ay) * (by - cy) + 
                (bx * bx + by * by) * (cy - ay) + 
                (cx * cx + cy * cy) * (ay - by)) / d;
    
    qreal uy = ((ax * ax + ay * ay) * (cx - bx) + 
                (bx * bx + by * by) * (ax - cx) + 
                (cx * cx + cy * cy) * (bx - ax)) / d;
    
    return QPointF(ux, uy);
}

QtArcGeometry calculateArcGeometry(const QPointF& start, const QPointF& mid, const QPointF& end)
{
    QtArcGeometry geometry;
    
    // Calculate center
    geometry.center = calculateArcCenter(start, mid, end);
    
    if (geometry.center.isNull()) {
        return geometry; // Invalid geometry
    }
    
    // Calculate radius
    geometry.radius = QLineF(geometry.center, start).length();
    
    // Calculate angles
    QLineF startLine(geometry.center, start);
    QLineF midLine(geometry.center, mid);
    QLineF endLine(geometry.center, end);
    
    geometry.startAngle = normalizeAngle(startLine.angle());
    qreal midAngle = normalizeAngle(midLine.angle());
    geometry.endAngle = normalizeAngle(endLine.angle());
    
    // Determine direction (CCW or CW)
    // Use cross product to determine orientation
    QPointF v1 = mid - start;
    QPointF v2 = end - mid;
    qreal cross = v1.x() * v2.y() - v1.y() * v2.x();
    
    geometry.isCCW = cross > 0;
    
    // Calculate arc angle
    if (geometry.isCCW) {
        geometry.arcAngle = geometry.endAngle - geometry.startAngle;
        if (geometry.arcAngle < 0) {
            geometry.arcAngle += 360.0;
        }
    } else {
        geometry.arcAngle = geometry.startAngle - geometry.endAngle;
        if (geometry.arcAngle < 0) {
            geometry.arcAngle += 360.0;
        }
        geometry.arcAngle = -geometry.arcAngle; // Negative for CW
    }
    
    return geometry;
}

bool isValidArc(const QPointF& start, const QPointF& mid, const QPointF& end, qreal tolerance)
{
    // Check if points are not collinear
    QLineF line1(start, mid);
    QLineF line2(mid, end);
    
    if (line1.length() < tolerance || line2.length() < tolerance) {
        return false;
    }
    
    // Check angle between segments
    qreal angle = qAbs(line1.angleTo(line2));
    return angle > tolerance && angle < (180.0 - tolerance);
}

QVector<QPointF> arcToPolyline(const QtArcGeometry& geometry, qreal maxError)
{
    QVector<QPointF> points;
    
    if (!geometry.isValid()) {
        return points;
    }
    
    int segmentCount = calculateSegmentCount(geometry.radius, qAbs(geometry.arcAngle), maxError);
    qreal angleStep = geometry.arcAngle / segmentCount;
    
    points.reserve(segmentCount + 1);
    
    for (int i = 0; i <= segmentCount; ++i) {
        qreal angle = geometry.startAngle + i * angleStep;
        qreal rad = qDegreesToRadians(angle);
        
        QPointF point(
            geometry.center.x() + geometry.radius * qCos(rad),
            geometry.center.y() + geometry.radius * qSin(rad)
        );
        
        points.append(point);
    }
    
    return points;
}

int calculateSegmentCount(qreal radius, qreal angle, qreal maxError)
{
    if (radius <= 0 || angle <= 0 || maxError <= 0) {
        return 1;
    }
    
    // Calculate segments based on maximum chord deviation
    qreal alpha = 2 * qAcos(1 - maxError / radius);
    int segments = qCeil(qDegreesToRadians(angle) / alpha);
    
    return qMax(1, segments);
}

QVector<QPointF> arcLineIntersection(const QtArcGeometry& arc, const QLineF& line)
{
    QVector<QPointF> intersections;
    
    if (!arc.isValid()) {
        return intersections;
    }
    
    // Convert to parametric form
    QPointF p1 = line.p1();
    QPointF p2 = line.p2();
    QPointF d = p2 - p1;
    QPointF f = p1 - arc.center;
    
    qreal a = QPointF::dotProduct(d, d);
    qreal b = 2 * QPointF::dotProduct(f, d);
    qreal c = QPointF::dotProduct(f, f) - arc.radius * arc.radius;
    
    qreal discriminant = b * b - 4 * a * c;
    
    if (discriminant < 0) {
        return intersections; // No intersection
    }
    
    qreal sqrt_discriminant = qSqrt(discriminant);
    qreal t1 = (-b - sqrt_discriminant) / (2 * a);
    qreal t2 = (-b + sqrt_discriminant) / (2 * a);
    
    // Check if intersections are on the line segment and arc
    auto checkPoint = [&](qreal t) {
        if (t >= 0 && t <= 1) {
            QPointF point = p1 + t * d;
            if (isPointOnArc(point, arc, 1e-6)) {
                intersections.append(point);
            }
        }
    };
    
    checkPoint(t1);
    if (discriminant > 0) { // Two distinct intersections
        checkPoint(t2);
    }
    
    return intersections;
}

bool isPointOnArc(const QPointF& point, const QtArcGeometry& arc, qreal tolerance)
{
    if (!arc.isValid()) {
        return false;
    }
    
    // Check if point is on the circle
    qreal distance = QLineF(arc.center, point).length();
    if (qAbs(distance - arc.radius) > tolerance) {
        return false;
    }
    
    // Check if point is within arc angle range
    QLineF line(arc.center, point);
    qreal pointAngle = normalizeAngle(line.angle());
    
    qreal startAngle = arc.startAngle;
    qreal endAngle = arc.endAngle;
    
    if (arc.isCCW) {
        if (startAngle <= endAngle) {
            return pointAngle >= startAngle && pointAngle <= endAngle;
        } else {
            return pointAngle >= startAngle || pointAngle <= endAngle;
        }
    } else {
        if (startAngle >= endAngle) {
            return pointAngle <= startAngle && pointAngle >= endAngle;
        } else {
            return pointAngle <= startAngle || pointAngle >= endAngle;
        }
    }
}

qreal getTangentAngle(const QtArcGeometry& arc, const QPointF& point)
{
    if (!arc.isValid()) {
        return 0.0;
    }
    
    QLineF radial(arc.center, point);
    qreal radialAngle = radial.angle();
    
    // Tangent is perpendicular to radial
    if (arc.isCCW) {
        return normalizeAngle(radialAngle + 90.0);
    } else {
        return normalizeAngle(radialAngle - 90.0);
    }
}

QPair<EDA_ARC_DATA*, EDA_ARC_DATA*> splitArc(const EDA_ARC_DATA* arc, const QPointF& splitPoint)
{
    if (!arc || !isPointOnArc(splitPoint, arc->getArcGeometry(), 1.0)) {
        return QPair<EDA_ARC_DATA*, EDA_ARC_DATA*>(nullptr, nullptr);
    }
    
    // Create two new arcs
    EDA_ARC_DATA* arc1 = new EDA_ARC_DATA();
    EDA_ARC_DATA* arc2 = new EDA_ARC_DATA();
    
    // Copy properties
    arc1->setLayer(arc->getLayer());
    arc1->setWidth(arc->getWidth());
    arc1->setNet(arc->getNet());
    
    arc2->setLayer(arc->getLayer());
    arc2->setWidth(arc->getWidth());
    arc2->setNet(arc->getNet());
    
    // Set geometry
    arc1->setStart(arc->getStart());
    arc1->setEnd(splitPoint);
    
    arc2->setStart(splitPoint);
    arc2->setEnd(arc->getEnd());
    
    // Calculate mid points
    // TODO: Implement proper mid point calculation for split arcs
    
    return QPair<EDA_ARC_DATA*, EDA_ARC_DATA*>(arc1, arc2);
}

EDA_ARC_DATA* mergeArcs(const EDA_ARC_DATA* arc1, const EDA_ARC_DATA* arc2, qreal angleTolerance)
{
    if (!arc1 || !arc2) {
        return nullptr;
    }
    
    // Check if arcs are connected
    bool connected = false;
    QPointF start, mid, end;
    
    if (arc1->getEnd() == arc2->getStart()) {
        start = arc1->getStart();
        mid = arc1->getEnd(); // Connection point
        end = arc2->getEnd();
        connected = true;
    } else if (arc2->getEnd() == arc1->getStart()) {
        start = arc2->getStart();
        mid = arc2->getEnd(); // Connection point
        end = arc1->getEnd();
        connected = true;
    }
    
    if (!connected) {
        return nullptr;
    }
    
    // Check if tangents match at connection point
    QtArcGeometry geom1 = arc1->getArcGeometry();
    QtArcGeometry geom2 = arc2->getArcGeometry();
    
    qreal tangent1 = getTangentAngle(geom1, mid);
    qreal tangent2 = getTangentAngle(geom2, mid);
    
    qreal angleDiff = qAbs(angularDistance(tangent1, tangent2));
    
    if (angleDiff > angleTolerance) {
        return nullptr; // Tangents don't match
    }
    
    // Create merged arc
    EDA_ARC_DATA* mergedArc = new EDA_ARC_DATA();
    mergedArc->setStart(start);
    mergedArc->setEnd(end);
    // TODO: Calculate proper mid point for merged arc
    
    // Copy common properties
    mergedArc->setLayer(arc1->getLayer());
    mergedArc->setWidth(arc1->getWidth());
    mergedArc->setNet(arc1->getNet());
    
    return mergedArc;
}

qreal normalizeAngle(qreal angle)
{
    while (angle < 0) {
        angle += 360.0;
    }
    while (angle >= 360.0) {
        angle -= 360.0;
    }
    return angle;
}

qreal angularDistance(qreal angle1, qreal angle2)
{
    qreal diff = angle2 - angle1;
    
    while (diff > 180.0) {
        diff -= 360.0;
    }
    while (diff < -180.0) {
        diff += 360.0;
    }
    
    return diff;
}

} // namespace QtPcbArcUtils