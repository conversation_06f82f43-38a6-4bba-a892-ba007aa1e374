/*
 * Qt-based reimplementation of KiCad PCB_SHAPE class
 * 
 * Copyright The KiCad Developers
 * Copyright (C) 2024 KiCad to Qt Migration Project
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 */

#pragma once

#include "eda_board_connected_object.h"
#include "eda_shape_data.h"
#include <QtCore/QMutex>
#include <QtCore/QVariantMap>
#include <QtCore/QJsonObject>
#include <QtCore/QPointF>
#include <QtCore/QRectF>
#include <QtCore/QVector>
#include <memory>
#include <optional>

// Forward declarations for dependencies not in migration scope
class QtLineReader;
class QtEdaDrawFrame;
class EDA_FOOTPRINT_DATA;
class QtMsgPanelItem;
// QtLayerSet is already included via eda_board_object_data.h
class QtShape;
class QtShapePolySet;
class QtBoardDesignSettings;
class QtKigfxView;

// QtErrorLoc is defined in eda_board_object_data.h

// Qt bitmaps enum for menu images
enum class QtBitmaps : int;

/**
 * @brief Qt-based PCB shape class
 * 
 * PCB_SHAPE represents a graphical shape on a PCB that has both geometric
 * properties (from EDA_SHAPE_DATA) and connection properties (from EDA_BOARD_CONNECTED_OBJECT).
 * This includes segments, rectangles, arcs, circles, polygons, and bezier curves
 * that can be connected to nets.
 * 
 * This class uses multiple inheritance to combine the functionality of both
 * parent classes while maintaining Qt's signal/slot system and property system.
 */
class EDA_BRD_SHAPE_DATA : public EDA_BOARD_CONNECTED_OBJECT, public EDA_SHAPE_DATA
{

public:
    //==========================================================================
    // CONSTRUCTION AND DESTRUCTION
    //==========================================================================
    explicit EDA_BRD_SHAPE_DATA(EDA_BOARD_OBJECT_DATA* parent, QtKicadType itemType, QtShapeType shapeType);
    explicit EDA_BRD_SHAPE_DATA(EDA_BOARD_OBJECT_DATA* parent = nullptr, QtShapeType shapeType = QtShapeType::Segment);
    
    // Copy constructor
    EDA_BRD_SHAPE_DATA(const EDA_BRD_SHAPE_DATA& other);
    
    ~EDA_BRD_SHAPE_DATA() override;
    
    // Type checking
    static bool classOf(const EDA_OBJECT_DATA* item);
    QString getClassName() const override { return QString("PCB_SHAPE"); }
    QString getClass() const override { return QString("PCB_SHAPE"); }
    
    //==========================================================================
    // SERIALIZATION
    //==========================================================================
    void serialize(QVariantMap& container) const;
    bool deserialize(const QVariantMap& container);
    
    //==========================================================================
    // CONNECTION AND TYPE CHECKING
    //==========================================================================
    bool isConnected() const override;
    QString getFriendlyName() const override;
    bool isType(const QVector<QtKicadType>& scanTypes) const override;
    
    //==========================================================================
    // LAYER MANAGEMENT
    //==========================================================================
    void setLayer(QtPcbLayerId layer) override;
    QtPcbLayerId getLayer() const { return m_layer; }
    bool isOnLayer(QtPcbLayerId layer) const override;
    QtLayerSet getLayerSet() const override;
    void setLayerSet(const QtLayerSet& layers) override;
    
    //==========================================================================
    // POSITION AND GEOMETRY
    //==========================================================================
    void setPosition(const QPointF& pos) override;
    QPointF getPosition() const override;
    QPointF getCenter() const override;
    
    /**
     * @return Connection points where this shape can form electrical connections
     */
    QVector<QPointF> getConnectionPoints() const;
    
    /**
     * @return Visual center for shapes - on outline for unfilled shapes
     */
    QPointF getFocusPosition() const override;
    
    /**
     * @return 4 corners for rectangles/rotated rectangles
     */
    virtual QVector<QPointF> getCorners() const;
    
    //==========================================================================
    // STROKE AND APPEARANCE
    //==========================================================================
    bool hasLineStroke() const override { return true; }
    QtStrokeParams getStroke() const override;
    void setStroke(const QtStrokeParams& stroke) override;
    int getWidth() const;
    void styleFromSettings(const QtBoardDesignSettings& settings) override;
    
    //==========================================================================
    // SOLDER MASK MANAGEMENT
    //==========================================================================
    void setHasSolderMask(bool val) { m_hasSolderMask = val; }
    bool hasSolderMask() const { return m_hasSolderMask; }
    
    void setLocalSolderMaskMargin(std::optional<int> margin);
    std::optional<int> getLocalSolderMaskMargin() const { return m_solderMaskMargin; }
    int getSolderMaskExpansion() const;
    
    //==========================================================================
    // PROXY ITEM MANAGEMENT (Override both parent classes)
    //==========================================================================
    bool isProxyItem() const;
    void setIsProxyItem(bool isProxy = true);
    
    //==========================================================================
    // INFORMATION AND UI
    //==========================================================================
    QString getItemDescription(QtUnitsProvider* unitsProvider, bool full) const override;
    QtBitmaps getMenuImage() const override;
    
    //==========================================================================
    // GEOMETRIC OPERATIONS
    //==========================================================================
    QRectF getBoundingBox() const override;
    
    bool hitTest(const QPointF& position, double accuracy = 0.0) const override;
    bool hitTest(const QRectF& rect, bool contained, double accuracy = 0.0) const override;
    
    void normalize() override;
    
    /**
     * Normalize coordinates for comparison (also normalizes segment endpoints)
     */
    void normalizeForCompare() override;
    
    //==========================================================================
    // TRANSFORMATIONS
    //==========================================================================
    void move(const QPointF& moveVector) override;
    void rotate(const QPointF& rotCenter, double angleDegrees) override;
    void flip(const QPointF& center, QtFlipDirection direction) override;
    virtual void mirror(const QPointF& center, QtFlipDirection direction) override;
    void scale(double scaleFactor);
    
    //==========================================================================
    // SHAPE OPERATIONS
    //==========================================================================
    std::shared_ptr<QtShape> getEffectiveShape(QtPcbLayerId layer = QtPcbLayerId::UndefinedLayer,
                                              QtFlashing flash = QtFlashing::Default) const override;
    
    /**
     * Convert shape to closed polygon
     * @param buffer Output polygon
     * @param layer PCB layer
     * @param clearance Clearance around shape
     * @param maxError Maximum deviation from true arc
     * @param errorLoc Error location (inside/outside)
     * @param ignoreLineWidth For edge cuts where line width is visual only
     */
    void transformShapeToPolygon(QtShapePolySet& buffer, QtPcbLayerId layer, 
                                int clearance, int maxError, QtErrorLoc errorLoc,
                                bool ignoreLineWidth = false) const;
    
    //==========================================================================
    // CLONING AND COMPARISON
    //==========================================================================
    EDA_OBJECT_DATA* clone() const override;
    
    double similarity(const EDA_BOARD_OBJECT_DATA& other) const override;
    bool operator==(const EDA_BRD_SHAPE_DATA& other) const;
    bool operator==(const EDA_BOARD_OBJECT_DATA& other) const override;
    
    
#if defined(DEBUG)
    void show(int nestLevel, std::ostream& os) const override;
#endif


protected:
    //==========================================================================
    // PROTECTED METHODS
    //==========================================================================
    void swapData(EDA_BOARD_OBJECT_DATA* image) override;
    
    /**
     * Comparison functor for drawings
     */
    struct CmpDrawings {
        bool operator()(const EDA_BOARD_OBJECT_DATA* first, const EDA_BOARD_OBJECT_DATA* second) const;
    };

private:
    //==========================================================================
    // PRIVATE DATA MEMBERS
    //==========================================================================
    
    // Layer information
    QtPcbLayerId m_layer;
    
    // Solder mask properties
    bool m_hasSolderMask;
    std::optional<int> m_solderMaskMargin;
    
    // Thread safety
    mutable QMutex m_mutex;
    
    // Cached data for performance
    mutable QRectF m_cachedBBox;
    mutable bool m_bboxValid = false;
    mutable QVector<QPointF> m_cachedConnectionPoints;
    mutable bool m_connectionPointsValid = false;
    
    //==========================================================================
    // PRIVATE HELPER METHODS
    //==========================================================================
    void invalidateCache();
    void updateCachedBBox() const;
    void updateCachedConnectionPoints() const;
    
    // Helper to resolve property conflicts from multiple inheritance
    void syncProperties();
};

//=============================================================================
// HELPER FUNCTIONS
//=============================================================================

/**
 * @brief Create a new EDA_BRD_SHAPE_DATA clone
 */
inline EDA_BRD_SHAPE_DATA* newClone(const EDA_BRD_SHAPE_DATA& shape) {
    return dynamic_cast<EDA_BRD_SHAPE_DATA*>(shape.clone());
}
