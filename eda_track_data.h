/*
 * Qt-based reimplementation of KiCad PCB_TRACK class
 * 
 * This class represents a PCB track segment using Qt frameworks.
 * It provides comprehensive track management including geometry,
 * width properties, solder mask configuration, and shape transformations.
 */

#pragma once

#include "eda_board_connected_object.h"
#include <QtCore/QString>
#include <QtCore/QPointF>
#include <QtCore/QLineF>
#include <QtCore/QRectF>
#include <QtCore/QLoggingCategory>
#include <QtCore/QMutex>
#include <QtCore/QVariantMap>
#include <QtCore/QHash>
#include <optional>
#include <memory>
#include <functional>

Q_DECLARE_LOGGING_CATEGORY(eDA_TRACK_DATA)

// Forward declarations for dependencies not in migration scope
class QtShapeSegment;
class QtShapeCompound;
class QtShapePolySet;
class QtShapeArc;
class QtMsgPanelItem;
class QtProgressReporter;

/**
 * @brief Endpoint enumeration for track operations
 */
enum class QtEndpointType : int {
    Start = 0,
    End = 1
};

/**
 * @brief Via type enumeration (synchronized with GAL_LAYER_ID)
 */
enum class QtViaType : int {
    Through = 3,      ///< Through hole via
    BlindBuried = 2,  ///< Via on internal layers
    MicroVia = 1,     ///< Connects external to neighbor internal layer
    NotDefined = 0    ///< Not yet used
};

/**
 * @brief Tenting mode for vias
 */
enum class QtTentingMode : int {
    FromRules = 0,
    Tented = 1,
    NotTented = 2
};

// Constants
constexpr int QT_UNDEFINED_DRILL_DIAMETER = -1;
constexpr int QT_GEOMETRY_MIN_SIZE = 1; // 0.001mm in internal units

/**
 * @brief Qt-based reimplementation of KiCad's PCB_TRACK class
 * 
 * This class represents a PCB track segment with comprehensive
 * geometry management, width properties, solder mask configuration,
 * and transformation capabilities using Qt frameworks.
 */
class EDA_TRACK_DATA : public EDA_BOARD_CONNECTED_OBJECT
{

public:
    //==========================================================================
    // CONSTRUCTION AND DESTRUCTION
    //==========================================================================
    explicit EDA_TRACK_DATA(EDA_BOARD_OBJECT_DATA* parent = nullptr, QtKicadType type = QtKicadType::PCB_TRACE_T);
    EDA_TRACK_DATA(const EDA_TRACK_DATA& other);
    ~EDA_TRACK_DATA() override;

    // Assignment operator
    EDA_TRACK_DATA& operator=(const EDA_TRACK_DATA& other);

    //==========================================================================
    // TYPE IDENTIFICATION
    //==========================================================================
    static bool classOf(const EDA_OBJECT_DATA* item);
    QString getClassName() const override { return QStringLiteral("EDA_TRACK_DATA"); }
    QString getClass() const override { return QStringLiteral("EDA_TRACK_DATA"); }

    //==========================================================================
    // GEOMETRY PROPERTIES
    //==========================================================================
    
    // Start point
    void setStart(const QPointF& start);
    const QPointF& getStart() const { return m_start; }
    
    void setStartX(double x) { m_start.setX(x); clearShapeCache(); }
    void setStartY(double y) { m_start.setY(y); clearShapeCache(); }
    double getStartX() const { return m_start.x(); }
    double getStartY() const { return m_start.y(); }
    
    // End point
    void setEnd(const QPointF& end);
    const QPointF& getEnd() const { return m_end; }
    
    void setEndX(double x) { m_end.setX(x); clearShapeCache(); }
    void setEndY(double y) { m_end.setY(y); clearShapeCache(); }
    double getEndX() const { return m_end.x(); }
    double getEndY() const { return m_end.y(); }
    
    // Endpoint selection
    const QPointF& getEndPoint(QtEndpointType endPoint) const;
    
    //==========================================================================
    // POSITION AND ORIENTATION
    //==========================================================================
    QPointF getPosition() const override { return m_start; }
    void setPosition(const QPointF& position) override;
    QPointF getFocusPosition() const override;
    
    //==========================================================================
    // WIDTH PROPERTIES
    //==========================================================================
    virtual void setWidth(int width);
    virtual int getWidth() const { return m_width; }
    
    //==========================================================================
    // SOLDER MASK PROPERTIES
    //==========================================================================
    void setHasSolderMask(bool hasMask);
    bool hasSolderMask() const { return m_hasSolderMask; }
    
    void setLocalSolderMaskMargin(std::optional<int> margin);
    std::optional<int> getLocalSolderMaskMargin() const { return m_solderMaskMargin; }
    
    int getSolderMaskExpansion() const;
    
    //==========================================================================
    // LAYER MANAGEMENT
    //==========================================================================
    bool isOnLayer(QtPcbLayerId layer) const override;
    QtLayerSet getLayerSet() const override;
    void setLayerSet(const QtLayerSet& layers) override;
    
    //==========================================================================
    // GEOMETRIC CALCULATIONS
    //==========================================================================
    virtual double getLength() const;
    bool isNull() const;
    QtEdaItemFlags isPointOnEnds(const QPointF& point, int minDist = 0) const;
    bool approxCollinear(const EDA_TRACK_DATA& track) const;
    
    //==========================================================================
    // TRANSFORMATIONS
    //==========================================================================
    void move(const QPointF& moveVector) override;
    void rotate(const QPointF& rotationCenter, double angleRadians) override;
    virtual void mirror(const QPointF& center, QtFlipDirection flipDirection) override;
    void flip(const QPointF& center, QtFlipDirection flipDirection) override;
    
    //==========================================================================
    // SHAPE GENERATION
    //==========================================================================
    //std::shared_ptr<QtShape> getEffectiveShape(QtPcbLayerId layer = QtPcbLayerId::UndefinedLayer,
    //                                         QtFlashing flash = QtFlashing::Default) const override;
    
    void transformShapeToPolygon(QtShapePolySet& buffer, QtPcbLayerId layer,
                                double clearance, double error, QtErrorLoc errorLocation,
                                bool ignoreLineWidth = false) const override;
    
    //==========================================================================
    // HIT TESTING
    //==========================================================================
    bool hitTest(const QPointF& position, double accuracy = 0.0) const override;
    bool hitTest(const QRectF& rect, bool contained = false, double accuracy = 0.0) const override;
    
    //==========================================================================
    // BOUNDING BOX
    //==========================================================================
    QRectF getBoundingBox() const override;
    QRectF viewBBox() const override;
    
    //==========================================================================
    // UI AND DESCRIPTION
    //==========================================================================
    void getMsgPanelInfo(QtDrawFrame* frame, QList<QtMsgPanelItem>& list) ;
    QString getFriendlyName() const override;
    QString getItemDescription(bool full = false) const ;
    QtBitmaps getMenuImage() const override;
    
    //==========================================================================
    // VIEWING
    //==========================================================================
    QVector<int> viewGetLayers() const ;
    double viewGetLOD(int layer, const QtView* view) const ;
    
    //==========================================================================
    // CONSTRAINTS
    //==========================================================================
    virtual QtMinOptMax<int> getWidthConstraint(QString* source = nullptr) const;
    
    //==========================================================================
    // COMPARISON AND DUPLICATION
    //==========================================================================
    EDA_BOARD_OBJECT_DATA* clone() const override;
    double similarity(const EDA_BOARD_OBJECT_DATA& other) const override;
    bool operator==(const EDA_BOARD_OBJECT_DATA& other) const override;
    bool operator==(const EDA_TRACK_DATA& other) const;
    
    //==========================================================================
    // COPPER LAYER STATUS
    //==========================================================================
    bool isOnCopperLayer() const override { return true; }
    
    //==========================================================================
    // VISIT PATTERN
    //==========================================================================
    QtInspectResult visit(QtInspector inspector, void* testData,
                         const QVector<QtKicadType>& scanTypes) override;
    
    //==========================================================================
    // QT SERIALIZATION
    //==========================================================================
    QVariantMap toVariantMap() const override;
    void fromVariantMap(const QVariantMap& map) override;
    
    //==========================================================================
    // COMPARATOR
    //==========================================================================
    struct Comparator {
        bool operator()(const EDA_TRACK_DATA* first, const EDA_TRACK_DATA* second) const;
    };


protected:
    //==========================================================================
    // PROTECTED HELPER METHODS
    //==========================================================================
    void swapData(EDA_BOARD_OBJECT_DATA* other) override;
    void getMsgPanelInfoBase_Common(QtDrawFrame* frame, QList<QtMsgPanelItem>& list) const;
    
    // Shape caching
    void clearShapeCache() const;
    void buildEffectiveShape() const;

protected:
    //==========================================================================
    // PROTECTED DATA MEMBERS
    //==========================================================================
    QPointF m_start;   ///< Line start point
    QPointF m_end;     ///< Line end point
    
    bool m_hasSolderMask = true;
    std::optional<int> m_solderMaskMargin;

private:
    //==========================================================================
    // PRIVATE DATA MEMBERS
    //==========================================================================
    int m_width = 0;   ///< Track thickness (not via width)
    
    // Shape caching
    mutable bool m_shapeDirty = true;
    mutable std::shared_ptr<QtShapeSegment> m_effectiveShape;
    mutable QMutex m_shapeCacheMutex;
};

//=============================================================================
// UTILITY FUNCTIONS AND TYPE ALIASES
//=============================================================================

// Container type aliases
using QtPcbTracks = QList<EDA_TRACK_DATA*>;
using QtPcbTrackList = QVector<EDA_TRACK_DATA*>;

// Hash support
inline uint qHash(QtEndpointType endpoint, uint seed = 0) {
    return static_cast<uint>(qHash(static_cast<int>(endpoint), seed));
}

inline uint qHash(QtViaType via, uint seed = 0) {
    return static_cast<uint>(qHash(static_cast<int>(via), seed));
}

inline uint qHash(QtTentingMode mode, uint seed = 0) {
    return static_cast<uint>(qHash(static_cast<int>(mode), seed));
}

// Utility namespace for track operations
namespace QtPcbTrackUtils {
    /**
     * @brief Calculate distance between two track segments
     */
    double distanceBetweenTracks(const EDA_TRACK_DATA* track1, const EDA_TRACK_DATA* track2);
    
    /**
     * @brief Check if two tracks are connected at endpoints
     */
    bool areTracksConnected(const EDA_TRACK_DATA* track1, const EDA_TRACK_DATA* track2);
    
    /**
     * @brief Find connection point between two tracks
     */
    std::optional<QPointF> findConnectionPoint(const EDA_TRACK_DATA* track1, const EDA_TRACK_DATA* track2);
    
    /**
     * @brief Check if tracks form a continuous path
     */
    bool formContinuousPath(const QList<EDA_TRACK_DATA*>& tracks);
    
    /**
     * @brief Merge collinear tracks if possible
     */
    EDA_TRACK_DATA* mergeCollinearTracks(const EDA_TRACK_DATA* track1, const EDA_TRACK_DATA* track2);
}

