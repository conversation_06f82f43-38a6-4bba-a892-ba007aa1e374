/*
 * Qt-based reimplementation of KiCad BOARD class
 * 
 * This is a complete Qt-based rewrite of the original KiCad BOARD class,
 * replacing wxWidgets dependencies with Qt equivalents and utilizing
 * Qt containers and utilities for better performance and maintainability.
 */

#pragma once

#include "eda_board_object_container.h"
#include "eda_netinfo_list.h"
#include "qt_temporary_implementations.h"
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QMap>
#include <QtCore/QHash>
#include <QtCore/QList>
#include <QtCore/QVector>
#include <QtCore/QSet>
#include <QtCore/QUuid>
#include <QtCore/QMutex>
#include <QtCore/QReadWriteLock>
#include <QtCore/QSharedPointer>
#include <QtCore/QWeakPointer>
#include <QtGui/QTransform>
#include <QtCore/QRectF>
#include <QtCore/QPointF>
#include <QtCore/QSizeF>

// Forward declarations - Qt versions of KiCad classes
class EDA_BOARD_OBJECT_DATA;
class EDA_FOOTPRINT_DATA;
class EDA_TRACK_DATA;
class EDA_BRD_SHAPE_DATA;
class EDA_ZONE_DATA;
class QtPcbGroup;
class QtPcbGenerator;
class QtPcbMarker;
class EDA_NETINFO_LIST;
class EDA_NET_DATA;
class QtBoardDesignSettings;
class EDA_CONNECTIVITY_MANAGER;
class QtProgressReporter;
class QtProject;
class QtPageInfo;
class QtTitleBlock;
class QtPcbPlotParams;
class QtBoardListener;

// Enums
enum class QtBoardUse {
    Normal,
    FootprintHolder
};

enum class QtLayerType {
    Undefined = -1,
    Signal,
    Power,
    Mixed,
    Jumper,
    Aux,
    Front,
    Back
};

// Data structures
struct QtLayerInfo {
    QString name;           // Canonical layer name
    QString userName;       // User-defined layer name
    QtLayerType type = QtLayerType::Signal;
    bool visible = true;
    int number = 0;
    int opposite = 0;       // Opposite layer for paired layers
    
    QtLayerInfo() = default;
    QtLayerInfo(const QString& layerName, QtLayerType layerType = QtLayerType::Signal, 
                bool isVisible = true, int layerNumber = -1)
        : name(layerName), type(layerType), visible(isVisible), number(layerNumber) {}
};

struct QtHighlightInfo {
    QSet<int> netCodes;     // Selected nets for highlighting
    bool highlightActive = false;
    
    void clear() {
        netCodes.clear();
        highlightActive = false;
    }
};

// Cache key structures for performance optimization
struct QtPtrPtrCacheKey {
    EDA_BOARD_OBJECT_DATA* itemA;
    EDA_BOARD_OBJECT_DATA* itemB;
    
    bool operator==(const QtPtrPtrCacheKey& other) const {
        return itemA == other.itemA && itemB == other.itemB;
    }
};

struct QtPtrLayerCacheKey {
    EDA_BOARD_OBJECT_DATA* item;
    QtPcbLayerId layer;
    
    bool operator==(const QtPtrLayerCacheKey& other) const {
        return item == other.item && layer == other.layer;
    }
};

// Hash functions for cache keys
inline uint qHash(const QtPtrPtrCacheKey& key, uint seed = 0) {
    return static_cast<uint>(qHash(key.itemA, seed)) ^ static_cast<uint>(qHash(key.itemB, seed + 1));
}

inline uint qHash(const QtPtrLayerCacheKey& key, uint seed = 0) {
    return static_cast<uint>(qHash(key.item, seed)) ^ static_cast<uint>(qHash(static_cast<int>(key.layer), seed + 1));
}

/**
 * @brief Qt-based reimplementation of KiCad's BOARD class
 * 
 * This class represents a PCB board and manages all its components,
 * tracks, zones, and other elements. It uses Qt containers and
 * follows Qt coding conventions. It inherits from EDA_BOARD_OBJECT_CONTAINER
 * to provide item management functionality.
 */
class EDA_BOARD_DATA : public EDA_BOARD_OBJECT_CONTAINER
{
public:
    // Container type aliases using Qt containers
    using QtMarkers = QList<QtPcbMarker*>;
    using QtDrawings = QList<EDA_BOARD_OBJECT_DATA*>;
    using QtFootprints = QList<EDA_FOOTPRINT_DATA*>;
    using QtTracks = QList<EDA_TRACK_DATA*>;
    using QtGroups = QList<QtPcbGroup*>;
    using QtZones = QList<EDA_ZONE_DATA*>;
    using QtGenerators = QList<QtPcbGenerator*>;

    //==========================================================================
    // CONSTRUCTION AND DESTRUCTION
    //==========================================================================
    explicit EDA_BOARD_DATA(EDA_BOARD_OBJECT_CONTAINER* parent = nullptr);
    ~EDA_BOARD_DATA() override;

    // Disable copy constructor and assignment
    EDA_BOARD_DATA(const EDA_BOARD_DATA&) = delete;
    EDA_BOARD_DATA& operator=(const EDA_BOARD_DATA&) = delete;

    //==========================================================================
    // BASIC PROPERTIES AND IDENTIFICATION
    //==========================================================================
    QtBoardUse getBoardUse() const { return m_boardUse; }
    void setBoardUse(QtBoardUse use) { m_boardUse = use; }
    
    bool isFootprintHolder() const { return m_boardUse == QtBoardUse::FootprintHolder; }
    
    const QString& getFileName() const { return m_fileName; }
    void setFileName(const QString& fileName) { m_fileName = fileName; }
    
    int getTimeStamp() const { return m_timeStamp; }
    void incrementTimeStamp() { ++m_timeStamp; }
    
    const QString& getGenerator() const { return m_generator; }
    void setGenerator(const QString& generator) { m_generator = generator; }
    
    int getFileFormatVersionAtLoad() const { return m_fileFormatVersionAtLoad; }
    void setFileFormatVersionAtLoad(int version) { m_fileFormatVersionAtLoad = version; }

    //==========================================================================
    // CONTAINER ACCESS METHODS
    //==========================================================================
    const QtMarkers& markers() const { return m_markers; }
    const QtDrawings& drawings() const { return m_drawings; }
    const QtFootprints& footprints() const { return m_footprints; }
    const QtTracks& tracks() const { return m_tracks; }
    const QtGroups& groups() const { return m_groups; }
    const QtZones& zones() const { return m_zones; }
    const QtGenerators& generators() const { return m_generators; }
    
    // Non-const versions for modification
    QtDrawings& drawings() { return m_drawings; }
    QtTracks& tracks() { return m_tracks; }

    //==========================================================================
    // ITEM MANAGEMENT (ADD/REMOVE/FIND) - Implementing EDA_BOARD_OBJECT_CONTAINER interface
    //==========================================================================
    void addItem(EDA_BOARD_OBJECT_DATA* item, QtAddMode mode = QtAddMode::Insert, 
                 bool skipConnectivity = false) override;
    void removeItem(EDA_BOARD_OBJECT_DATA* item, QtRemoveMode mode = QtRemoveMode::Normal) override;
    
    // Additional container interface implementations
    int getItemCount() const override;
    bool isEmpty() const override;
    bool contains(const EDA_BOARD_OBJECT_DATA* item) const override;
    QList<EDA_BOARD_OBJECT_DATA*> getAllItems() const override;
    QList<EDA_BOARD_OBJECT_DATA*> getItemsByType(QtKicadType type) const override;
    void clear() override;
    void removeAllItems(const QList<int>& itemTypes = {});
    
    EDA_BOARD_OBJECT_DATA* getItem(const QUuid& id) const;
    void fillItemMap(QHash<QUuid, EDA_BOARD_OBJECT_DATA*>& itemMap);
    
    // Specialized finders
    EDA_FOOTPRINT_DATA* findFootprintByReference(const QString& reference) const;
    EDA_FOOTPRINT_DATA* findFootprintByPath(const QList<QUuid>& path) const;
    EDA_FOOTPRINT_DATA* getFirstFootprint() const;

    //==========================================================================
    // LAYER MANAGEMENT
    //==========================================================================
    int getCopperLayerCount() const;
    void setCopperLayerCount(int count);
    
    int getUserDefinedLayerCount() const;
    void setUserDefinedLayerCount(int count);
    
    QtPcbLayerId getCopperLayerStackMaxId() const;
    QtPcbLayerId flipLayer(QtPcbLayerId layer) const;
    int layerDepth(QtPcbLayerId startLayer, QtPcbLayerId endLayer) const;
    
    // Layer properties
    QString getLayerName(QtPcbLayerId layer) const;
    bool setLayerName(QtPcbLayerId layer, const QString& layerName);
    static QString getStandardLayerName(QtPcbLayerId layer);
    
    QtLayerType getLayerType(QtPcbLayerId layer) const;
    bool setLayerType(QtPcbLayerId layer, QtLayerType layerType);
    
    bool setLayerDescriptor(QtPcbLayerId layer, const QtLayerInfo& layerInfo);
    
    // Layer visibility and enabling
    QtLayerSet getEnabledLayers() const;
    void setEnabledLayers(const QtLayerSet& layers);
    bool isLayerEnabled(QtPcbLayerId layer) const;
    
    QtLayerSet getVisibleLayers() const;
    void setVisibleLayers(const QtLayerSet& layers);
    bool isLayerVisible(QtPcbLayerId layer) const;
    bool isElementVisible(QtLayerGalId element) const;
    
    void setVisibleAll();

    //==========================================================================
    // NETWORK AND CONNECTIVITY
    //==========================================================================
    QSharedPointer<EDA_CONNECTIVITY_MANAGER> getConnectivity() const { return m_connectivity; }
    bool buildConnectivity(QtProgressReporter* reporter = nullptr);
    
    // Network information access
    EDA_NETINFO_LIST* getNetInfo() { return &m_netInfo; }
    const EDA_NETINFO_LIST* getNetInfo() const { return &m_netInfo; }
    
    EDA_NET_DATA* findNet(int netcode) const;
    EDA_NET_DATA* findNet(const QString& netname) const;
    EDA_NET_DATA* getDifferentialPairCoupledNet(const EDA_NET_DATA* net);
    
    int matchDifferentialPairSuffix(const QString& netName, QString& complementNet);
    
    unsigned int getNetCount() const;
    unsigned int getNodesCount(int net = -1) const;
    
    void buildListOfNets();
    void removeUnusedNets();
    void synchronizeNetsAndNetClasses(bool resetTrackAndViaSizes);
    void sanitizeNetcodes();
    void mapNets(EDA_BOARD_DATA* destBoard);

    //==========================================================================
    // HIGHLIGHTING
    //==========================================================================
    void resetNetHighlight();
    const QSet<int>& getHighlightNetCodes() const { return m_highlight.netCodes; }
    void setHighlightNet(int netCode, bool multi = false);
    bool isHighlightNetOn() const { return m_highlight.highlightActive; }
    void setHighlightOn(bool value = true);
    void setHighlightOff() { setHighlightOn(false); }

    //==========================================================================
    // GEOMETRIC OPERATIONS
    //==========================================================================
    QRectF computeBoundingBox(bool boardEdgesOnly = false) const;
    QRectF getBoundingBox() const { return computeBoundingBox(false); }
    QRectF getBoardEdgesBoundingBox() const { return computeBoundingBox(true); }
    
    void move(const QPointF& moveVector);
    QPointF getPosition() const;
    void setPosition(const QPointF& position);
    QPointF getFocusPosition() const { return getBoundingBox().center(); }
    
    // Board outline operations
    bool getBoardPolygonOutlines(QList<QPolygonF>& outlines, 
                                bool allowArcsInPolygons = false,
                                bool includeNpthAsOutlines = false);
    
    double getOutlinesChainingEpsilon() const { return m_outlinesChainingEpsilon; }
    void setOutlinesChainingEpsilon(double value) { m_outlinesChainingEpsilon = value; }

    //==========================================================================
    // PROJECT AND SETTINGS MANAGEMENT
    //==========================================================================
    QtProject* getProject() const { return m_project; }
    void setProject(QtProject* project, bool referenceOnly = false);
    void clearProject();
    
    QtBoardDesignSettings& getDesignSettings() const;
    
    const QtPageInfo& getPageSettings() const { return *m_pageInfo; }
    void setPageSettings(const QtPageInfo& pageSettings);
    
    const QtPcbPlotParams& getPlotOptions() const { return *m_plotOptions; }
    void setPlotOptions(const QtPcbPlotParams& options);
    
    const QtTitleBlock& getTitleBlock() const { return *m_titleBlock; }
    void setTitleBlock(const QtTitleBlock& titleBlock);
    
    const QMap<QString, QString>& getProperties() const { return m_properties; }
    void setProperties(const QMap<QString, QString>& properties) { m_properties = properties; }
    
    void synchronizeProperties();

    //==========================================================================
    // DRC AND VALIDATION
    //==========================================================================
    void deleteMarkers();
    void deleteMarkers(bool warningsAndErrors, bool exclusions);
    
    QList<QtPcbMarker*> resolveDrcExclusions(bool createMarkers);
    void recordDrcExclusions();
    void updateRatsnestExclusions();
    
    int getMaxClearanceValue() const;

    //==========================================================================
    // LISTENER MANAGEMENT
    //==========================================================================
    void addListener(QtBoardListener* listener);
    void removeListener(QtBoardListener* listener);
    void removeAllListeners();

    //==========================================================================
    // EMBEDDED FILES AND FONTS
    //==========================================================================
    bool getEmbedFonts() const { return m_embedFonts; }
    void setEmbedFonts(bool embed) { m_embedFonts = embed; }
    
    QSet<QString> getFonts() const;
    void embedFonts();

    //==========================================================================
    // GROUPS MANAGEMENT
    //==========================================================================
    QString groupsSanityCheck(bool repair = false);
    
    struct GroupLegalOpsField {
        bool create : 1;
        bool ungroup : 1;
        bool removeItems : 1;
    };
    
    GroupLegalOpsField getGroupLegalOps(const QList<EDA_BOARD_OBJECT_DATA*>& selection) const;

    //==========================================================================
    // UTILITY AND CONVERSION METHODS
    //==========================================================================
    QString convertCrossReferencesToKiids(const QString& source) const;
    QString convertKiidssToCrossReferences(const QString& source) const;
    
    bool resolveTextVar(QString& token, int depth) const;
    void getContextualTextVars(QStringList& vars) const;
    
    QString getItemDescription(bool full = false) const;
    QString getClassName() const override { return QStringLiteral("EDA_BOARD_DATA"); }
    QString getClass() const override { return QStringLiteral("BOARD"); }
    
    double similarity(const EDA_BOARD_DATA& other) const { return 1.0; }
    bool operator==(const EDA_BOARD_DATA& other) const;

    //==========================================================================
    // CACHE MANAGEMENT
    //==========================================================================
    void clearCaches();
    
    // Thread-safe cache access
    mutable QReadWriteLock cachesMutex;


protected:
    //==========================================================================
    // PROTECTED HELPER METHODS
    //==========================================================================
    void invokeListeners(const std::function<void(QtBoardListener*)>& func);
    void recalculateOpposites();
    
    template<typename Func>
    void runOnDescendants(const Func& function, int depth = 0) const;
    
    // Template helper methods for container operations
    template<typename Container, typename Item>
    void addToContainer(Container& container, Item* item, QtAddMode mode);
    
    template<typename Container, typename Item>
    void removeFromContainer(Container& container, Item* item);

private:
    //==========================================================================
    // CORE DATA MEMBERS
    //==========================================================================
    
    // Basic properties
    QtBoardUse m_boardUse = QtBoardUse::Normal;
    int m_timeStamp = 1;
    QString m_fileName;
    QString m_generator;
    int m_fileFormatVersionAtLoad = 0;
    
    // Main object containers - using Qt containers
    QtMarkers m_markers;
    QtDrawings m_drawings;
    QtFootprints m_footprints;
    QtTracks m_tracks;
    QtGroups m_groups;
    QtZones m_zones;
    QtGenerators m_generators;
    
    // Fast lookup cache
    mutable QHash<QUuid, EDA_BOARD_OBJECT_DATA*> m_itemByIdCache;
    
    // Layer information
    QMap<int, QtLayerInfo> m_layers;
    
    // Highlighting
    QtHighlightInfo m_highlight;
    QtHighlightInfo m_highlightPrevious;
    
    // Project and settings
    QtProject* m_project = nullptr;
    QSharedPointer<QtBoardDesignSettings> m_designSettings;
    QSharedPointer<QtPageInfo> m_pageInfo;
    QSharedPointer<QtTitleBlock> m_titleBlock;
    QSharedPointer<QtPcbPlotParams> m_plotOptions;
    
    // Properties and metadata
    QMap<QString, QString> m_properties;
    bool m_embedFonts = false;
    
    // Connectivity
    QSharedPointer<EDA_CONNECTIVITY_MANAGER> m_connectivity;
    EDA_NETINFO_LIST m_netInfo;  // Network information list
    
    // Geometry
    double m_outlinesChainingEpsilon = 0.01; // mm
    
    // Performance caches - using Qt containers
    mutable QHash<QtPtrPtrCacheKey, bool> m_intersectsCourtyardCache;
    mutable QHash<QtPtrPtrCacheKey, bool> m_intersectsFCourtyardCache;
    mutable QHash<QtPtrPtrCacheKey, bool> m_intersectsBCourtyardCache;
    mutable QHash<QtPtrLayerCacheKey, bool> m_intersectsAreaCache;
    mutable QHash<QtPtrLayerCacheKey, bool> m_enclosedByAreaCache;
    mutable QHash<QString, QSet<QtPcbLayerId>> m_layerExpressionCache;
    
    // DRC caches
    QList<EDA_ZONE_DATA*> m_drcZones;
    QList<EDA_ZONE_DATA*> m_drcCopperZones;
    int m_drcMaxClearance = 0;
    int m_drcMaxPhysicalClearance = 0;
    
    mutable QHash<EDA_ZONE_DATA*, QRectF> m_zoneBBoxCache;
    mutable int m_maxClearanceValue = -1;
    
    // Listeners
    QList<QtBoardListener*> m_listeners;
    
    // Legacy compatibility flags
    bool m_legacyDesignSettingsLoaded = false;
    bool m_legacyCopperEdgeClearanceLoaded = false;
    bool m_legacyNetclassesLoaded = false;
    bool m_legacyTeardrops = false;
};
