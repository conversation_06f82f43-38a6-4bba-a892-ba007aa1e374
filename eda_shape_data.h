/*
 * Qt-based reimplementation of KiCad EDA_SHAPE class
 * 
 * This class represents geometric shapes (segments, rectangles, arcs, circles, 
 * polygons, bezier curves) with Qt frameworks for better performance and 
 * type safety.
 */

#pragma once

#include <QtCore/QString>
#include <QtCore/QVector>
#include <QtCore/QPointF>
#include <QtCore/QRectF>
#include <QtCore/QJsonObject>
#include <QtGui/QColor>
#include <QtGui/QPolygonF>
#include <QtGui/QPainterPath>
#include <functional>

// Forward declarations for dependencies not in migration scope
class QtSerializable;
class QtEdaDrawFrame;
class QtMsgPanelItem;
class QtShapePolySet;
class QtShape;
class QtUnitsProvider;

// Qt versions of geometry dependencies
namespace QtGeometry {
    class Shape;
    class ShapeArc;
    class ShapeCircle;
    class ShapeSegment;
    class ShapeRect;
    class ShapePolySet;
}

// Enhanced Qt-based enums with Q_ENUM for better introspection
enum class QtShapeType : int {
    Undefined = -1,
    Segment = 0,
    Rectangle,
    Arc,
    Circle,
    Polygon,
    Bezier
};

enum class QtFillType : int {
    NoFill = 1,
    FilledShape,              // Fill with object color
    FilledWithBgBodyColor,    // Fill with background body color
    FilledWithColor           // Fill with separate color
};

enum class QtLineStyle : int {
    Default = -1,
    Solid = 0,
    Dash,
    Dot,
    DashDot,
    DashDotDot
};

// Qt-based stroke parameters structure
struct QtStrokeParams {
    int width = 0;
    QtLineStyle style = QtLineStyle::Default;
    QColor color = QColor();
    
    QtStrokeParams() = default;
    QtStrokeParams(int w, QtLineStyle s = QtLineStyle::Default, const QColor& c = QColor()) 
        : width(w), style(s), color(c) {}
    
    bool operator==(const QtStrokeParams& other) const {
        return width == other.width && style == other.style && color == other.color;
    }
    
    bool operator!=(const QtStrokeParams& other) const {
        return !(*this == other);
    }
};

// Arc midpoint data structure for VCS consistency
struct QtArcMidData {
    QPointF mid;
    QPointF start;
    QPointF end;
    QPointF center;
    
    QtArcMidData() = default;
    QtArcMidData(const QPointF& m, const QPointF& s, const QPointF& e, const QPointF& c)
        : mid(m), start(s), end(e), center(c) {}
};

/**
 * @brief Qt-based reimplementation of KiCad's EDA_SHAPE class
 * 
 * This class represents geometric shapes using Qt frameworks for better
 * performance, type safety, and integration with Qt's graphics system.
 * It supports segments, rectangles, arcs, circles, polygons, and bezier curves.
 */
class EDA_SHAPE_DATA
{

public:
    //==========================================================================
    // CONSTRUCTION AND DESTRUCTION
    //==========================================================================
    explicit EDA_SHAPE_DATA(QtShapeType shapeType = QtShapeType::Segment, 
                        int lineWidth = 0, 
                        QtFillType fillType = QtFillType::NoFill);
    
    // Construct from abstract Qt geometry shape
    explicit EDA_SHAPE_DATA(const QtGeometry::Shape& shape);
    
    // Copy constructor
    EDA_SHAPE_DATA(const EDA_SHAPE_DATA& other);
    
    ~EDA_SHAPE_DATA();

    //==========================================================================
    // SHAPE TYPE AND BASIC PROPERTIES
    //==========================================================================
    QtShapeType getShapeType() const { return m_shapeType; }
    void setShapeType(QtShapeType type);
    
    QString getShapeTypeName() const;
    QString getFriendlyName() const;
    
    bool isProxyItem() const { return m_proxyItem; }
    void setIsProxyItem(bool isProxy = true) { m_proxyItem = isProxy; }

    //==========================================================================
    // GEOMETRIC PROPERTIES - POINTS AND DIMENSIONS  
    //==========================================================================
    const QPointF& getStartPoint() const { return m_startPoint; }
    const QPointF& getEndPoint() const { return m_endPoint; }
    
    void setStartPoint(const QPointF& point);
    void setEndPoint(const QPointF& point);
    void setStartX(double x);
    void setStartY(double y);
    void setEndX(double x);
    void setEndY(double y);
    
    // Center point operations (for circles and arcs)
    QPointF getCenter() const;
    void setCenter(const QPointF& center);
    void setCenterX(double x);
    void setCenterY(double y);
    
    // Rectangle specific operations
    QPointF getTopLeft() const { return getStartPoint(); }
    QPointF getBottomRight() const { return getEndPoint(); }
    void setTopLeft(const QPointF& point) { setStartPoint(point); }
    void setBottomRight(const QPointF& point) { setEndPoint(point); }
    
    // Radius operations (for circles)
    void setRadius(double radius);
    double getRadius() const;
    
    // Rectangle dimensions
    void setRectangleSize(const QSizeF& size);
    void setRectangleWidth(double width);
    void setRectangleHeight(double height);
    QSizeF getRectangleSize() const;
    double getRectangleWidth() const;
    double getRectangleHeight() const;
    
    // Position and bounding operations
    QPointF getPosition() const;
    void setPosition(const QPointF& position);
    QRectF getBoundingRect() const;
    QVector<QPointF> getRectCorners() const;

    //==========================================================================
    // STROKE PROPERTIES AND OPERATIONS
    //==========================================================================
    int getLineWidth() const { return m_stroke.width; }
    void setLineWidth(int width);
    int getEffectiveWidth() const { return getLineWidth(); }
    
    QtLineStyle getLineStyle() const { return m_stroke.style; }
    void setLineStyle(QtLineStyle style);
    
    QColor getLineColor() const { return m_stroke.color; }
    void setLineColor(const QColor& color);
    
    const QtStrokeParams& getStroke() const { return m_stroke; }
    void setStroke(const QtStrokeParams& stroke);

    //==========================================================================
    // FILL PROPERTIES AND OPERATIONS
    //==========================================================================
    QtFillType getFillMode() const { return m_fillMode; }
    void setFillMode(QtFillType fillMode);
    
    QColor getFillColor() const { return m_fillColor; }
    void setFillColor(const QColor& color);
    
    bool isFilled() const { return m_fillMode != QtFillType::NoFill; }
    void setFilled(bool filled);
    bool isFilledForHitTesting() const { return isFilled(); }
    bool isClosed() const;

    //==========================================================================
    // ARC-SPECIFIC OPERATIONS
    //==========================================================================
    void setArcGeometry(const QPointF& start, const QPointF& mid, const QPointF& end);
    void setArcAngleAndEnd(double angleDegrees, bool checkNegativeAngle = false);
    double getArcAngle() const;       // In degrees
    QPointF getArcMid() const;
    bool isClockwiseArc() const;
    void calcArcAngles(double& startAngle, double& endAngle) const;
    
    // Arc midpoint caching for VCS consistency
    void setCachedArcData(const QPointF& start, const QPointF& mid, 
                         const QPointF& end, const QPointF& center);
    const QtArcMidData& getArcMidData() const { return m_arcMidData; }
    
    bool areEndsSwapped() const { return m_endsSwapped; }

    //==========================================================================
    // BEZIER CURVE OPERATIONS
    //==========================================================================
    void setBezierC1(const QPointF& point) { m_bezierC1 = point; }
    void setBezierC2(const QPointF& point) { m_bezierC2 = point; }
    const QPointF& getBezierC1() const { return m_bezierC1; }
    const QPointF& getBezierC2() const { return m_bezierC2; }
    
    const QVector<QPointF>& getBezierPoints() const { return m_bezierPoints; }
    void rebuildBezierToSegments(double maxError);

    //==========================================================================
    // POLYGON OPERATIONS
    //==========================================================================
    void setPolygonPoints(const QVector<QPointF>& points);
    void setPolygonPoints(const QPolygonF& polygon);
    const QPolygonF& getPolygon() const { return m_polygon; }
    QPolygonF& getPolygon() { return m_polygon; }
    
    int getPointCount() const { return m_polygon.size(); }
    bool isPolygonValid() const { return m_polygon.size() > 2; }
    
    // Copy polygon points to external vector
    void copyPolygonPoints(QVector<QPointF>& buffer) const;

    //==========================================================================
    // GEOMETRIC CALCULATIONS AND MEASUREMENTS
    //==========================================================================
    double getLength() const;
    double getSegmentAngle() const;   // In degrees
    void setSegmentAngle(double angleDegrees);
    void setLength(double length);
    
    // Angle operations
    double getAngleDegrees() const;
    double getAngleRadians() const;

    //==========================================================================
    // SHAPE TRANSFORMATIONS
    //==========================================================================
    void move(const QPointF& moveVector);
    void rotate(const QPointF& rotCenter, double angleDegrees);
    void flip(const QPointF& center, bool horizontal = true);
    void scale(double scaleFactor);
    void scale(double scaleX, double scaleY);

    //==========================================================================
    // HIT TESTING AND COLLISION DETECTION
    //==========================================================================
    bool hitTest(const QPointF& position, double accuracy = 0.0) const;
    bool hitTest(const QRectF& rect, bool contained = false, double accuracy = 0.0) const;

    //==========================================================================
    // SHAPE CONVERSION AND REPRESENTATION
    //==========================================================================
    QVector<QtGeometry::Shape*> makeEffectiveShapes(bool edgeOnly = false) const;
    QPainterPath toPainterPath() const;
    QPolygonF toPolygon(double maxError = 0.1) const;
    
    // Convert shape to closed polygon for various operations
    void transformShapeToPolygon(QPolygonF& buffer, double clearance = 0.0, 
                                double maxError = 0.1, bool ignoreLineWidth = false) const;

    //==========================================================================
    // COMPARISON AND SIMILARITY
    //==========================================================================
    int compare(const EDA_SHAPE_DATA& other) const;
    double similarity(const EDA_SHAPE_DATA& other) const;
    bool operator==(const EDA_SHAPE_DATA& other) const;
    bool operator!=(const EDA_SHAPE_DATA& other) const { return !(*this == other); }

    //==========================================================================
    // EDITING STATE MANAGEMENT
    //==========================================================================
    void beginEdit(const QPointF& startPoint);
    bool continueEdit(const QPointF& position);
    void endEdit(bool closed = true);
    void calcEdit(const QPointF& position);
    int getEditState() const { return m_editState; }
    void setEditState(int state) { m_editState = state; }

    //==========================================================================
    // SERIALIZATION SUPPORT
    //==========================================================================
    QVariantMap serialize() const;
    bool deserialize(const QVariantMap& data);
    
    // JSON serialization
    QJsonObject toJson() const;
    bool fromJson(const QJsonObject& json);

    //==========================================================================
    // USER INTERFACE SUPPORT
    //==========================================================================
    void getMsgPanelInfo(QtEdaDrawFrame* frame, QVector<QtMsgPanelItem>& msgItems) const;
    QString getItemDescription(QtUnitsProvider* unitsProvider = nullptr, bool full = false) const;

    //==========================================================================
    // UTILITY METHODS
    //==========================================================================
    void swapShape(EDA_SHAPE_DATA& other);
    EDA_SHAPE_DATA* clone() const;
    
    // Static utility methods
    static QString shapeTypeToString(QtShapeType type);
    static QtShapeType shapeTypeFromString(const QString& str);
    static QString fillTypeToString(QtFillType type);
    static QtFillType fillTypeFromString(const QString& str);
    static QString lineStyleToString(QtLineStyle style);
    static QtLineStyle lineStyleFromString(const QString& str);


protected:
    //==========================================================================
    // PROTECTED HELPER METHODS
    //==========================================================================
    void computeArcBoundingBox(QRectF& bbox) const;
    QVector<QPointF> buildBezierToSegments(double maxError) const;
    QVector<QtGeometry::Shape*> makeEffectiveShapesImpl(bool edgeOnly, bool lineChainOnly = false) const;
    
    void updateDerivedProperties();  // Update computed properties when geometry changes

private:
    //==========================================================================
    // PRIVATE DATA MEMBERS
    //==========================================================================
    
    // Core shape properties  
    QtShapeType m_shapeType;           // Type of shape
    bool m_endsSwapped;                // True if start/end were swapped
    QtStrokeParams m_stroke;           // Line style, width, color
    QtFillType m_fillMode;             // Fill mode
    QColor m_fillColor;                // Fill color
    
    // Geometric data
    QPointF m_startPoint;              // Start point or center for circles
    QPointF m_endPoint;                // End point or radius point for circles
    QPointF m_arcCenter;               // Arc center (computed)
    QtArcMidData m_arcMidData;         // Arc midpoint data for VCS consistency
    
    // Bezier curve data
    QPointF m_bezierC1;                // Bezier control point 1
    QPointF m_bezierC2;                // Bezier control point 2
    QVector<QPointF> m_bezierPoints;   // Computed bezier segments
    
    // Polygon data
    QPolygonF m_polygon;               // Polygon points for POLY shape
    
    // Rectangle dimensions (cached for performance)
    double m_rectangleWidth;
    double m_rectangleHeight;
    
    // Segment properties (cached for performance)
    double m_segmentLength;
    double m_segmentAngle;             // In degrees
    
    // State management
    int m_editState;                   // Current editing state
    bool m_proxyItem;                  // True if this is a proxy item
    
    // Qt-specific optimizations
    mutable QRectF m_cachedBoundingRect;  // Cached bounding rectangle
    mutable bool m_boundingRectValid;     // Cache validity flag
    
    void invalidateCache();            // Invalidate cached computations
    void ensureBoundingRectValid() const;  // Ensure bounding rect is valid
};

/**
 * @brief Qt container type aliases for shapes
 */
using EDA_SHAPE_DATAs = QVector<EDA_SHAPE_DATA*>;
using EDA_SHAPE_DATAList = QList<EDA_SHAPE_DATA*>;

/**
 * @brief Helper function for cloning shapes in containers
 */
inline EDA_SHAPE_DATA* newClone(const EDA_SHAPE_DATA& shape) { 
    return shape.clone(); 
}

