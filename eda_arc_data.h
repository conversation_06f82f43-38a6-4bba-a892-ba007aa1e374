/*
 * Qt-based reimplementation of KiCad PCB_ARC class
 * 
 * This class represents an arc-shaped PCB track using Qt frameworks.
 * Arcs are defined by start, mid, and end points, allowing for curved connections
 * between components or routing around obstacles.
 */

#pragma once

#include "eda_track_data.h"
#include <QtCore/QString>
#include <QtCore/QPointF>
#include <QtCore/QRectF>
#include <QtCore/QSharedPointer>
#include <QtCore/QMutex>
#include <QtCore/QVariantMap>
#include <QtCore/QList>
#include <QtCore/QVector>
#include <QtCore/QtMath>
#include <QtCore/QLineF>
#include <qt_temporary_implementations.h>

// Logging category removed (Qt object system removal)

// Forward declarations for dependencies not in migration scope
class QtShapeArc;
class QtShapeSegment;
class QtShapeCircle;
class QtView;

/**
 * @brief Arc geometry information structure
 */
struct QtArcGeometry {
    QPointF center;        ///< Arc center point
    qreal radius = 0.0;    ///< Arc radius
    qreal startAngle = 0.0;  ///< Start angle in degrees
    qreal endAngle = 0.0;    ///< End angle in degrees
    qreal arcAngle = 0.0;    ///< Total arc angle (can be negative for CW)
    bool isCCW = true;       ///< Counter-clockwise direction
    
    bool isValid() const {
        return radius > 0.0 && !qFuzzyIsNull(arcAngle);
    }
};

/**
 * @brief Qt-based reimplementation of KiCad's PCB_ARC class
 * 
 * This class represents an arc-shaped PCB track with comprehensive
 * geometric calculations, transformations, and hit testing capabilities.
 * Arcs are defined by three points: start, mid, and end.
 */
class EDA_ARC_DATA : public EDA_TRACK_DATA
{

public:
    //==========================================================================
    // CONSTRUCTION AND DESTRUCTION
    //==========================================================================
    explicit EDA_ARC_DATA(EDA_BOARD_OBJECT_DATA* parent = nullptr);
    EDA_ARC_DATA(EDA_BOARD_OBJECT_DATA* parent, const QtShapeArc* arc);
    EDA_ARC_DATA(const EDA_ARC_DATA& other, QObject* parent = nullptr);
    ~EDA_ARC_DATA() override;

    // Assignment operator
    EDA_ARC_DATA& operator=(const EDA_ARC_DATA& other);

    //==========================================================================
    // TYPE IDENTIFICATION
    //==========================================================================
    static bool classOf(const EDA_OBJECT_DATA* item);
    QString getClassName() const override { return QStringLiteral("EDA_ARC_DATA"); }
    QString getClass() const override { return getClassName(); }

    //==========================================================================
    // GEOMETRIC PROPERTIES
    //==========================================================================
    void setMid(const QPointF& mid);
    const QPointF& getMid() const { return m_mid; }
    
    // Position returns the arc center
    QPointF getPosition() const override;
    void setPosition(const QPointF& pos) override { setStart(pos); }
    
    // Focus position for UI operations
    QPointF getFocusPosition() const override { return m_mid; }
    
    // Center calculations
    QPointF getCenter() const override { return getPosition(); }
    
    //==========================================================================
    // ARC SPECIFIC CALCULATIONS
    //==========================================================================
    qreal getRadius() const;
    qreal getAngle() const;      // Total arc angle (signed)
    qreal getArcAngleStart() const;   // Start angle in degrees
    qreal getArcAngleEnd() const;     // End angle in degrees
    
    bool isCCW() const;
    bool isDegenerated(int threshold = 5) const;
    
    // Get complete arc geometry information
    QtArcGeometry getArcGeometry() const;
    
    //==========================================================================
    // LENGTH CALCULATION
    //==========================================================================
    double getLength() const override;

    //==========================================================================
    // TRANSFORMATIONS
    //==========================================================================
    void move(const QPointF& moveVector) override;
    void rotate(const QPointF& rotCenter, qreal angle) override;
    void mirror(const QPointF& center, QtFlipDirection flipDirection) override;
    void flip(const QPointF& center, QtFlipDirection flipDirection) override;

    //==========================================================================
    // SHAPE GENERATION
    //==========================================================================
    std::shared_ptr<QtShape> getEffectiveShape(QtPcbLayerId layer = QtPcbLayerId::UndefinedLayer,
                                             QtFlashing flash = QtFlashing::Default) const override;

    //==========================================================================
    // HIT TESTING
    //==========================================================================
    bool hitTest(const QPointF& position, double accuracy = 0.0) const override;
    bool hitTest(const QRectF& rect, bool contained, int accuracy = 0) const ;

    //==========================================================================
    // BOUNDING BOX
    //==========================================================================
    QRectF getBoundingBox() const override;

    //==========================================================================
    // UI AND DESCRIPTION
    //==========================================================================
    QString getItemDescription(bool full = false) const;

    //==========================================================================
    // COMPARISON AND DUPLICATION
    //==========================================================================
    EDA_BOARD_OBJECT_DATA* clone() const override;
    double similarity(const EDA_BOARD_OBJECT_DATA& other) const override;
    bool operator==(const EDA_BOARD_OBJECT_DATA& other) const override;
    bool operator==(const EDA_ARC_DATA& other) const;

    //==========================================================================
    // QT SERIALIZATION
    //==========================================================================
    QVariantMap toVariantMap() const override;
    void fromVariantMap(const QVariantMap& map) override;


protected:
    //==========================================================================
    // PROTECTED HELPER METHODS
    //==========================================================================
    void swapData(EDA_BOARD_OBJECT_DATA* other) override;
    
    // Arc-specific calculations
    static QPointF calcArcCenter(const QPointF& start, const QPointF& mid, const QPointF& end);
    static bool isPointOnArc(const QPointF& point, const QtArcGeometry& geometry, double tolerance);
    
    // Update cached geometry when points change
    void updateCachedGeometry() const;

private:
    //==========================================================================
    // PRIVATE DATA MEMBERS
    //==========================================================================
    QPointF m_mid;    ///< Arc mid point, halfway between start and end
    
    // Cached geometry with thread safety
    mutable QMutex m_geometryCacheMutex;
    mutable QtArcGeometry m_cachedGeometry;
    mutable bool m_geometryDirty = true;
    
    // Silence operator== warning
    bool operator==(const EDA_TRACK_DATA& other) const  {
        return EDA_TRACK_DATA::operator==(other);
    }
};

//=============================================================================
// UTILITY FUNCTIONS AND TYPE ALIASES
//=============================================================================

// Container type aliases - QtPcbArc defined in qt_temporary_implementations.h
using QtPcbArcs = QList<EDA_ARC_DATA*>;
using QtPcbArcList = QVector<EDA_ARC_DATA*>;

// Utility namespace for arc operations
namespace QtPcbArcUtils {
    /**
     * @brief Calculate arc center from three points
     * @return Center point, or null point if calculation fails
     */
    QPointF calculateArcCenter(const QPointF& start, const QPointF& mid, const QPointF& end);
    
    /**
     * @brief Calculate complete arc geometry from three points
     */
    QtArcGeometry calculateArcGeometry(const QPointF& start, const QPointF& mid, const QPointF& end);
    
    /**
     * @brief Check if three points form a valid arc (not collinear)
     */
    bool isValidArc(const QPointF& start, const QPointF& mid, const QPointF& end, qreal tolerance = 0.001);
    
    /**
     * @brief Convert arc to polyline approximation
     * @param geometry Arc geometry
     * @param maxError Maximum deviation from true arc
     * @return List of points approximating the arc
     */
    QVector<QPointF> arcToPolyline(const QtArcGeometry& geometry, qreal maxError);
    
    /**
     * @brief Calculate the number of segments needed for arc approximation
     */
    int calculateSegmentCount(qreal radius, qreal angle, qreal maxError);
    
    /**
     * @brief Find intersection points between arc and line
     */
    QVector<QPointF> arcLineIntersection(const QtArcGeometry& arc, const QLineF& line);
    
    /**
     * @brief Check if point lies on arc
     */
    bool isPointOnArc(const QPointF& point, const QtArcGeometry& arc, qreal tolerance);
    
    /**
     * @brief Calculate tangent angle at a point on the arc
     */
    qreal getTangentAngle(const QtArcGeometry& arc, const QPointF& point);
    
    /**
     * @brief Split arc at a given point
     * @return Pair of arcs (before and after split point)
     */
    QPair<EDA_ARC_DATA*, EDA_ARC_DATA*> splitArc(const EDA_ARC_DATA* arc, const QPointF& splitPoint);
    
    /**
     * @brief Merge two connected arcs if they form a smooth curve
     */
    EDA_ARC_DATA* mergeArcs(const EDA_ARC_DATA* arc1, const EDA_ARC_DATA* arc2, qreal angleTolerance = 1.0);
    
    /**
     * @brief Convert arc angle to normalized range [0, 360)
     */
    qreal normalizeAngle(qreal angle);
    
    /**
     * @brief Calculate shortest angular distance between two angles
     */
    qreal angularDistance(qreal angle1, qreal angle2);
}
