/*
 * This program source code file is part of KiCad, a free EDA CAD application.
 *
 * Qt-based implementation of PCB text
 */

#include <QDebug>
#include <QPainter>
#include <QTransform>
#include <QtMath>
#include <QPolygonF>
#include <QRegularExpression>
#include <QTextDocument>

#include "eda_brd_text_data.h"
#include "eda_board_data.h"
#include "eda_footprint_data.h"
#include "qt_temporary_implementations.h"

// Helper function for knockout text margin calculation
static int getKnockoutTextMargin(const QSizeF& textSize, int thickness) {
    int margin = qMin(textSize.width(), textSize.height()) / 4;
    return qMax(margin, thickness * 2);
}

// Constructor implementations
EDA_BRD_TEXT_DATA::EDA_BRD_TEXT_DATA(EDA_BOARD_OBJECT_CONTAINER* parent, QtKicadType idtype)
    : EDA_BOARD_OBJECT_DATA(parent, idtype)
    , EDA_TEXT_DATA()
{
    // setMultilineAllowed(true); // Commented out - method not available in base class
}

EDA_BRD_TEXT_DATA::EDA_BRD_TEXT_DATA(EDA_FOOTPRINT_DATA* parent, QtKicadType idtype)
    : EDA_BOARD_OBJECT_DATA(parent, idtype)
    , EDA_TEXT_DATA()
{
    // setKeepUpright(true); // Commented out - method not available in base class
    
    // Set default text thickness
    // setTextThickness(0.15); // Commented out - method not available in base class
    setLayer(F_SILKS); // Front silkscreen by default
    
    if (parent) {
        // setTextPos(parent->getPosition()); // Commented out - method not available in base class
        
        // If parent is on back layer, set text to back silkscreen
        if (isBackLayer(parent->getLayer())) {
            setLayer(B_SILKS);
        }
    }
}

EDA_BRD_TEXT_DATA::~EDA_BRD_TEXT_DATA() = default;

// Static factory method
bool EDA_BRD_TEXT_DATA::classOf(const EDA_OBJECT_DATA* aItem) {
    return aItem && aItem->getType() == QtKicadType::PcbTextT;
}

// Type checking
bool EDA_BRD_TEXT_DATA::isType(const QVector<QtKicadType>& aScanTypes) const {
    if (EDA_BOARD_OBJECT_DATA::isType(aScanTypes)) {
        return true;
    }
    
    for (QtKicadType scanType : aScanTypes) {
        if (scanType == QtKicadType::PcbTextT) {
            return true;
        }
    }
    
    return false;
}

// Style management
void EDA_BRD_TEXT_DATA::styleFromSettings(const QtBoardDesignSettings& settings) {
    auto layerId = static_cast<int>(getLayer());
    setTextSize(settings.getTextSize(layerId));
    // setTextThickness(settings.getTextThickness(layerId)); // Commented out - method not available
    setItalic(settings.getTextItalic(layerId));
    // setKeepUpright(settings.getTextUpright(layerId)); // Commented out - method not available
    setMirrored(isBackLayer(getLayer()));
}

// Keep text upright
void EDA_BRD_TEXT_DATA::keepUpright() {
    // if (!isKeepUpright()) { // Commented out - method not available
    //     return;
    // }
    
    // QtEdaAngle newAngle = getTextAngle(); // Commented out - method not available
    // newAngle.normalize();
    
    // bool needsFlipped = newAngle.value >= 180.0;
    
    // if (needsFlipped) {
    //     setHorizJustify(-getHorizJustify());
    //     setVertJustify(-getVertJustify());
    //     newAngle.value += 180.0;
    //     newAngle.normalize();
    //     setTextAngle(newAngle);
    // }
    // Placeholder implementation - comment out all functionality for now
}

// Text content
QString EDA_BRD_TEXT_DATA::getShownText(bool aAllowExtraText, int aDepth) const {
    return EDA_TEXT_DATA::getShownText(aAllowExtraText, aDepth);
}

// Search functionality
bool EDA_BRD_TEXT_DATA::matches(const QtEdaSearchData& aSearchData, void* aAuxData) const {
    // return EDA_TEXT_DATA::matches(aSearchData, aAuxData); // Commented out - signature mismatch
    // Simple implementation for now - just check if the search text is contained in our text
    Q_UNUSED(aSearchData);
    return false; // Placeholder - just return false for now to avoid method call issues
}

// Position management
QPointF EDA_BRD_TEXT_DATA::getPosition() const {
    // return EDA_TEXT_DATA::getTextPos(); // Commented out - method not available
    return QPointF(); // Placeholder implementation
}

void EDA_BRD_TEXT_DATA::setPosition(const QPointF& aPos) {
    // EDA_TEXT_DATA::setTextPos(aPos); // Commented out - method not available
    // invalidateBoundingBox(); // Commented out - method not available
    // Position changed notification removed (Qt object system removal)
    Q_UNUSED(aPos); // Placeholder implementation
}

void EDA_BRD_TEXT_DATA::move(const QPointF& aMoveVector) {
    // EDA_TEXT_DATA::offset(aMoveVector); // Commented out - method not available
    // invalidateBoundingBox(); // Commented out - method not available
    // Position changed notification removed (Qt object system removal)
    Q_UNUSED(aMoveVector); // Placeholder implementation
}

// Transformations
void EDA_BRD_TEXT_DATA::rotate(const QPointF& aRotCentre, const QtEdaAngle& aAngle) {
    // QPointF pt = getTextPos(); // Commented out - method not available
    // rotatePoint(pt, aRotCentre, aAngle);
    // setTextPos(pt);
    
    // QtEdaAngle newAngle = getTextAngle(); // Commented out - method not available
    // newAngle.value += aAngle.value;
    // newAngle.normalize();
    // setTextAngle(newAngle);
    
    // invalidateBoundingBox(); // Commented out - method not available
    // Rotation changed notification removed (Qt object system removal)
    Q_UNUSED(aRotCentre);
    Q_UNUSED(aAngle);
    // Placeholder implementation
}

void EDA_BRD_TEXT_DATA::mirror(const QPointF& aCentre, QtFlipDirection aFlipDirection) {
    // if (aFlipDirection == FLIP_LEFT_RIGHT) {
    //     if (getTextAngle().value == 90.0) {  // Vertical text
    //         setHorizJustify(-getHorizJustify());
    //     }
    //     setTextY(mirrorValue(getTextPos().y(), aCentre.y()));
    // } else {
    //     if (getTextAngle().value == 0.0) {  // Horizontal text
    //         setHorizJustify(-getHorizJustify());
    //     }
    //     setTextX(mirrorValue(getTextPos().x(), aCentre.x()));
    // }
    
    // invalidateBoundingBox();
    Q_UNUSED(aCentre);
    Q_UNUSED(aFlipDirection);
    // Placeholder implementation
}

void EDA_BRD_TEXT_DATA::flip(const QPointF& aCentre, QtFlipDirection aFlipDirection) {
    // if (aFlipDirection == FLIP_LEFT_RIGHT) {
    //     setTextX(mirrorValue(getTextPos().x(), aCentre.x()));
    //     QtEdaAngle angle = getTextAngle();
    //     angle.value = -angle.value;
    //     setTextAngle(angle);
    // } else {
    //     setTextY(mirrorValue(getTextPos().y(), aCentre.y()));
    //     QtEdaAngle angle = getTextAngle();
    //     angle.value = 180.0 - angle.value;
    //     setTextAngle(angle);
    // }
    
    // // Flip layer
    // if (auto board = getBoard()) {
    //     setLayer(board->flipLayer(getLayer()));
    // }
    
    // if (isSideSpecific()) {
    //     setMirrored(!isMirrored());
    // }
    
    // invalidateBoundingBox();
    Q_UNUSED(aCentre);
    Q_UNUSED(aFlipDirection);
    // Placeholder implementation
}

// Information display
void EDA_BRD_TEXT_DATA::getMsgPanelInfo(EDA_BOARD_DATA* frame, QVector<QtMsgPanelItem>& items) const {
    // EDA_FOOTPRINT_DATA* parentFP = getParentFootprint(); // Commented out - method not available
    
    // if (parentFP && frame) {
    //     items.append(QtMsgPanelItem("Footprint", parentFP->getReference()));
    // }
    
    // Show text content
    // if (parentFP) {
    //     items.append(QtMsgPanelItem("Text", getText()));
    // } else {
        items.append(QtMsgPanelItem("PCB Text", getText()));
    // }
    
    // if (parentFP) {
    //     items.append(QtMsgPanelItem("Type", getTextTypeDescription()));
    // }
    
    // if (frame && isLocked()) {
    //     items.append(QtMsgPanelItem("Status", "Locked"));
    // }
    
    Q_UNUSED(frame);
    // Placeholder implementation with basic info - commenting out all references to unavailable methods
    
    // items.append(QtMsgPanelItem("Layer", getLayerName())); // getLayerName() not available
    // items.append(QtMsgPanelItem("Mirror", isMirrored() ? "Yes" : "No")); // isMirrored() not available
    // items.append(QtMsgPanelItem("Angle", QString::number(getTextAngle().value, 'g'))); // getTextAngle() not available
    
    // QString fontName = getFont() ? getFont()->getName() : "Default"; // getFont()->getName() not available
    // items.append(QtMsgPanelItem("Font", fontName));
    // items.append(QtMsgPanelItem("Thickness", frame->messageTextFromValue(getTextThickness()))); // getTextThickness() not available
    // items.append(QtMsgPanelItem("Width", frame->messageTextFromValue(getTextWidth()))); // getTextWidth() not available
    // items.append(QtMsgPanelItem("Height", frame->messageTextFromValue(getTextHeight()))); // getTextHeight() not available
}

// Hit testing
bool EDA_BRD_TEXT_DATA::textHitTest(const QPointF& aPoint, int aAccuracy) const {
    // int accuracy = aAccuracy;
    
    // if (isKnockout()) { // isKnockout() not available
    //     accuracy += getKnockoutTextMargin(getTextSize(), getTextThickness()); // getTextSize(), getTextThickness() not available
    // }
    
    // return EDA_TEXT_DATA::textHitTest(aPoint, accuracy); // textHitTest() not available in base class
    Q_UNUSED(aPoint);
    Q_UNUSED(aAccuracy);
    return false; // Placeholder implementation
}

bool EDA_BRD_TEXT_DATA::textHitTest(const QtBox2I& aRect, bool aContains, int aAccuracy) const {
    // QtBox2I rect = aRect;
    // rect.inflate(aAccuracy);
    
    // if (aContains) {
    //     return rect.contains(getBoundingBox()); // getBoundingBox() override exists but may not work
    // }
    
    // return rect.intersects(getBoundingBox());
    Q_UNUSED(aRect);
    Q_UNUSED(aContains);
    Q_UNUSED(aAccuracy);
    return false; // Placeholder implementation
}

bool EDA_BRD_TEXT_DATA::hitTest(const QPointF& aPosition, double aAccuracy) const {
    return textHitTest(aPosition, static_cast<int>(aAccuracy));
}

bool EDA_BRD_TEXT_DATA::hitTest(const QRectF& aRect, bool aContained, double aAccuracy) const {
    // Convert QRectF to QtBox2I for the textHitTest call
    QtBox2I box(QPoint(static_cast<int>(aRect.x()), static_cast<int>(aRect.y())), 
                QSize(static_cast<int>(aRect.width()), static_cast<int>(aRect.height())));
    return textHitTest(box, aContained, static_cast<int>(aAccuracy));
}

// Shape conversion
void EDA_BRD_TEXT_DATA::transformTextToPolySet(QtShapePolySet& aBuffer, int aClearance, 
                                      int aMaxError, QtErrorLoc aErrorLoc) const {
    // QtShapePolySet textShape;
    
    // // Render text to polygon set
    // // This would normally use font rendering to create the actual text shapes
    // // For now, we'll create a simple bounding box representation
    // QRectF textRect = getTextBox(); // getTextBox() not available
    // textShape.newOutline();
    // textShape.append(textRect.left(), textRect.top());
    // textShape.append(textRect.right(), textRect.top());
    // textShape.append(textRect.right(), textRect.bottom());
    // textShape.append(textRect.left(), textRect.bottom());
    
    // textShape.simplify();
    
    // if (isKnockout()) { // isKnockout() not available
    //     QtShapePolySet finalPoly;
    //     int margin = getKnockoutTextMargin(getTextSize(), getTextThickness()); // methods not available
        
    //     buildBoundingHull(&finalPoly, textShape, margin + aClearance);
    //     finalPoly.booleanSubtract(textShape);
        
    //     aBuffer.append(finalPoly);
    // } else {
    //     if (aClearance > 0 || aErrorLoc == QtErrorLoc::ErrorOutside) {
    //         int clearance = aClearance;
    //         if (aErrorLoc == QtErrorLoc::ErrorOutside) {
    //             clearance += aMaxError;
    //         }
    //         textShape.inflate(clearance, CORNER_STRATEGY_ROUND_ALL, aMaxError); // constants not available
    //     }
        
    //     aBuffer.append(textShape);
    // }
    Q_UNUSED(aBuffer);
    Q_UNUSED(aClearance);
    Q_UNUSED(aMaxError);
    Q_UNUSED(aErrorLoc);
    // Placeholder implementation
}

void EDA_BRD_TEXT_DATA::transformShapeToPolygon(QtShapePolySet& aBuffer, QtPcbLayerId aLayer,
                                       int aClearance, int aMaxError, QtErrorLoc aErrorLoc,
                                       bool aIgnoreLineWidth) const {
    // QtShapePolySet poly;
    // transformTextToPolySet(poly, 0, aMaxError, aErrorLoc);
    // buildBoundingHull(&aBuffer, poly, aClearance); // buildBoundingHull not available
    Q_UNUSED(aBuffer);
    Q_UNUSED(aLayer);
    Q_UNUSED(aClearance);
    Q_UNUSED(aMaxError);
    Q_UNUSED(aErrorLoc);
    Q_UNUSED(aIgnoreLineWidth);
    // Placeholder implementation
}

// Shape management - all remaining methods are stub implementations
std::shared_ptr<QtShape> EDA_BRD_TEXT_DATA::getEffectiveShape(QtPcbLayerId aLayer, QtFlashing aFlash) const {
    Q_UNUSED(aLayer);
    Q_UNUSED(aFlash);
    return nullptr; // Placeholder
}

// Description methods - stub implementations
QString EDA_BRD_TEXT_DATA::getTextTypeDescription() const {
    return "PCB Text"; // Placeholder
}

QString EDA_BRD_TEXT_DATA::getItemDescription(QtUnitsProvider* aUnitsProvider, bool aFull) const {
    Q_UNUSED(aUnitsProvider);
    Q_UNUSED(aFull);
    return getText(); // Placeholder
}

// UI support
QtBitmaps EDA_BRD_TEXT_DATA::getMenuImage() const {
    return QtBitmaps::TextDefault; // Placeholder
}

// Drawing rotation
QtEdaAngle EDA_BRD_TEXT_DATA::getDrawRotation() const {
    return QtEdaAngle(0.0); // Placeholder
}

// View support
QRectF EDA_BRD_TEXT_DATA::viewBBox() const {
    return getBoundingBox(); // This should work as it's implemented
}

QVector<int> EDA_BRD_TEXT_DATA::viewGetLayers() const {
    QVector<int> layers;
    layers.append(static_cast<int>(getLayer()));
    return layers;
}

double EDA_BRD_TEXT_DATA::viewGetLOD(int aLayer, const QtView* aView) const {
    Q_UNUSED(aLayer);
    Q_UNUSED(aView);
    return 0.0; // Always visible
}

// Bounding box
QRectF EDA_BRD_TEXT_DATA::getBoundingBox() const {
    return QRectF(); // Placeholder - return empty rectangle
}

// Cloning
EDA_OBJECT_DATA* EDA_BRD_TEXT_DATA::clone() const {
    return new EDA_BRD_TEXT_DATA(*this);
}

// Comparison methods
double EDA_BRD_TEXT_DATA::similarity(const EDA_BOARD_OBJECT_DATA& other) const {
    Q_UNUSED(other);
    return 0.0; // Placeholder
}

bool EDA_BRD_TEXT_DATA::operator==(const EDA_BOARD_OBJECT_DATA& other) const {
    Q_UNUSED(other);
    return false; // Placeholder
}

bool EDA_BRD_TEXT_DATA::operator==(const EDA_BRD_TEXT_DATA& aOther) const {
    Q_UNUSED(aOther);
    return false; // Placeholder
}

// Protected methods
void EDA_BRD_TEXT_DATA::buildBoundingHull(QtShapePolySet* aBuffer, const QtShapePolySet& aRenderedText, int aClearance) const {
    Q_UNUSED(aBuffer);
    Q_UNUSED(aRenderedText);
    Q_UNUSED(aClearance);
    // Placeholder
}

void EDA_BRD_TEXT_DATA::swapData(EDA_BOARD_OBJECT_DATA* aImage) {
    Q_UNUSED(aImage);
    // Placeholder
}

int EDA_BRD_TEXT_DATA::getKnockoutMargin() const {
    return 0; // Placeholder
}

QFontMetrics EDA_BRD_TEXT_DATA::getFontMetrics() const {
    return QFontMetrics(QFont()); // Return default font metrics
}

// Private methods
void EDA_BRD_TEXT_DATA::updateBoundingBox() const {
    // Placeholder
}

void EDA_BRD_TEXT_DATA::invalidateBoundingBox() {
    // Placeholder
}
