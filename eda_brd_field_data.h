/*
 * This program source code file is part of KiCad, a free EDA CAD application.
 *
 * Copyright The KiCad Developers, see AUTHORS.txt for contributors.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, you may find one here:
 * http://www.gnu.org/licenses/old-licenses/gpl-2.0.html
 * or you may search the http://www.gnu.org website for the version 2 license,
 * or you may write to the Free Software Foundation, Inc.,
 * 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA
 */

#ifndef QT_PCB_FIELD_H
#define QT_PCB_FIELD_H

#include <QString>
#include <QVector>
#include "eda_brd_text_data.h"

// Forward declarations
class EDA_FOOTPRINT_DATA;
class BOARD_DESIGN_SETTINGS;
class UNITS_PROVIDER;

namespace google {
namespace protobuf {
class Any;
}
}

namespace KIGFX {
class VIEW;
}

// Field types enumeration
enum MANDATORY_FIELD_T {
    REFERENCE_FIELD = 0,          // Field Reference of part, i.e. "IC21"
    VALUE_FIELD,                  // Field Value of part, i.e. "3.3K"
    FOOTPRINT_FIELD,              // Field Name Module PCB, i.e. "16DIP300"
    DATASHEET_FIELD,              // name of datasheet
    DESCRIPTION_FIELD,            // Field Description of part, i.e. "1/4W 1% Metal Film Resistor"
    MANDATORY_FIELDS
};

// Item types are defined in qt_temporary_implementations.h as QtKicadType

class EDA_BRD_FIELD_DATA : public EDA_BRD_TEXT_DATA
{

public:
    // Constructors
    EDA_BRD_FIELD_DATA(EDA_FOOTPRINT_DATA* aParent, int aFieldId, const QString& aName = QString());
    EDA_BRD_FIELD_DATA(const EDA_BRD_TEXT_DATA& aText, int aFieldId, const QString& aName = QString());
    EDA_BRD_FIELD_DATA(const EDA_BRD_FIELD_DATA& aOther) = default;
    
    virtual ~EDA_BRD_FIELD_DATA() = default;

    // Static methods
    static bool classOf(const EDA_OBJECT_DATA* aItem);

    // Virtual overrides from base classes
    QString getClass() const override { return QStringLiteral("PCB_FIELD"); }
    bool isType(const QVector<QtKicadType>& aScanTypes) const override;
    
    // Serialization
    void serialize(google::protobuf::Any& aContainer) const;
    bool deserialize(const google::protobuf::Any& aContainer);

    // Field type queries
    bool isReference() const { return m_id == REFERENCE_FIELD; }
    bool isValue() const { return m_id == VALUE_FIELD; }
    bool isDatasheet() const { return m_id == DATASHEET_FIELD; }
    bool isComponentClass() const { return getName() == QStringLiteral("Component Class"); }
    bool isMandatory() const;
    bool isHypertext() const;

    // Text descriptions
    QString getTextTypeDescription() const override;
    QString getItemDescription(UNITS_PROVIDER* aUnitsProvider, bool aFull) const;

    // View management
    double viewGetLOD(int aLayer, const QtView* aView) const;

    // Cloning
    EDA_OBJECT_DATA* clone() const override;
    EDA_BRD_FIELD_DATA* cloneField() const { return static_cast<EDA_BRD_FIELD_DATA*>(clone()); }

    // Field name management
    QString getName(bool aUseDefaultName = true) const;
    QString getCanonicalName() const;
    void setName(const QString& aName);

    // Field ID management
    int getId() const { return m_id; }
    void setId(int aId) { m_id = aId; }

    // Comparison operators
    double similarity(const EDA_BOARD_OBJECT_DATA& aOther) const override;
    bool operator==(const EDA_BRD_FIELD_DATA& aOther) const;
    bool operator==(const EDA_BOARD_OBJECT_DATA& aOther) const override;


protected:
    // Protected methods
    void swapData(EDA_BOARD_OBJECT_DATA* aImage) override;

private:
    // Private methods
    void setIdInternal(int aId) { m_id = aId; }
    
    // Helper functions for field names
    static QString getCanonicalFieldName(int idx);
    static QString getUserFieldName(int aFieldNdx, bool aTranslate);

    // Member variables
    int m_id;           // Field index, see enum MANDATORY_FIELD_T
    QString m_name;     // Field name
};

#endif // QT_PCB_FIELD_H