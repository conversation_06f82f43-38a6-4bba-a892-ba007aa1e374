/*
 * This program source code file is part of KiCad, a free EDA CAD application.
 *
 * Copyright The KiCad Developers, see AUTHORS.txt for contributors.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, you may find one here:
 * http://www.gnu.org/licenses/old-licenses/gpl-2.0.html
 * or you may search the http://www.gnu.org website for the version 2 license,
 * or you may write to the Free Software Foundation, Inc.,
 * 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA
 */

#ifndef QT_NETINFO_ITEM_H
#define QT_NETINFO_ITEM_H

#include <QString>
#include <QSharedPointer>
#include <QRect>
#include <QVector2D>
#include "eda_board_object_data.h"

// 前向声明
class EDA_BOARD_DATA;
class QtNetclass;
class QtEdaDrawFrame;
class QtMsgPanelItem;
class QtEdaSearchData;

/**
 * Qt版本的网络信息项，处理网络数据
 */
class EDA_NET_DATA : public EDA_BOARD_OBJECT_DATA
{
private:
    // 网络属性
    int m_netCode;                              // 网络代码
    QString m_netname;                          // 完整网络名称
    QString m_shortNetname;                     // 短网络名称
    QString m_displayNetname;                   // 显示用网络名称

    // 网络类
    QSharedPointer<QtNetclass> m_netClass;

    // 状态
    bool m_isCurrent;                           // 当前是否在使用

    // 父对象
    EDA_BOARD_DATA* m_parent;

public:
    explicit EDA_NET_DATA(EDA_BOARD_DATA* parent = nullptr, const QString& netName = QString(), int netCode = -1);
    ~EDA_NET_DATA() override;

    // Qt对象系统
    static bool classOf(const EDA_OBJECT_DATA* item);
    QString getClassName() const override;
    QString getClass() const override;
    EDA_OBJECT_DATA* clone() const override;

    // 几何信息
    QRectF getBoundingBox() const override;
    QPointF getPosition() const override;
    void setPosition(const QPointF& pos) override;

    // 网络类管理
    void setNetClass(QSharedPointer<QtNetclass> netClass);
    QtNetclass* getNetClass() const;
    QSharedPointer<QtNetclass> getNetClassShared() const;

    // 网络代码管理
    int getNetCode() const { return m_netCode; }
    void setNetCode(int netCode);

    // 网络名称管理
    const QString& getNetname() const { return m_netname; }
    const QString& getShortNetname() const { return m_shortNetname; }
    const QString& getDisplayNetname() const { return m_displayNetname; }
    
    // Aliases for compatibility
    const QString& getNetName() const { return getNetname(); }
    const QString& getShortNetName() const { return getShortNetname(); }
    const QString& getDisplayNetName() const { return getDisplayNetname(); }
    
    void setNetname(const QString& newName);
    
    // Orphaned item functionality  
    bool orphanedItem() const { return m_netCode == 0; }

    // 状态管理
    bool isCurrent() const { return m_isCurrent; }
    void setIsCurrent(bool current);

    // 网络名称检测
    bool hasAutoGeneratedNetname() const;

    // 信息面板
    void getMsgPanelInfo(EDA_BOARD_DATA* frame, QVector<QtMsgPanelItem>& list) const override;

    // 清理功能
    void clear();

    // 父对象管理
    void setParent(EDA_BOARD_DATA* parent);
    EDA_BOARD_DATA* getParent() const;

    // 搜索功能
    bool matches(const QtEdaSearchData& searchData, void* auxData = nullptr) const override;

    // 比较功能
    double similarity(const EDA_BOARD_OBJECT_DATA& boardItem) const override;
    bool operator==(const EDA_BOARD_OBJECT_DATA& boardItem) const override;

    // 网络名称更新
    void updateDisplayNetname();

private:
    // 内部状态更新
    void onNetClassDestroyed();
    
    // 辅助方法
    void updateShortNetname();
    QString unescapeString(const QString& str) const;
    void connectNetClassSignals();
    void disconnectNetClassSignals();

};

#endif /* QT_NETINFO_ITEM_H */