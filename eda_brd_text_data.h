/*
 * This program source code file is part of KiCad, a free EDA CAD application.
 *
 * Qt-based implementation of PCB text
 */

#ifndef QT_PCB_TEXT_H
#define QT_PCB_TEXT_H

#include <QString>
#include <QPointF>
#include <QTransform>
#include <QSharedPointer>
#include <QVector>

#include "eda_board_object_data.h"
#include "eda_text_data.h"
#include "qt_temporary_implementations.h"

// Forward declarations
class QtLineReader;
class QtMsgPanelItem;
class EDA_FOOTPRINT_DATA;
class QtPcbEditFrame;
class QtBoardDesignSettings;
class QtShape;
class QtShapePolySet;
class QtEdaSearchData;
class QtBox2I;
class QtUnitsProvider;
class QtView;

// Type definitions - use proper enums from eda_board_object_data.h

class EDA_BRD_TEXT_DATA : public EDA_BOARD_OBJECT_DATA, public virtual EDA_TEXT_DATA
{

public:
    // Constructor for normal PCB text
    explicit EDA_BRD_TEXT_DATA(EDA_BOARD_OBJECT_CONTAINER* parent = nullptr, QtKicadType idtype = QtKicadType::PcbTextT);
    
    // Constructor for footprint text
    explicit EDA_BRD_TEXT_DATA(EDA_FOOTPRINT_DATA* parent, QtKicadType idtype = QtKicadType::PcbTextT);
    
    virtual ~EDA_BRD_TEXT_DATA();

    // Static factory method
    static bool classOf(const EDA_OBJECT_DATA* aItem);

    // Type identification
    QString getClassName() const override { return QStringLiteral("EDA_BRD_TEXT_DATA"); }
    QString getClass() const override { return QStringLiteral("PCB_TEXT"); }

    // Type checking
    virtual bool isType(const QVector<QtKicadType>& aScanTypes) const override;

    // Style management
    virtual void styleFromSettings(const QtBoardDesignSettings& settings) override;

    // Text orientation management
    void keepUpright();

    // Text content - from EDA_TEXT_DATA (not virtual in base)
    QString getShownText(bool aAllowExtraText = true, int aDepth = 0) const;

    // Search functionality
    virtual bool matches(const QtEdaSearchData& aSearchData, void* aAuxData = nullptr) const;

    // Position management
    virtual QPointF getPosition() const override;
    virtual void setPosition(const QPointF& aPos) override;
    virtual void move(const QPointF& aMoveVector) override;

    // Transformations
    virtual void rotate(const QPointF& aRotCentre, const QtEdaAngle& angleRadians);
    virtual void mirror(const QPointF& aCentre, QtFlipDirection aFlipDirection) override;
    virtual void flip(const QPointF& aCentre, QtFlipDirection aFlipDirection) override;

    // Information display - override from EDA_OBJECT_DATA (signature should match)
    virtual void getMsgPanelInfo(EDA_BOARD_DATA* frame, QVector<QtMsgPanelItem>& items) const override;

    // Hit testing
    virtual bool textHitTest(const QPointF& aPoint, int aAccuracy = 0) const;
    virtual bool textHitTest(const QtBox2I& aRect, bool aContains, int aAccuracy = 0) const;
    virtual bool hitTest(const QPointF& aPosition, double accuracy = 0.0) const override;
    virtual bool hitTest(const QRectF& aRect, bool aContained, double accuracy = 0.0) const override;

    // Note: getClass() is already declared above - removed duplicate

    // Shape conversion
    void transformTextToPolySet(QtShapePolySet& aBuffer, int aClearance, int aMaxError,
                               QtErrorLoc aErrorLoc) const;
                               
    virtual void transformShapeToPolygon(QtShapePolySet& aBuffer, QtPcbLayerId aLayer, 
                                       int aClearance, int aMaxError, QtErrorLoc aErrorLoc,
                                       bool aIgnoreLineWidth = false) const;

    // Shape management
    virtual std::shared_ptr<QtShape> getEffectiveShape(QtPcbLayerId aLayer = QtPcbLayerId::UndefinedLayer,
                                                     QtFlashing aFlash = QtFlashing::Default) const override;

    // Description methods
    virtual QString getTextTypeDescription() const;
    virtual QString getItemDescription(QtUnitsProvider* aUnitsProvider, bool aFull = true) const override;
    
    // UI support
    virtual QtBitmaps getMenuImage() const override;

    // Drawing rotation
    virtual QtEdaAngle getDrawRotation() const;

    // View support
    virtual QRectF viewBBox() const override;
    virtual QVector<int> viewGetLayers() const;
    virtual double viewGetLOD(int aLayer, const QtView* aView) const override;

    // Bounding box
    virtual QRectF getBoundingBox() const override;

    // Cloning
    virtual EDA_OBJECT_DATA* clone() const override;

    // Comparison (required by EDA_BOARD_OBJECT_DATA)
    double similarity(const EDA_BOARD_OBJECT_DATA& other) const override;
    bool operator==(const EDA_BOARD_OBJECT_DATA& other) const override;
    bool operator==(const EDA_BRD_TEXT_DATA& aOther) const;

protected:
    // Build bounding hull for rendered text
    void buildBoundingHull(QtShapePolySet* aBuffer, const QtShapePolySet& aRenderedText,
                          int aClearance) const;

    // Data swapping for undo/redo
    virtual void swapData(EDA_BOARD_OBJECT_DATA* aImage) override;

    // Get knockout margin
    int getKnockoutMargin() const;

    // Font metrics - override from EDA_TEXT_DATA (return type should match)
    virtual QFontMetrics getFontMetrics() const override;

private:
    // Additional properties specific to PCB text
    bool m_knockout = false;
    mutable QtBox2I m_cachedBoundingBox;
    mutable bool m_boundingBoxValid = false;

    // Helper methods
    void updateBoundingBox() const;
    void invalidateBoundingBox();

    // Constants
    static constexpr int PCB_TEXT_T = 1;  // Type identifier
    static constexpr int PCB_LOCATE_TEXT_T = 2;
    static constexpr int UNDEFINED_LAYER = -1;
    static constexpr int FLASHING_DEFAULT = 0;
    static constexpr double DEFAULT_TEXT_WIDTH = 0.15;  // mm
};

#endif // QT_PCB_TEXT_H