{"version": 3, "configurePresets": [{"name": "base", "hidden": true, "generator": "Ninja", "binaryDir": "${sourceDir}/build", "condition": {"type": "equals", "lhs": "${hostSystemName}", "rhs": "Windows"}, "cacheVariables": {"CMAKE_PREFIX_PATH": "C:/Qt/6.9.1/msvc2022_64", "Qt6_DIR": "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6", "CMAKE_C_COMPILER": "cl", "CMAKE_CXX_COMPILER": "cl", "CMAKE_CXX_STANDARD": "17", "CMAKE_AUTOMOC": "ON", "CMAKE_AUTORCC": "ON", "CMAKE_AUTOUIC": "ON", "CMAKE_EXPORT_COMPILE_COMMANDS": "ON"}}, {"name": "win64", "hidden": true, "cacheVariables": {"CMAKE_INSTALL_PREFIX": "out"}, "architecture": {"value": "x64", "strategy": "external"}, "inherits": ["base"]}, {"name": "x64-debug", "displayName": "x64 Debug", "description": "Sets debug build type and x64 arch with Qt support", "inherits": "win64", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug"}}, {"name": "x64-release", "displayName": "x64 Release", "description": "Sets release build type with Qt support", "inherits": "win64", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release"}}], "buildPresets": [{"name": "x64-debug-build", "configurePreset": "x64-debug"}, {"name": "x64-release-build", "configurePreset": "x64-release"}]}