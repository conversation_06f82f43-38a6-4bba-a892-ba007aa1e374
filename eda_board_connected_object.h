/*
 * This program source code file is part of KiCad, a free EDA CAD application.
 *
 * Copyright (C) 2012 <PERSON><PERSON><PERSON>, <EMAIL>
 * Copyright (C) 2012 SoftPLC Corporation, <PERSON> <<EMAIL>>
 * Copyright The KiCad Developers, see Authors.txt for contributors.
 * Copyright (C) 2024 KiCad to Qt Migration Project
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */

#ifndef QT_BOARD_CONNECTED_ITEM_H
#define QT_BOARD_CONNECTED_ITEM_H

#include "eda_board_object_data.h"
#include <QtCore/QHash>
#include <QtCore/QStringList>
#include <QtCore/QDebug>
#include <QtCore/QLoggingCategory>
#include <QtCore/QVariant>
#include <optional>
#include <memory>

Q_DECLARE_LOGGING_CATEGORY(eDA_BOARD_CONNECTED_OBJECT)

// Forward declarations for classes not in migration scope
class QtNetclass;
class EDA_NET_DATA;
class EDA_BOARD_DATA;
class QtDrcConstraint;
class QtBoardDesignSettings;

// Teardrop target types
enum class QtTeardropTarget {
    Unknown = -1,
    Round = 0,
    Rect = 1,
    Track = 2,
    Count = 3
};

// Teardrop parameters structure
struct QtTeardropParameters {
    // Size constraints in internal units
    int maxLength = 1000000;      // 1.0mm default
    int maxWidth = 2000000;       // 2.0mm default
    
    // Ratio parameters
    double bestLengthRatio = 0.5;
    double bestWidthRatio = 1.0;
    double widthToSizeFilterRatio = 0.9;
    
    // Feature flags
    bool curvedEdges = false;
    bool enabled = false;
    bool allowUseTwoTracks = true;
    bool onPadsInZones = false;
    
    // Operators
    bool operator==(const QtTeardropParameters& other) const {
        return enabled == other.enabled &&
               allowUseTwoTracks == other.allowUseTwoTracks &&
               maxLength == other.maxLength &&
               maxWidth == other.maxWidth &&
               bestLengthRatio == other.bestLengthRatio &&
               bestWidthRatio == other.bestWidthRatio &&
               curvedEdges == other.curvedEdges &&
               widthToSizeFilterRatio == other.widthToSizeFilterRatio &&
               onPadsInZones == other.onPadsInZones;
    }
    
    bool operator!=(const QtTeardropParameters& other) const {
        return !(*this == other);
    }
    
    // Qt serialization support
    QVariantMap toVariantMap() const;
    void fromVariantMap(const QVariantMap& map);
};

/**
 * @brief Qt-based board connected item class
 * 
 * A base class derived from EDA_BOARD_OBJECT_DATA for items that can be connected 
 * and have a net, netname, clearance, and other connection-related properties.
 * This class provides comprehensive network management, clearance handling,
 * and teardrop functionality using Qt's object system.
 */
class EDA_BOARD_CONNECTED_OBJECT : public EDA_BOARD_OBJECT_DATA
{

public:
    explicit EDA_BOARD_CONNECTED_OBJECT(EDA_BOARD_OBJECT_DATA* parent = nullptr, 
                                  QtKicadType itemType = QtKicadType::BoardConnectedItem);
    ~EDA_BOARD_CONNECTED_OBJECT() override;

    // Type checking
    static bool classOf(const EDA_OBJECT_DATA* item);
    bool isConnected() const override { return true; }
    
    // Network management
    EDA_NET_DATA* getNet() const { return m_netInfo; }
    void setNet(EDA_NET_DATA* netInfo);
    
    int getNetCode() const;
    bool setNetCode(int netCode, bool noAssert = false);
    
    QString getNetName() const;
    QString getNetNameMessage() const;
    const QString& getShortNetName() const;
    const QString& getDisplayNetName() const;
    
    // Network class management
    QtNetclass* getEffectiveNetClass() const;
    QString getNetClassName() const;
    
    // Clearance management
    virtual int getOwnClearance(QtPcbLayerId layer, QString* source = nullptr) const;
    virtual std::optional<int> getClearanceOverrides(QString* source = nullptr) const;
    virtual std::optional<int> getLocalClearance(QString* source = nullptr) const;
    
    // Ratsnest visibility
    bool getLocalRatsnestVisible() const { return m_localRatsnestVisible; }
    void setLocalRatsnestVisible(bool visible);
    
    // Teardrop parameters access
    QtTeardropParameters& getTeardropParams() { return m_teardropParams; }
    const QtTeardropParameters& getTeardropParams() const { return m_teardropParams; }
    
    // Teardrop property accessors
    bool getTeardropsEnabled() const { return m_teardropParams.enabled; }
    void setTeardropsEnabled(bool enabled);
    
    double getTeardropBestLengthRatio() const { return m_teardropParams.bestLengthRatio; }
    void setTeardropBestLengthRatio(double ratio);
    
    int getTeardropMaxLength() const { return m_teardropParams.maxLength; }
    void setTeardropMaxLength(int maxLength);
    
    double getTeardropBestWidthRatio() const { return m_teardropParams.bestWidthRatio; }
    void setTeardropBestWidthRatio(double ratio);
    
    int getTeardropMaxWidth() const { return m_teardropParams.maxWidth; }
    void setTeardropMaxWidth(int maxWidth);
    
    bool getTeardropCurved() const { return m_teardropParams.curvedEdges; }
    void setTeardropCurved(bool curved);
    
    bool getTeardropPreferZoneConnections() const { return !m_teardropParams.onPadsInZones; }
    void setTeardropPreferZoneConnections(bool prefer);
    
    bool getTeardropAllowSpanTwoTracks() const { return m_teardropParams.allowUseTwoTracks; }
    void setTeardropAllowSpanTwoTracks(bool allow);
    
    double getTeardropMaxTrackWidth() const { return m_teardropParams.widthToSizeFilterRatio; }
    void setTeardropMaxTrackWidth(double ratio);
    
    // Qt serialization
    QVariantMap toVariantMap() const override;
    void fromVariantMap(const QVariantMap& map) override;
    
    // Qt debugging
    QString toString() const override;

    // Network management methods
    void updateNetworkInfo();
    void invalidateNetworkCache();
    
    // Teardrop management methods
    void resetTeardropParameters();
    void applyTeardropTemplate(const QtTeardropParameters& params);

protected:
    // Core network data
    EDA_NET_DATA* m_netInfo;
    
    // Teardrop parameters
    QtTeardropParameters m_teardropParams;
    
    // UI state
    bool m_localRatsnestVisible;
    
    // Performance caching
    mutable QHash<QtPcbLayerId, int> m_clearanceCache;
    mutable bool m_clearanceCacheValid;
    
    // Network information caching
    mutable QString m_cachedNetName;
    mutable QString m_cachedShortNetName;
    mutable QString m_cachedDisplayNetName;
    mutable QString m_cachedNetClassName;
    mutable bool m_netInfoCacheValid;
    
    // Helper methods
    void invalidateClearanceCache();
    void invalidateNetInfoCache();
    void updateNetInfoCache() const;
    
    // Network info signal helpers (simplified)
    void connectNetInfoSignals();
    void disconnectNetInfoSignals();

protected:
    // Allow copy constructor for derived classes like EDA_BRD_SHAPE_DATA
    EDA_BOARD_CONNECTED_OBJECT(const EDA_BOARD_CONNECTED_OBJECT& other);
    EDA_BOARD_CONNECTED_OBJECT& operator=(const EDA_BOARD_CONNECTED_OBJECT&) = delete;
};

// Utility functions for teardrop parameters
namespace QtTeardropUtils {
    QString getTargetCanonicalName(QtTeardropTarget target);
    QtTeardropTarget getTargetFromCanonicalName(const QString& name);
    QStringList getAllTargetNames();
    
    // Teardrop parameter validation
    bool validateParameters(const QtTeardropParameters& params, QString* error = nullptr);
    QtTeardropParameters getDefaultParameters(QtTeardropTarget target);
}


#endif // QT_BOARD_CONNECTED_ITEM_H