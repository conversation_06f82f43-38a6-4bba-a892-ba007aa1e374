/*
 * This program source code file is part of KiCad, a free EDA CAD application.
 *
 * Copyright (C) 2016 CERN
 * Copyright The KiCad Developers, see AUTHORS.txt for contributors.
 * Copyright (C) 2024 KiCad to Qt Migration Project
 *
 * <AUTHOR> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */

#ifndef EDA_BOARD_OBJECT_CONTAINER_H
#define EDA_BOARD_OBJECT_CONTAINER_H

#include "eda_board_object_data.h"
#include <QtCore/QList>
#include <QtCore/QDebug>
#include <QtCore/QLoggingCategory>
#include <functional>

// Logging category removed (Qt object system removal)

// Forward declarations for classes not in migration scope
struct QtZoneSettings;

/**
 * @brief Qt-based item addition modes for board item containers
 */
enum class QtAddMode {
    Insert = 0,      ///< Insert item at the beginning
    Append = 1,      ///< Append item at the end
    BulkAppend = 2,  ///< Bulk append mode (optimized for multiple items)
    BulkInsert = 3   ///< Bulk insert mode (optimized for multiple items)
};
// Qt enum registration removed

/**
 * @brief Qt-based item removal modes for board item containers
 */
enum class QtRemoveMode {
    Normal = 0,      ///< Normal removal with full processing
    Bulk = 1         ///< Bulk removal mode (optimized for multiple items)
};

/**
 * @brief Abstract interface for board items capable of storing other items
 * 
 * This class provides the interface for containers like EDA_BOARD_DATA and EDA_FOOTPRINT_DATA
 * that can hold collections of board items. Uses standard C++ with Qt containers
 * and geometry classes but without Qt object system.
 */
class EDA_BOARD_OBJECT_CONTAINER : public EDA_BOARD_OBJECT_DATA
{

public:
    explicit EDA_BOARD_OBJECT_CONTAINER(EDA_BOARD_OBJECT_DATA* parent = nullptr, 
                                  QtKicadType itemType = QtKicadType::BoardItemContainer);
    ~EDA_BOARD_OBJECT_CONTAINER() override;

    // Type checking
    static bool classOf(const EDA_OBJECT_DATA* item);
    bool isContainer() const override { return true; }
    
    // Pure virtual container interface - must be implemented by derived classes
    
    /**
     * @brief Add an item to the container
     * @param item Item to add to the container
     * @param mode Addition mode controlling where/how item is added
     * @param skipConnectivity Skip connectivity update (useful during bulk operations)
     */
    virtual void addItem(EDA_BOARD_OBJECT_DATA* item, QtAddMode mode = QtAddMode::Insert,
                        bool skipConnectivity = false) = 0;

    /**
     * @brief Remove an item from the container
     * @param item Item to remove from the container
     * @param mode Removal mode controlling how removal is processed
     */
    virtual void removeItem(EDA_BOARD_OBJECT_DATA* item, QtRemoveMode mode = QtRemoveMode::Normal) = 0;

    /**
     * @brief Remove an item from container and delete it
     * @param item Item to delete
     */
    virtual void deleteItem(EDA_BOARD_OBJECT_DATA* item) {
        removeItem(item);
        delete item;
    }
    
    // Container state queries
    virtual int getItemCount() const = 0;
    virtual bool isEmpty() const = 0;
    virtual bool contains(const EDA_BOARD_OBJECT_DATA* item) const = 0;
    
    // Container iteration support
    virtual QList<EDA_BOARD_OBJECT_DATA*> getAllItems() const = 0;
    virtual QList<EDA_BOARD_OBJECT_DATA*> getItemsByType(QtKicadType type) const = 0;
    
    // Bulk operations support
    virtual void beginBulkOperation() {}
    virtual void endBulkOperation() {}
    
    // Container modification tracking
    bool isModified() const { return m_isModified; }
    void setModified(bool modified);
    
    // Serialization
    QVariantMap toVariantMap() const override;
    void fromVariantMap(const QVariantMap& map) override;
    
    // Debugging
    QString toString() const override;

    // Container management methods
    virtual void clear() = 0;
    virtual void optimizeStorage() {}
    virtual void validateContainer() {}
    
    // Bulk operation helpers
    void addItems(const QList<EDA_BOARD_OBJECT_DATA*>& items, QtAddMode mode = QtAddMode::Insert);
    void removeItems(const QList<EDA_BOARD_OBJECT_DATA*>& items, QtRemoveMode mode = QtRemoveMode::Normal);

    // Callback mechanisms to replace Qt signals
    using ItemAddedCallback = std::function<void(EDA_BOARD_OBJECT_DATA*)>;
    using ItemRemovedCallback = std::function<void(EDA_BOARD_OBJECT_DATA*)>;
    using ItemCountChangedCallback = std::function<void(int)>;
    using ModifiedChangedCallback = std::function<void(bool)>;
    using ContainerClearedCallback = std::function<void()>;
    using BulkOperationCallback = std::function<void()>;
    
    // Callback setters
    void setItemAddedCallback(ItemAddedCallback callback) { m_itemAddedCallback = std::move(callback); }
    void setItemRemovedCallback(ItemRemovedCallback callback) { m_itemRemovedCallback = std::move(callback); }
    void setItemCountChangedCallback(ItemCountChangedCallback callback) { m_itemCountChangedCallback = std::move(callback); }
    void setModifiedChangedCallback(ModifiedChangedCallback callback) { m_modifiedChangedCallback = std::move(callback); }

protected:
    // Container state
    bool m_isModified;
    
    // Bulk operation state
    bool m_inBulkOperation;
    int m_bulkOperationCounter;
    
    // Callback function members
    ItemAddedCallback m_itemAddedCallback;
    ItemRemovedCallback m_itemRemovedCallback;
    ItemCountChangedCallback m_itemCountChangedCallback;
    ModifiedChangedCallback m_modifiedChangedCallback;
    
    // Helper methods for derived classes
    void notifyItemAdded(EDA_BOARD_OBJECT_DATA* item);
    void notifyItemRemoved(EDA_BOARD_OBJECT_DATA* item);
    void updateModifiedState();
    
    // Internal item management
    void onChildItemModified();

private:

    
    // Disable copy constructor and assignment operator
    EDA_BOARD_OBJECT_CONTAINER(const EDA_BOARD_OBJECT_CONTAINER&) = delete;
    EDA_BOARD_OBJECT_CONTAINER& operator=(const EDA_BOARD_OBJECT_CONTAINER&) = delete;
};

// Utility functions for container operations
namespace QtContainerUtils {
    /**
     * @brief Get canonical name for add mode
     */
    QString getAddModeCanonicalName(QtAddMode mode);
    
    /**
     * @brief Get add mode from canonical name
     */
    QtAddMode getAddModeFromCanonicalName(const QString& name);
    
    /**
     * @brief Get canonical name for remove mode
     */
    QString getRemoveModeCanonicalName(QtRemoveMode mode);
    
    /**
     * @brief Get remove mode from canonical name
     */
    QtRemoveMode getRemoveModeFromCanonicalName(const QString& name);
    
    /**
     * @brief Get all add mode names
     */
    QStringList getAllAddModeNames();
    
    /**
     * @brief Get all remove mode names
     */
    QStringList getAllRemoveModeNames();
    
    /**
     * @brief Validate container operations
     */
    bool validateContainerOperation(const EDA_BOARD_OBJECT_CONTAINER* container,
                                   const EDA_BOARD_OBJECT_DATA* item,
                                   QString* error = nullptr);
}



#endif // EDA_BOARD_OBJECT_CONTAINER_H