/*
 * Qt-based reimplementation of KiCad EDA_TEXT class
 * 
 * MIGRATION STATUS: Qt object system removed (2025-08-05)
 * - ✅ Removed QObject inheritance, Q_OBJECT, Q_PROPERTY, signals
 * - ✅ Preserved Qt containers (QVector, QList, QHash, etc.)
 * - ✅ Preserved Qt geometry (QPointF, QRectF, QTransform, etc.)
 * - ✅ Preserved Qt strings (QString) for Unicode support
 * - ✅ Converted to standard C++ class with Qt utility integration
 * 
 * This class represents text objects with comprehensive typography support,
 * using Qt frameworks for better performance, internationalization, and 
 * integration with Qt's graphics and font systems.
 */

#pragma once

#include <QtCore/QString>
#include <QtCore/QVector>
#include <QtCore/QPointF>
#include <QtCore/QRectF>
#include <QtCore/QSizeF>
#include <QtCore/QUrl>
#include <QtCore/QHash>
#include <QtCore/QJsonObject>
#include <QtCore/QJsonArray>
#include <QtGui/QColor>
#include <QtGui/QFont>
#include <QtGui/QFontMetrics>
#include <QtGui/QPainterPath>
#include <QtGui/QTextLayout>
#include <QtGui/QTransform>
#include <memory>

// Forward declarations for dependencies not in migration scope
class QtSerializable;
class QtEdaDrawFrame;
class QtMsgPanelItem;
class QtUnitsProvider;
class QtSearchData;
class QtOutputFormatter;
class QtShapeCompound;
class QtShapePolySet;
class QtRenderSettings;

// Qt-based enums with Q_ENUM for better introspection
enum class QtTextHAlign : int {
    Left = -1,
    Center = 0,
    Right = 1,
    Indeterminate = 2
};

enum class QtTextVAlign : int {
    Top = -1,
    Center = 0,
    Bottom = 1,  
    Indeterminate = 2
};

// Text constraints
namespace QtTextLimits {
    constexpr double MIN_SIZE_MM = 0.001;    // Minimum text size (1 micron)
    constexpr double MAX_SIZE_MM = 250.0;    // Maximum text size (~10 inches)
    constexpr int DEFAULT_SIZE_MILS = 50;    // Default text height in mils
}

// Qt-based text attributes structure with complete type safety
struct QtTextAttributes {
    QFont font;                          // Qt native font
    QString fontFamily;                  // Font family name 
    QtTextHAlign horizontalAlign = QtTextHAlign::Left;
    QtTextVAlign verticalAlign = QtTextVAlign::Bottom;
    double angleRadians = 0.0;           // Rotation angle in radians
    double lineSpacing = 1.0;            // Line spacing multiplier
    int strokeWidth = 0;                 // Text outline width
    bool italic = false;
    bool bold = false;
    bool underlined = false;
    QColor color = QColor(Qt::black);
    bool mirrored = false;
    bool multiline = false;
    QSizeF size = QSizeF(1.27, 1.27);   // Text size in mm
    bool keepUpright = false;            // Keep readable orientation
    int storedStrokeWidth = 0;           // Stored stroke width for bold toggle

    QtTextAttributes() = default;
    QtTextAttributes(const QFont& f) : font(f), fontFamily(f.family()) {}
    
    bool operator==(const QtTextAttributes& other) const;
    bool operator!=(const QtTextAttributes& other) const { return !(*this == other); }
    
    // Convert angle between degrees and radians
    double angleDegrees() const { return angleRadians * 180.0 / M_PI; }
    void setAngleDegrees(double degrees) { angleRadians = degrees * M_PI / 180.0; }
};

// Cache entry for bounding box calculations
struct QtBBoxCacheEntry {
    QPointF position;
    QRectF boundingBox;
    
    QtBBoxCacheEntry() = default;
    QtBBoxCacheEntry(const QPointF& pos, const QRectF& bbox) 
        : position(pos), boundingBox(bbox) {}
};

/**
 * @brief Qt-based reimplementation of KiCad's EDA_TEXT class
 * 
 * This class provides comprehensive text handling with Qt's advanced typography
 * system, including full Unicode support, advanced font rendering, and 
 * integrated internationalization capabilities.
 */
class EDA_TEXT_DATA
{

public:
    //==========================================================================
    // CONSTRUCTION AND DESTRUCTION
    //==========================================================================
    explicit EDA_TEXT_DATA(const QString& text = QString());
    explicit EDA_TEXT_DATA(const EDA_TEXT_DATA& other);
    ~EDA_TEXT_DATA();

    //==========================================================================
    // TEXT CONTENT AND PROCESSING
    //==========================================================================
    const QString& getText() const { return m_text; }
    QString getShownText(bool allowExtraText = true, int depth = 0) const;
    void setText(const QString& text);
    void copyText(const EDA_TEXT_DATA& source);
    
    bool hasTextVars() const { return m_shownTextHasVars; }
    bool isEmpty() const { return m_text.isEmpty(); }
    void clear();

    //==========================================================================
    // TYPOGRAPHY AND FONT MANAGEMENT
    //==========================================================================
    const QFont& getFont() const { return m_attributes.font; }
    void setFont(const QFont& font);
    QString getFontFamily() const { return m_attributes.fontFamily; }
    void setFontFamily(const QString& family);
    int getFontIndex() const;
    void setFontIndex(int index);
    
    // Font resolution and embedding support
    void setUnresolvedFontName(const QString& fontName);
    bool resolveFont(const QStringList& embeddedFonts = QStringList());
    QString getFontName() const;

    //==========================================================================
    // TEXT SIZE AND DIMENSIONS
    //==========================================================================
    QSizeF getTextSize() const { return m_attributes.size; }
    void setTextSize(const QSizeF& size, bool enforceMinSize = true);
    void setTextSize(double width, double height, bool enforceMinSize = true);
    
    double getTextWidth() const { return m_attributes.size.width(); }
    double getTextHeight() const { return m_attributes.size.height(); }
    void setTextWidth(double width);
    void setTextHeight(double height);

    //==========================================================================
    // POSITION AND TRANSFORMATION
    //==========================================================================
    QPointF getPosition() const { return m_position; }
    void setPosition(const QPointF& position);
    void setPosition(double x, double y);
    void setX(double x);
    void setY(double y);
    
    void offset(const QPointF& offset);
    void move(const QPointF& delta) { offset(delta); }

    //==========================================================================
    // ROTATION AND ANGLE MANAGEMENT
    //==========================================================================
    double getAngleRadians() const { return m_attributes.angleRadians; }
    double getAngleDegrees() const { return m_attributes.angleDegrees(); }
    void setAngleRadians(double radians);
    void setAngleDegrees(double degrees);
    
    QPointF getDrawPosition() const { return getPosition(); }
    double getDrawAngleRadians() const { return getAngleRadians(); }
    double getDrawAngleDegrees() const { return getAngleDegrees(); }

    //==========================================================================
    // TEXT STYLE PROPERTIES
    //==========================================================================
    bool isBold() const { return m_attributes.bold; }
    void setBold(bool bold);
    void setBoldFlag(bool bold);  // Set flag without font update
    
    bool isItalic() const { return m_attributes.italic; }
    void setItalic(bool italic);  
    void setItalicFlag(bool italic);  // Set flag without font update
    
    bool isUnderlined() const { return m_attributes.underlined; }
    void setUnderlined(bool underlined);
    
    QString getStyleName() const;

    //==========================================================================
    // STROKE AND COLOR PROPERTIES
    //==========================================================================
    int getStrokeWidth() const { return m_attributes.strokeWidth; }
    void setStrokeWidth(int width);
    int getEffectiveStrokeWidth(int defaultWidth = 0) const;
    
    QColor getTextColor() const { return m_attributes.color; }
    void setTextColor(const QColor& color);

    //==========================================================================
    // VISIBILITY AND TRANSFORMATION PROPERTIES
    //==========================================================================
    bool isVisible() const { return m_visible; }
    void setVisible(bool visible);
    
    bool isMirrored() const { return m_attributes.mirrored; }
    void setMirrored(bool mirrored);
    
    bool isKeepUpright() const { return m_attributes.keepUpright; }
    void setKeepUpright(bool keepUpright);

    //==========================================================================
    // TEXT ALIGNMENT AND LAYOUT
    //==========================================================================
    QtTextHAlign getHorizontalAlign() const { return m_attributes.horizontalAlign; }
    void setHorizontalAlign(QtTextHAlign align);
    
    QtTextVAlign getVerticalAlign() const { return m_attributes.verticalAlign; }
    void setVerticalAlign(QtTextVAlign align);
    
    void flipHorizontalAlignment();
    
    bool isMultiline() const { return m_attributes.multiline; }
    void setMultiline(bool multiline);
    
    double getLineSpacing() const { return m_attributes.lineSpacing; }
    void setLineSpacing(double spacing);
    double getInterline() const;

    //==========================================================================
    // TEXT ATTRIBUTES MANAGEMENT
    //==========================================================================
    const QtTextAttributes& getAttributes() const { return m_attributes; }
    void setAttributes(const QtTextAttributes& attributes);
    void setAttributes(const EDA_TEXT_DATA& source, bool includePosition = true);
    void swapAttributes(EDA_TEXT_DATA& other);
    void swapText(EDA_TEXT_DATA& other);

    //==========================================================================
    // HYPERLINK SUPPORT
    //==========================================================================
    bool hasHyperlink() const { return !m_hyperlink.isEmpty(); }
    QUrl getHyperlink() const { return m_hyperlink; }
    void setHyperlink(const QUrl& url);
    void setHyperlink(const QString& url);
    void removeHyperlink();
    
    // Static hyperlink validation utilities
    static bool isValidHyperlink(const QUrl& url);
    static bool isValidHyperlink(const QString& url);
    static bool isInternalPageLink(const QString& href, QString* destination = nullptr);
    static QString createPageLink(const QString& destination);

    //==========================================================================
    // GEOMETRIC CALCULATIONS AND HIT TESTING
    //==========================================================================
    QRectF getBoundingRect(int line = -1) const;
    QRectF getTextBox(int line = -1) const { return getBoundingRect(line); }
    
    bool hitTest(const QPointF& point, double accuracy = 0.0) const;
    bool hitTest(const QRectF& rect, bool contained = false, double accuracy = 0.0) const;

    //==========================================================================
    // MULTILINE TEXT OPERATIONS
    //==========================================================================
    QVector<QPointF> getLinePositions(int lineCount) const;
    int getLineCount() const;
    QStringList getTextLines() const;

    //==========================================================================
    // SEARCH AND REPLACE FUNCTIONALITY
    //==========================================================================
    bool replace(const QtSearchData& searchData);
    bool matches(const QString& searchText, const QtSearchData& searchData) const;

    //==========================================================================
    // SHAPE AND RENDERING SUPPORT
    //==========================================================================
    std::shared_ptr<QtShapeCompound> getEffectiveTextShape(bool triangulate = true,
                                                           const QRectF& bbox = QRectF(),
                                                           double angle = 0.0) const;
    QPainterPath toPainterPath() const;
    
    // Rendering support
    void print(const QtRenderSettings* settings, const QPointF& offset, 
               const QColor& color, bool filled = true);

    //==========================================================================
    // CACHING AND PERFORMANCE OPTIMIZATION
    //==========================================================================
    void clearRenderCache();
    void clearBoundingBoxCache();
    void invalidateCache() { clearRenderCache(); clearBoundingBoxCache(); }
    
    // Setup render cache for performance
    void setupRenderCache(const QString& resolvedText, const QFont& font,
                         double angle, const QPointF& offset);

    //==========================================================================
    // COMPARISON AND SIMILARITY
    //==========================================================================
    int compare(const EDA_TEXT_DATA& other) const;
    double similarity(const EDA_TEXT_DATA& other) const;
    double levenshteinDistance(const EDA_TEXT_DATA& other) const;
    
    bool operator==(const EDA_TEXT_DATA& other) const { return compare(other) == 0; }
    bool operator!=(const EDA_TEXT_DATA& other) const { return compare(other) != 0; }
    bool operator<(const EDA_TEXT_DATA& other) const { return compare(other) < 0; }
    bool operator>(const EDA_TEXT_DATA& other) const { return compare(other) > 0; }

    //==========================================================================
    // FORMATTING AND DEFAULT STATE
    //==========================================================================
    bool isDefaultFormatting() const;

    //==========================================================================
    // SERIALIZATION SUPPORT
    //==========================================================================
    QJsonObject serialize() const;
    bool deserialize(const QJsonObject& json);
    
    // Text-specific serialization
    QJsonObject toJson() const;
    bool fromJson(const QJsonObject& json);

    //==========================================================================
    // USER INTERFACE SUPPORT  
    //==========================================================================
    QString getItemDescription(QtUnitsProvider* unitsProvider = nullptr, bool full = false) const;

    //==========================================================================
    // OUTPUT AND FORMATTING
    //==========================================================================
    void format(QtOutputFormatter* formatter, int controlBits) const;

    //==========================================================================
    // UTILITY METHODS
    //==========================================================================
    EDA_TEXT_DATA* clone() const;
    
    // Static utilities for alignment conversion
    static QtTextHAlign mapHorizontalAlign(int align);
    static QtTextVAlign mapVerticalAlign(int align);
    static QString alignmentToString(QtTextHAlign align);
    static QString alignmentToString(QtTextVAlign align);
    static QtTextHAlign alignmentFromString(const QString& str);
    static QtTextVAlign vAlignmentFromString(const QString& str);


protected:
    //==========================================================================
    // PROTECTED HELPER METHODS
    //==========================================================================
    virtual QFont getDrawFont() const;
    virtual QFontMetrics getFontMetrics() const;
    virtual void cacheShownText();
    
    void printSingleLine(const QtRenderSettings* settings, const QPointF& offset,
                        const QColor& color, bool filled, const QString& text,
                        const QPointF& position);
    
    void updateDerivedProperties();    // Update computed properties
    void applyFontStyle();             // Apply bold/italic to font
    void enforceTextSizeLimits();      // Enforce min/max size constraints

private:
    //==========================================================================
    // PRIVATE DATA MEMBERS
    //==========================================================================
    
    // Core text data
    QString m_text;                    // Raw text content
    mutable QString m_shownText;       // Processed text for display
    mutable bool m_shownTextHasVars;   // Cached flag for text variable presence
    QPointF m_position;                // Text position
    bool m_visible;                    // Visibility flag
    
    // Text attributes
    QtTextAttributes m_attributes;     // All text styling attributes
    QString m_unresolvedFontName;      // Font name before resolution
    
    // Hyperlink support
    QUrl m_hyperlink;                  // Hyperlink URL
    
    // Qt-specific optimizations and caching
    mutable QFont m_renderCacheFont;              // Cached font for rendering
    mutable QString m_renderCacheText;            // Cached text for rendering  
    mutable double m_renderCacheAngle;            // Cached angle for rendering
    mutable QPointF m_renderCacheOffset;          // Cached offset for rendering
    mutable QHash<int, QtBBoxCacheEntry> m_bboxCache;  // Bounding box cache
    
    // Text layout and measurement cache
    mutable QTextLayout* m_textLayout;            // Qt text layout engine
    mutable bool m_layoutValid;                   // Layout cache validity
    mutable QRectF m_cachedBoundingRect;          // Cached bounding rectangle
    mutable bool m_boundingRectValid;             // Bounding rect validity
    
    void invalidateLayout();           // Invalidate text layout cache
    void ensureLayoutValid() const;    // Ensure text layout is valid
    void updateTextLayout() const;     // Update Qt text layout
};

/**
 * @brief Qt container type aliases for text objects
 */
using EDA_TEXT_DATAs = QVector<EDA_TEXT_DATA*>;
using EDA_TEXT_DATAList = QList<EDA_TEXT_DATA*>;

/**
 * @brief Helper functions for text operations
 */
inline EDA_TEXT_DATA* newClone(const EDA_TEXT_DATA& text) { 
    return text.clone(); 
}

// Flip alignment helper functions
inline QtTextHAlign getFlippedAlignment(QtTextHAlign align) {
    switch (align) {
        case QtTextHAlign::Left: return QtTextHAlign::Right;
        case QtTextHAlign::Right: return QtTextHAlign::Left;
        default: return align;
    }
}

inline QtTextVAlign getFlippedAlignment(QtTextVAlign align) {
    switch (align) {
        case QtTextVAlign::Top: return QtTextVAlign::Bottom;
        case QtTextVAlign::Bottom: return QtTextVAlign::Top;
        default: return align;
    }
}


// Hash function for QHash support
inline uint qHash(const QtTextAttributes& attrs, uint seed = 0) {
    // QColor doesn't have a default hash, so we'll use its rgba value
    return qHashMulti(seed, attrs.fontFamily, attrs.horizontalAlign, attrs.verticalAlign,
                      attrs.angleRadians, attrs.lineSpacing, attrs.strokeWidth,
                      attrs.italic, attrs.bold, attrs.underlined, attrs.color.rgba(),
                      attrs.mirrored, attrs.multiline, attrs.size.width(), attrs.size.height());
}