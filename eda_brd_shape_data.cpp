/*
 * Qt-based reimplementation of KiCad PCB_SHAPE class
 * 
 * Copyright The KiCad Developers
 * Copyright (C) 2024 KiCad to Qt Migration Project
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 */

#include "eda_brd_shape_data.h"
#include "eda_board_data.h"
#include "eda_footprint_data.h"
#include "eda_pad_data.h"
#include "qt_temporary_implementations.h"

#include <QtCore/QDebug>
#include <QtCore/QMutexLocker>
#include <QtCore/QJsonArray>
#include <QtCore/QJsonValue>
#include <QtGui/QPainterPath>
#include <QtMath>
#include <algorithm>
#include <limits>

// Constants
static constexpr double DEFAULT_LINE_WIDTH_MM = 0.2;
static constexpr int DEFAULT_LINE_WIDTH_IU = 200000; // 0.2mm in internal units

//=============================================================================
// CONSTRUCTION AND DESTRUCTION
//=============================================================================

EDA_BRD_SHAPE_DATA::EDA_BRD_SHAPE_DATA(EDA_BOARD_OBJECT_DATA* parent, QtKicadType itemType, QtShapeType shapeType)
    : EDA_BOARD_CONNECTED_OBJECT(parent, itemType)
    , EDA_SHAPE_DATA(shapeType, DEFAULT_LINE_WIDTH_IU, QtFillType::NoFill)
    , m_layer(QtPcbLayerId::FCu)
    , m_hasSolderMask(false)
{
    syncProperties();
}

EDA_BRD_SHAPE_DATA::EDA_BRD_SHAPE_DATA(EDA_BOARD_OBJECT_DATA* parent, QtShapeType shapeType)
    : EDA_BOARD_CONNECTED_OBJECT(parent, QtKicadType::PcbShape)
    , EDA_SHAPE_DATA(shapeType, DEFAULT_LINE_WIDTH_IU, QtFillType::NoFill)
    , m_layer(QtPcbLayerId::FCu)
    , m_hasSolderMask(false)
{
    syncProperties();
}

EDA_BRD_SHAPE_DATA::EDA_BRD_SHAPE_DATA(const EDA_BRD_SHAPE_DATA& other)
    : EDA_BOARD_CONNECTED_OBJECT(other)
    , EDA_SHAPE_DATA(other)
    , m_layer(other.m_layer)
    , m_hasSolderMask(other.m_hasSolderMask)
    , m_solderMaskMargin(other.m_solderMaskMargin)
{
    syncProperties();
}

EDA_BRD_SHAPE_DATA::~EDA_BRD_SHAPE_DATA()
{
}

bool EDA_BRD_SHAPE_DATA::classOf(const EDA_OBJECT_DATA* item)
{
    return item && item->getType() == QtKicadType::PcbShape;
}

//=============================================================================
// SERIALIZATION
//=============================================================================

void EDA_BRD_SHAPE_DATA::serialize(QVariantMap& container) const
{
    QMutexLocker locker(&m_mutex);
    
    // Serialize base class data - EDA_BOARD_CONNECTED_OBJECT doesn't have serialize method
    // EDA_BOARD_CONNECTED_OBJECT::serialize(container);
    
    // Serialize shape data
    QVariantMap shapeData = EDA_SHAPE_DATA::serialize();
    container["shape"] = shapeData;
    
    // Serialize PCB_SHAPE specific data
    container["layer"] = static_cast<int>(m_layer);
    container["hasSolderMask"] = m_hasSolderMask;
    if (m_solderMaskMargin.has_value()) {
        container["solderMaskMargin"] = m_solderMaskMargin.value();
    }
}

bool EDA_BRD_SHAPE_DATA::deserialize(const QVariantMap& container)
{
    QMutexLocker locker(&m_mutex);
    
    // Deserialize base class data - EDA_BOARD_CONNECTED_OBJECT doesn't have deserialize method
    // if (!EDA_BOARD_CONNECTED_OBJECT::deserialize(container)) {
    //     return false;
    // }
    
    // Deserialize shape data
    if (container.contains("shape")) {
        QVariantMap shapeData = container["shape"].toMap();
        if (!EDA_SHAPE_DATA::deserialize(shapeData)) {
            return false;
        }
    }
    
    // Deserialize PCB_SHAPE specific data
    if (container.contains("layer")) {
        m_layer = static_cast<QtPcbLayerId>(container["layer"].toInt());
    }
    
    m_hasSolderMask = container.value("hasSolderMask", false).toBool();
    
    if (container.contains("solderMaskMargin")) {
        m_solderMaskMargin = container["solderMaskMargin"].toInt();
    } else {
        m_solderMaskMargin.reset();
    }
    
    invalidateCache();
    syncProperties();
    
    return true;
}

//=============================================================================
// CONNECTION AND TYPE CHECKING
//=============================================================================

bool EDA_BRD_SHAPE_DATA::isConnected() const
{
    // Check if on a copper layer and in a net
    return isOnCopperLayer() && getNetCode() > 0;
}

QString EDA_BRD_SHAPE_DATA::getFriendlyName() const
{
    return EDA_SHAPE_DATA::getFriendlyName();
}

bool EDA_BRD_SHAPE_DATA::isType(const QVector<QtKicadType>& scanTypes) const
{
    if (EDA_BOARD_OBJECT_DATA::isType(scanTypes)) {
        return true;
    }
    
    for (QtKicadType scanType : scanTypes) {
        if (scanType == QtKicadType::PcbLocateBoardEdge) {
            if (m_layer == QtPcbLayerId::EdgeCuts) {
                return true;
            }
        } else if (scanType == QtKicadType::PcbShapeLocateArc) {
            if (getShapeType() == QtShapeType::Arc) {
                return true;
            }
        } else if (scanType == QtKicadType::PcbShapeLocateCircle) {
            if (getShapeType() == QtShapeType::Circle) {
                return true;
            }
        } else if (scanType == QtKicadType::PcbShapeLocateRect) {
            if (getShapeType() == QtShapeType::Rectangle) {
                return true;
            }
        } else if (scanType == QtKicadType::PcbShapeLocateSegment) {
            if (getShapeType() == QtShapeType::Segment) {
                return true;
            }
        } else if (scanType == QtKicadType::PcbShapeLocatePoly) {
            if (getShapeType() == QtShapeType::Polygon) {
                return true;
            }
        } else if (scanType == QtKicadType::PcbShapeLocateBezier) {
            if (getShapeType() == QtShapeType::Bezier) {
                return true;
            }
        }
    }
    
    return false;
}

//=============================================================================
// LAYER MANAGEMENT
//=============================================================================

void EDA_BRD_SHAPE_DATA::setLayer(QtPcbLayerId layer)
{
    if (m_layer != layer) {
        m_layer = layer;
        invalidateCache();
        // Layer and connection changed notifications removed (Qt object system removal)
    }
}

bool EDA_BRD_SHAPE_DATA::isOnLayer(QtPcbLayerId layer) const
{
    if (m_layer == layer) {
        return true;
    }
    
    // Special handling for solder mask layers
    if (m_hasSolderMask) {
        if ((layer == QtPcbLayerId::FMask && m_layer == QtPcbLayerId::FCu) ||
            (layer == QtPcbLayerId::BMask && m_layer == QtPcbLayerId::BCu)) {
            return true;
        }
    }
    
    return false;
}

QtLayerSet EDA_BRD_SHAPE_DATA::getLayerSet() const
{
    QtLayerSet layers(m_layer);
    
    // Add solder mask layers if applicable
    if (m_hasSolderMask) {
        if (m_layer == QtPcbLayerId::FCu) {
            layers.set(QtPcbLayerId::FMask);
        } else if (m_layer == QtPcbLayerId::BCu) {
            layers.set(QtPcbLayerId::BMask);
        }
    }
    
    return layers;
}

void EDA_BRD_SHAPE_DATA::setLayerSet(const QtLayerSet& layers)
{
    // Find first valid layer in the set
    for (int layer = 0; layer < static_cast<int>(QtPcbLayerId::LayerCount); ++layer) {
        if (layers.test(static_cast<QtPcbLayerId>(layer))) {
            setLayer(static_cast<QtPcbLayerId>(layer));
            break;
        }
    }
}

//=============================================================================
// POSITION AND GEOMETRY
//=============================================================================

void EDA_BRD_SHAPE_DATA::setPosition(const QPointF& pos)
{
    EDA_SHAPE_DATA::setPosition(pos);
    invalidateCache();
}

QPointF EDA_BRD_SHAPE_DATA::getPosition() const
{
    return EDA_SHAPE_DATA::getPosition();
}

QPointF EDA_BRD_SHAPE_DATA::getCenter() const
{
    return EDA_SHAPE_DATA::getCenter();
}

QVector<QPointF> EDA_BRD_SHAPE_DATA::getConnectionPoints() const
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_connectionPointsValid) {
        updateCachedConnectionPoints();
    }
    
    return m_cachedConnectionPoints;
}

void EDA_BRD_SHAPE_DATA::updateCachedConnectionPoints() const
{
    m_cachedConnectionPoints.clear();
    
    switch (getShapeType()) {
    case QtShapeType::Segment:
    case QtShapeType::Arc:
    case QtShapeType::Bezier:
        // Start and end points are natural connection points
        m_cachedConnectionPoints.append(getStartPoint());
        m_cachedConnectionPoints.append(getEndPoint());
        break;
        
    case QtShapeType::Rectangle:
        // Four corners are connection points
        m_cachedConnectionPoints = getCorners();
        break;
        
    case QtShapeType::Polygon:
        // All polygon vertices are potential connection points
        for (const QPointF& point : getPolygon()) {
            m_cachedConnectionPoints.append(point);
        }
        break;
        
    case QtShapeType::Circle:
        // Circles don't have natural connection points
        break;
        
    default:
        break;
    }
    
    m_connectionPointsValid = true;
}

QPointF EDA_BRD_SHAPE_DATA::getFocusPosition() const
{
    // For filled shapes, center is appropriate
    if (isFilled()) {
        return getCenter();
    }
    
    // For unfilled shapes, use a point on the outline
    switch (getShapeType()) {
    case QtShapeType::Segment:
        return getStartPoint();
        
    case QtShapeType::Rectangle:
        return getTopLeft();
        
    case QtShapeType::Arc:
        return getArcMid();
        
    case QtShapeType::Circle:
        // Point on circle at 0 degrees
        return getStartPoint() + QPointF(getRadius(), 0);
        
    case QtShapeType::Polygon:
        if (!getPolygon().isEmpty()) {
            return getPolygon().first();
        }
        break;
        
    case QtShapeType::Bezier:
        return getStartPoint();
        
    default:
        break;
    }
    
    return getCenter();
}

QVector<QPointF> EDA_BRD_SHAPE_DATA::getCorners() const
{
    QVector<QPointF> corners;
    
    if (getShapeType() == QtShapeType::Rectangle) {
        QRectF rect(getStartPoint(), getEndPoint());
        rect = rect.normalized();
        
        corners.append(rect.topLeft());
        corners.append(rect.topRight());
        corners.append(rect.bottomRight());
        corners.append(rect.bottomLeft());
    } else if (getShapeType() == QtShapeType::Polygon) {
        // For rotated rectangles stored as polygons
        if (getPolygon().size() == 4) {
            for (const QPointF& point : getPolygon()) {
                corners.append(point);
            }
        }
    }
    
    return corners;
}

//=============================================================================
// STROKE AND APPEARANCE
//=============================================================================

QtStrokeParams EDA_BRD_SHAPE_DATA::getStroke() const
{
    return EDA_SHAPE_DATA::getStroke();
}

void EDA_BRD_SHAPE_DATA::setStroke(const QtStrokeParams& stroke)
{
    EDA_SHAPE_DATA::setStroke(stroke);
    invalidateCache();
}

int EDA_BRD_SHAPE_DATA::getWidth() const
{
    // Board edge on Edge_Cuts layer has special handling
    if (m_layer == QtPcbLayerId::EdgeCuts) {
        return 0;
    }
    
    return EDA_SHAPE_DATA::getLineWidth();
}

void EDA_BRD_SHAPE_DATA::styleFromSettings(const QtBoardDesignSettings& settings)
{
    // Apply default style from board settings
    setLineWidth(settings.getLineThickness(static_cast<int>(m_layer)));
}

//=============================================================================
// SOLDER MASK MANAGEMENT
//=============================================================================

void EDA_BRD_SHAPE_DATA::setLocalSolderMaskMargin(std::optional<int> margin)
{
    if (m_solderMaskMargin != margin) {
        m_solderMaskMargin = margin;
        // Solder mask changed notification removed (Qt object system removal)
    }
}

int EDA_BRD_SHAPE_DATA::getSolderMaskExpansion() const
{
    // Local margin takes precedence
    if (m_solderMaskMargin.has_value()) {
        return m_solderMaskMargin.value();
    }
    
    // Otherwise use board settings
    if (const EDA_BOARD_DATA* board = getBoard()) {
        // return board->getDesignSettings().getSolderMaskExpansion(); // Method may not exist
        return 0; // Default value for now
    }
    
    return 0;
}

//=============================================================================
// PROXY ITEM MANAGEMENT
//=============================================================================

bool EDA_BRD_SHAPE_DATA::isProxyItem() const
{
    return EDA_SHAPE_DATA::isProxyItem();
}

void EDA_BRD_SHAPE_DATA::setIsProxyItem(bool isProxy)
{
    EDA_SHAPE_DATA::setIsProxyItem(isProxy);
}

//=============================================================================
// INFORMATION AND UI
//=============================================================================

QString EDA_BRD_SHAPE_DATA::getItemDescription(QtUnitsProvider* unitsProvider, bool full) const
{
    if (!full) {
        return getFriendlyName();
    }
    
    QString layerName = getLayerName();
    QString netName;
    
    if (isOnCopperLayer() && getNetCode() > 0) {
        netName = QString(" [%1]").arg(getDisplayNetName());
    }
    
    return QString("%1 on %2%3").arg(getFriendlyName(), layerName, netName);
}

QtBitmaps EDA_BRD_SHAPE_DATA::getMenuImage() const
{
    return static_cast<QtBitmaps>(0); // AddShape
}

//=============================================================================
// GEOMETRIC OPERATIONS
//=============================================================================

QRectF EDA_BRD_SHAPE_DATA::getBoundingBox() const
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_bboxValid) {
        updateCachedBBox();
    }
    
    return m_cachedBBox;
}

void EDA_BRD_SHAPE_DATA::updateCachedBBox() const
{
    m_cachedBBox = EDA_SHAPE_DATA::getBoundingRect();
    
    // Expand by line width
    int width = getWidth();
    if (width > 0) {
        double halfWidth = width / 2.0;
        m_cachedBBox.adjust(-halfWidth, -halfWidth, halfWidth, halfWidth);
    }
    
    m_bboxValid = true;
}

bool EDA_BRD_SHAPE_DATA::hitTest(const QPointF& position, double accuracy) const
{
    // Simple implementation - check if position is within bounding box
    QRectF bbox = getBoundingBox();
    if (accuracy > 0) {
        bbox.adjust(-accuracy, -accuracy, accuracy, accuracy);
    }
    return bbox.contains(position);
}

bool EDA_BRD_SHAPE_DATA::hitTest(const QRectF& rect, bool contained, double accuracy) const
{
    QRectF bbox = getBoundingBox();
    if (accuracy > 0) {
        bbox.adjust(-accuracy, -accuracy, accuracy, accuracy);
    }
    
    if (contained) {
        return rect.contains(bbox);
    } else {
        return rect.intersects(bbox);
    }
}

void EDA_BRD_SHAPE_DATA::normalize()
{
    // EDA_SHAPE_DATA::normalize(); // Method doesn't exist, skip for now
    invalidateCache();
}

void EDA_BRD_SHAPE_DATA::normalizeForCompare()
{
    normalize();
    
    // Additional normalization for segments
    if (getShapeType() == QtShapeType::Segment) {
        // Sort endpoints by X, then Y
        QPointF start = getStartPoint();
        QPointF end = getEndPoint();
        
        if (start.x() > end.x() || (start.x() == end.x() && start.y() > end.y())) {
            setStartPoint(end);
            setEndPoint(start);
        }
    }
}

//=============================================================================
// TRANSFORMATIONS
//=============================================================================

void EDA_BRD_SHAPE_DATA::move(const QPointF& moveVector)
{
    EDA_SHAPE_DATA::move(moveVector);
    invalidateCache();
}

void EDA_BRD_SHAPE_DATA::rotate(const QPointF& rotCenter, double angleDegrees)
{
    EDA_SHAPE_DATA::rotate(rotCenter, angleDegrees);
    invalidateCache();
}

void EDA_BRD_SHAPE_DATA::flip(const QPointF& center, QtFlipDirection direction)
{
    EDA_SHAPE_DATA::flip(center, direction == static_cast<QtFlipDirection>(1)); // Horizontal
    
    // Flip layer - simplified implementation 
    // setLayer(::flipLayer(m_layer, getBoard()->getCopperLayerCount())); // flipLayer function not implemented
    if (m_layer == QtPcbLayerId::FCu) {
        setLayer(QtPcbLayerId::BCu);
    } else if (m_layer == QtPcbLayerId::BCu) {
        setLayer(QtPcbLayerId::FCu);
    }
}

void EDA_BRD_SHAPE_DATA::mirror(const QPointF& center, QtFlipDirection direction)
{
    flip(center, direction);
}

void EDA_BRD_SHAPE_DATA::scale(double scaleFactor)
{
    EDA_SHAPE_DATA::scale(scaleFactor);
    invalidateCache();
}

//=============================================================================
// SHAPE OPERATIONS
//=============================================================================

std::shared_ptr<QtShape> EDA_BRD_SHAPE_DATA::getEffectiveShape(QtPcbLayerId layer, QtFlashing flash) const
{
    // Temporary implementation - return a basic segment shape
    // TODO: Implement proper shape creation based on actual shape type
    auto segment = std::make_shared<QtShapeSegment>(QPointF(0, 0), QPointF(100, 100), 100);
    return std::static_pointer_cast<QtShape>(segment);
}

void EDA_BRD_SHAPE_DATA::transformShapeToPolygon(QtShapePolySet& buffer, QtPcbLayerId layer,
                                        int clearance, int maxError, QtErrorLoc errorLoc,
                                        bool ignoreLineWidth) const
{
    // Calculate actual clearance including line width
    int actualClearance = clearance;
    if (!ignoreLineWidth) {
        actualClearance += getWidth() / 2;
    }
    
    // Convert to polygon using QtEdaShape functionality
    QPolygonF polygon;
    EDA_SHAPE_DATA::transformShapeToPolygon(polygon, actualClearance, maxError, ignoreLineWidth);
    
    // Add to output buffer
    // buffer.addPolygon(polygon); // Method not implemented in QtShapePolySet
    Q_UNUSED(buffer); // Suppress warning
}

//=============================================================================
// CLONING AND COMPARISON
//=============================================================================

EDA_OBJECT_DATA* EDA_BRD_SHAPE_DATA::clone() const
{
    // Create new instance and copy properties manually to avoid copy constructor issues
    EDA_BRD_SHAPE_DATA* newShape = new EDA_BRD_SHAPE_DATA(nullptr, getType(), getShapeType());
    
    // Copy layer and properties
    newShape->setLayer(m_layer);
    newShape->m_hasSolderMask = m_hasSolderMask;
    newShape->m_solderMaskMargin = m_solderMaskMargin;
    
    // Copy base class properties would go here if accessible
    // For now, return the basic clone
    return newShape;
}

double EDA_BRD_SHAPE_DATA::similarity(const EDA_BOARD_OBJECT_DATA& other) const
{
    if (other.getType() != getType()) {
        return 0.0;
    }
    
    const EDA_BRD_SHAPE_DATA* otherShape = dynamic_cast<const EDA_BRD_SHAPE_DATA*>(&other);
    if (!otherShape) {
        return 0.0;
    }
    
    double similarity = 1.0;
    
    // Check layer
    if (m_layer != otherShape->m_layer) {
        similarity *= 0.9;
    }
    
    // Check shape similarity
    similarity *= EDA_SHAPE_DATA::similarity(*otherShape);
    
    // Check net
    if (getNetCode() != otherShape->getNetCode()) {
        similarity *= 0.9;
    }
    
    // Check solder mask
    if (m_hasSolderMask != otherShape->m_hasSolderMask) {
        similarity *= 0.95;
    }
    
    return similarity;
}

bool EDA_BRD_SHAPE_DATA::operator==(const EDA_BRD_SHAPE_DATA& other) const
{
    return m_layer == other.m_layer &&
           m_hasSolderMask == other.m_hasSolderMask &&
           m_solderMaskMargin == other.m_solderMaskMargin &&
           EDA_BOARD_CONNECTED_OBJECT::operator==(other) &&
           EDA_SHAPE_DATA::operator==(other);
}

bool EDA_BRD_SHAPE_DATA::operator==(const EDA_BOARD_OBJECT_DATA& other) const
{
    if (other.getType() != getType()) {
        return false;
    }
    
    const EDA_BRD_SHAPE_DATA* otherShape = dynamic_cast<const EDA_BRD_SHAPE_DATA*>(&other);
    return otherShape && (*this == *otherShape);
}

//=============================================================================
// PROTECTED METHODS
//=============================================================================

void EDA_BRD_SHAPE_DATA::swapData(EDA_BOARD_OBJECT_DATA* image)
{
    EDA_BRD_SHAPE_DATA* shape = dynamic_cast<EDA_BRD_SHAPE_DATA*>(image);
    if (!shape) {
        return;
    }
    
    // Swap base class data
    EDA_BOARD_CONNECTED_OBJECT::swapData(image);
    EDA_SHAPE_DATA::swapShape(*shape);
    
    // Swap PCB_SHAPE specific data
    std::swap(m_layer, shape->m_layer);
    std::swap(m_hasSolderMask, shape->m_hasSolderMask);
    std::swap(m_solderMaskMargin, shape->m_solderMaskMargin);
    
    invalidateCache();
    shape->invalidateCache();
}

bool EDA_BRD_SHAPE_DATA::CmpDrawings::operator()(const EDA_BOARD_OBJECT_DATA* first, 
                                       const EDA_BOARD_OBJECT_DATA* second) const
{
    if (first->getType() != second->getType()) {
        return first->getType() < second->getType();
    }
    
    if (first->getLayer() != second->getLayer()) {
        return first->getLayer() < second->getLayer();
    }
    
    const EDA_BRD_SHAPE_DATA* shape1 = dynamic_cast<const EDA_BRD_SHAPE_DATA*>(first);
    const EDA_BRD_SHAPE_DATA* shape2 = dynamic_cast<const EDA_BRD_SHAPE_DATA*>(second);
    
    if (!shape1 || !shape2) {
        return false;
    }
    
    return shape1->compare(*shape2) < 0;
}

//=============================================================================
// PRIVATE HELPER METHODS
//=============================================================================

void EDA_BRD_SHAPE_DATA::invalidateCache()
{
    m_bboxValid = false;
    m_connectionPointsValid = false;
}

void EDA_BRD_SHAPE_DATA::syncProperties()
{
    // Ensure properties from both parent classes are synchronized
    // This is important for multiple inheritance scenarios
    
    // Signal connections removed (Qt object system removal)
    // Shape changes now directly call invalidateCache() when needed
}

#if defined(DEBUG)
void EDA_BRD_SHAPE_DATA::show(int nestLevel, std::ostream& os) const
{
    QString indent = QString(" ").repeated(nestLevel);
    os << indent.toStdString() << "EDA_BRD_SHAPE_DATA: " << getItemDescription(nullptr, true).toStdString() << std::endl;
}
#endif