/*
 * This program source code file is part of KiCad, a free EDA CAD application.
 *
 * Copyright (C) 2015 <PERSON><PERSON><PERSON>, jp.charras at wanadoo.fr
 * Copyright (C) 2012 SoftPLC Corporation, <PERSON> <<EMAIL>>
 * Copyright The KiCad Developers, see Authors.txt for contributors.
 * Copyright (C) 2024 KiCad to Qt Migration Project
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */

#include "eda_board_connected_object.h"
#include "eda_net_data.h"
#include "eda_board_data.h"
#include "eda_netinfo_list.h"
#include "qt_temporary_implementations.h"  // For QtNetclass definition
#include <QtCore/QVariantMap>
#include <QtCore/QJsonDocument>
#include <QtCore/QJsonObject>
#include <QtCore/QJsonArray>
#include <QtCore/QStringBuilder>
#include <QtCore/QCoreApplication>
#include <algorithm>

Q_LOGGING_CATEGORY(eDA_BOARD_CONNECTED_OBJECT, "qt.pcbnew.board.connected_item")

// QtTeardropParameters implementation
QVariantMap QtTeardropParameters::toVariantMap() const {
    QVariantMap map;
    map[QStringLiteral("maxLength")] = maxLength;
    map[QStringLiteral("maxWidth")] = maxWidth;
    map[QStringLiteral("bestLengthRatio")] = bestLengthRatio;
    map[QStringLiteral("bestWidthRatio")] = bestWidthRatio;
    map[QStringLiteral("widthToSizeFilterRatio")] = widthToSizeFilterRatio;
    map[QStringLiteral("curvedEdges")] = curvedEdges;
    map[QStringLiteral("enabled")] = enabled;
    map[QStringLiteral("allowUseTwoTracks")] = allowUseTwoTracks;
    map[QStringLiteral("onPadsInZones")] = onPadsInZones;
    return map;
}

void QtTeardropParameters::fromVariantMap(const QVariantMap& map) {
    maxLength = map.value(QStringLiteral("maxLength"), maxLength).toInt();
    maxWidth = map.value(QStringLiteral("maxWidth"), maxWidth).toInt();
    bestLengthRatio = map.value(QStringLiteral("bestLengthRatio"), bestLengthRatio).toDouble();
    bestWidthRatio = map.value(QStringLiteral("bestWidthRatio"), bestWidthRatio).toDouble();
    widthToSizeFilterRatio = map.value(QStringLiteral("widthToSizeFilterRatio"), widthToSizeFilterRatio).toDouble();
    curvedEdges = map.value(QStringLiteral("curvedEdges"), curvedEdges).toBool();
    enabled = map.value(QStringLiteral("enabled"), enabled).toBool();
    allowUseTwoTracks = map.value(QStringLiteral("allowUseTwoTracks"), allowUseTwoTracks).toBool();
    onPadsInZones = map.value(QStringLiteral("onPadsInZones"), onPadsInZones).toBool();
}

// EDA_BOARD_CONNECTED_OBJECT implementation
EDA_BOARD_CONNECTED_OBJECT::EDA_BOARD_CONNECTED_OBJECT(EDA_BOARD_OBJECT_DATA* parent, QtKicadType itemType)
    : EDA_BOARD_OBJECT_DATA(dynamic_cast<EDA_BOARD_OBJECT_CONTAINER*>(parent), itemType)
    , m_netInfo(EDA_NETINFO_LIST::orphanedItem())
    , m_localRatsnestVisible(true)
    , m_clearanceCacheValid(false)
    , m_netInfoCacheValid(false)
{
    
    // Initialize teardrop parameters with defaults
    m_teardropParams = QtTeardropParameters();
    
    // Cache invalidation will be handled directly in setter methods
}

EDA_BOARD_CONNECTED_OBJECT::~EDA_BOARD_CONNECTED_OBJECT() {
    disconnectNetInfoSignals();
}

bool EDA_BOARD_CONNECTED_OBJECT::classOf(const EDA_OBJECT_DATA* item) {
    if (!item) return false;
    
    switch (item->getType()) {
    case QtKicadType::PcbPad:
    case QtKicadType::PcbTrace:
    case QtKicadType::PcbArc:
    case QtKicadType::PcbVia:
    case QtKicadType::PcbZone:
    case QtKicadType::PcbShape:
        return true;
    default:
        return false;
    }
}

//=============================================================================
// CONSTRUCTION AND DESTRUCTION
//=============================================================================

EDA_BOARD_CONNECTED_OBJECT::EDA_BOARD_CONNECTED_OBJECT(const EDA_BOARD_CONNECTED_OBJECT& other)
    : EDA_BOARD_OBJECT_DATA(other)
    , m_netInfo(other.m_netInfo)
    , m_teardropParams(other.m_teardropParams)
{
    // Copy constructor implementation
}

void EDA_BOARD_CONNECTED_OBJECT::setNet(EDA_NET_DATA* netInfo) {
    if (m_netInfo == netInfo) return;
    
    disconnectNetInfoSignals();
    
    EDA_NET_DATA* oldNet = m_netInfo;
    m_netInfo = netInfo ? netInfo : EDA_NETINFO_LIST::orphanedItem();
    
    connectNetInfoSignals();
    invalidateNetInfoCache();
    invalidateClearanceCache();
    
    

}

int EDA_BOARD_CONNECTED_OBJECT::getNetCode() const {
    return m_netInfo ? m_netInfo->getNetCode() : -1;
}

bool EDA_BOARD_CONNECTED_OBJECT::setNetCode(int netCode, bool noAssert) {
    if (!isOnCopperLayer()) {
        netCode = 0;
    }
    
    const EDA_BOARD_DATA* board = dynamic_cast<const EDA_BOARD_DATA*>(getBoard());
    EDA_NET_DATA* newNet = nullptr;
    
    if (netCode >= 0 && board) {
        newNet = board->findNet(netCode);
    } else {
        newNet = EDA_NETINFO_LIST::orphanedItem();
    }
    
    if (!noAssert && !newNet) {
        qCWarning(eDA_BOARD_CONNECTED_OBJECT) << "Net with code" << netCode << "not found";
        return false;
    }
    
    setNet(newNet);
    return newNet != nullptr;
}

QString EDA_BOARD_CONNECTED_OBJECT::getNetName() const {
    if (!m_netInfoCacheValid) {
        updateNetInfoCache();
    }
    return m_cachedNetName;
}

QString EDA_BOARD_CONNECTED_OBJECT::getNetNameMessage() const {
    if (!getBoard()) {
        return QStringLiteral("[** NO BOARD DEFINED **]");
    }
    
    QString netname = getNetName();
    
    if (netname.isEmpty()) {
        return QStringLiteral("[<no net>]");
    } else if (getNetCode() < 0) {
        return QStringLiteral("[%1](%2)").arg(netname, QCoreApplication::translate("EDA_BOARD_CONNECTED_OBJECT", "Not Found"));
    } else {
        return QStringLiteral("[%1]").arg(netname);
    }
}

const QString& EDA_BOARD_CONNECTED_OBJECT::getShortNetName() const {
    if (!m_netInfoCacheValid) {
        updateNetInfoCache();
    }
    return m_cachedShortNetName;
}

const QString& EDA_BOARD_CONNECTED_OBJECT::getDisplayNetName() const {
    if (!m_netInfoCacheValid) {
        updateNetInfoCache();
    }
    return m_cachedDisplayNetName;
}

QtNetclass* EDA_BOARD_CONNECTED_OBJECT::getEffectiveNetClass() const {
    if (m_netInfo && m_netInfo->getNetClass()) {
        return m_netInfo->getNetClass();
    }
    
    const EDA_BOARD_DATA* board = dynamic_cast<const EDA_BOARD_DATA*>(getBoard());
    if (board) {
        // Return default netclass - implementation would access default from design settings
        static QtNetclass defaultNetClass;
        return &defaultNetClass;
    }
    
    static QtNetclass fallbackNetClass;
    return &fallbackNetClass;
}

QString EDA_BOARD_CONNECTED_OBJECT::getNetClassName() const {
    if (!m_netInfoCacheValid) {
        updateNetInfoCache();
    }
    return m_cachedNetClassName;
}

int EDA_BOARD_CONNECTED_OBJECT::getOwnClearance(QtPcbLayerId layer, QString* source) const {
    // Check cache first
    if (m_clearanceCacheValid && m_clearanceCache.contains(layer)) {
        if (source) {
            *source = QStringLiteral("Cached");
        }
        return m_clearanceCache.value(layer);
    }
    
    int clearance = 0;
    QString constraintSource;
    
    const EDA_BOARD_DATA* board = dynamic_cast<const EDA_BOARD_DATA*>(getBoard());
    if (board) {
        // This would normally query the DRC engine
        // For now, return a default clearance
        clearance = 200000; // 0.2mm default
        constraintSource = QStringLiteral("Default");
    }
    
    // Cache the result
    if (m_clearanceCacheValid) {
        m_clearanceCache.insert(layer, clearance);
    }
    
    if (source) {
        *source = constraintSource;
    }
    
    return clearance;
}

std::optional<int> EDA_BOARD_CONNECTED_OBJECT::getClearanceOverrides(QString* source) const {
    Q_UNUSED(source)
    // Base implementation returns no overrides
    return std::nullopt;
}

std::optional<int> EDA_BOARD_CONNECTED_OBJECT::getLocalClearance(QString* source) const {
    Q_UNUSED(source)
    // Base implementation returns no local clearance
    return std::nullopt;
}

void EDA_BOARD_CONNECTED_OBJECT::setLocalRatsnestVisible(bool visible) {
    if (m_localRatsnestVisible == visible) return;
    
    m_localRatsnestVisible = visible;
    
}

// Teardrop property setters
void EDA_BOARD_CONNECTED_OBJECT::setTeardropsEnabled(bool enabled) {
    if (m_teardropParams.enabled == enabled) return;
    
    m_teardropParams.enabled = enabled;
    
}

void EDA_BOARD_CONNECTED_OBJECT::setTeardropBestLengthRatio(double ratio) {
    if (qFuzzyCompare(m_teardropParams.bestLengthRatio, ratio)) return;
    
    m_teardropParams.bestLengthRatio = qBound(0.0, ratio, 10.0);
}

void EDA_BOARD_CONNECTED_OBJECT::setTeardropMaxLength(int maxLength) {
    if (m_teardropParams.maxLength == maxLength) return;
    
    m_teardropParams.maxLength = qMax(0, maxLength);
}

void EDA_BOARD_CONNECTED_OBJECT::setTeardropBestWidthRatio(double ratio) {
    if (qFuzzyCompare(m_teardropParams.bestWidthRatio, ratio)) return;
    
    m_teardropParams.bestWidthRatio = qBound(0.0, ratio, 10.0);
}

void EDA_BOARD_CONNECTED_OBJECT::setTeardropMaxWidth(int maxWidth) {
    if (m_teardropParams.maxWidth == maxWidth) return;
    
    m_teardropParams.maxWidth = qMax(0, maxWidth);
}

void EDA_BOARD_CONNECTED_OBJECT::setTeardropCurved(bool curved) {
    if (m_teardropParams.curvedEdges == curved) return;
    
    m_teardropParams.curvedEdges = curved;
}

void EDA_BOARD_CONNECTED_OBJECT::setTeardropPreferZoneConnections(bool prefer) {
    bool newOnPadsInZones = !prefer;
    if (m_teardropParams.onPadsInZones == newOnPadsInZones) return;
    
    m_teardropParams.onPadsInZones = newOnPadsInZones;
}

void EDA_BOARD_CONNECTED_OBJECT::setTeardropAllowSpanTwoTracks(bool allow) {
    if (m_teardropParams.allowUseTwoTracks == allow) return;
    
    m_teardropParams.allowUseTwoTracks = allow;
}

void EDA_BOARD_CONNECTED_OBJECT::setTeardropMaxTrackWidth(double ratio) {
    if (qFuzzyCompare(m_teardropParams.widthToSizeFilterRatio, ratio)) return;
    
    m_teardropParams.widthToSizeFilterRatio = qBound(0.0, ratio, 1.0);
}

// Qt serialization
QVariantMap EDA_BOARD_CONNECTED_OBJECT::toVariantMap() const {
    QVariantMap map = EDA_BOARD_OBJECT_DATA::toVariantMap();
    
    // Network information
    map[QStringLiteral("netCode")] = getNetCode();
    map[QStringLiteral("netName")] = getNetName();
    map[QStringLiteral("localRatsnestVisible")] = m_localRatsnestVisible;
    
    // Teardrop parameters
    map[QStringLiteral("teardropParams")] = m_teardropParams.toVariantMap();
    
    return map;
}

void EDA_BOARD_CONNECTED_OBJECT::fromVariantMap(const QVariantMap& map) {
    EDA_BOARD_OBJECT_DATA::fromVariantMap(map);
    
    // Network information
    if (map.contains(QStringLiteral("netCode"))) {
        setNetCode(map.value(QStringLiteral("netCode")).toInt());
    }
    
    if (map.contains(QStringLiteral("localRatsnestVisible"))) {
        setLocalRatsnestVisible(map.value(QStringLiteral("localRatsnestVisible")).toBool());
    }
    
    // Teardrop parameters
    if (map.contains(QStringLiteral("teardropParams"))) {
        QVariantMap teardropMap = map.value(QStringLiteral("teardropParams")).toMap();
        m_teardropParams.fromVariantMap(teardropMap);
    }
}

QString EDA_BOARD_CONNECTED_OBJECT::toString() const {
    return QStringLiteral("EDA_BOARD_CONNECTED_OBJECT(type=%1, net=%2)")
           .arg(static_cast<int>(getType()))
           .arg(getNetName());
}

// Public slots
void EDA_BOARD_CONNECTED_OBJECT::updateNetworkInfo() {
    invalidateNetInfoCache();
    invalidateClearanceCache();
    
    // Network information updated (no signals in simplified version)
}

void EDA_BOARD_CONNECTED_OBJECT::invalidateNetworkCache() {
    invalidateNetInfoCache();
    invalidateClearanceCache();
}

void EDA_BOARD_CONNECTED_OBJECT::resetTeardropParameters() {
    m_teardropParams = QtTeardropParameters();
    
}

void EDA_BOARD_CONNECTED_OBJECT::applyTeardropTemplate(const QtTeardropParameters& params) {
    if (m_teardropParams != params) {
        m_teardropParams = params;
        
    }
}

// Protected helper methods
void EDA_BOARD_CONNECTED_OBJECT::invalidateClearanceCache() {
    m_clearanceCache.clear();
    m_clearanceCacheValid = false;
    
}

void EDA_BOARD_CONNECTED_OBJECT::invalidateNetInfoCache() {
    m_netInfoCacheValid = false;
    
}

void EDA_BOARD_CONNECTED_OBJECT::updateNetInfoCache() const {
    if (m_netInfo) {
        m_cachedNetName = m_netInfo->getNetName();
        m_cachedShortNetName = m_netInfo->getShortNetName();
        m_cachedDisplayNetName = m_netInfo->getDisplayNetName();
        
        QtNetclass* netClass = getEffectiveNetClass();
        m_cachedNetClassName = netClass ? netClass->getName() : QString();
    } else {
        m_cachedNetName.clear();
        m_cachedShortNetName.clear();
        m_cachedDisplayNetName.clear();
        m_cachedNetClassName.clear();
    }
    
    m_netInfoCacheValid = true;
}

void EDA_BOARD_CONNECTED_OBJECT::connectNetInfoSignals() {
    // Simplified: no signal connections needed
}

void EDA_BOARD_CONNECTED_OBJECT::disconnectNetInfoSignals() {
    // Simplified: no signal disconnections needed
}

// Utility functions implementation
namespace QtTeardropUtils {

QString getTargetCanonicalName(QtTeardropTarget target) {
    switch (target) {
    case QtTeardropTarget::Round:  return QStringLiteral("round");
    case QtTeardropTarget::Rect:   return QStringLiteral("rect");
    case QtTeardropTarget::Track:  return QStringLiteral("track");
    default:                       return QStringLiteral("unknown");
    }
}

QtTeardropTarget getTargetFromCanonicalName(const QString& name) {
    QString lowerName = name.toLower();
    
    if (lowerName == QStringLiteral("round")) {
        return QtTeardropTarget::Round;
    } else if (lowerName == QStringLiteral("rect")) {
        return QtTeardropTarget::Rect;
    } else if (lowerName == QStringLiteral("track")) {
        return QtTeardropTarget::Track;
    } else {
        return QtTeardropTarget::Unknown;
    }
}

QStringList getAllTargetNames() {
    return QStringList() 
           << getTargetCanonicalName(QtTeardropTarget::Round)
           << getTargetCanonicalName(QtTeardropTarget::Rect)
           << getTargetCanonicalName(QtTeardropTarget::Track);
}

bool validateParameters(const QtTeardropParameters& params, QString* error) {
    if (params.bestLengthRatio < 0.0 || params.bestLengthRatio > 10.0) {
        if (error) *error = QStringLiteral("Best length ratio must be between 0.0 and 10.0");
        return false;
    }
    
    if (params.bestWidthRatio < 0.0 || params.bestWidthRatio > 10.0) {
        if (error) *error = QStringLiteral("Best width ratio must be between 0.0 and 10.0");
        return false;
    }
    
    if (params.widthToSizeFilterRatio < 0.0 || params.widthToSizeFilterRatio > 1.0) {
        if (error) *error = QStringLiteral("Width to size filter ratio must be between 0.0 and 1.0");
        return false;
    }
    
    if (params.maxLength < 0 || params.maxWidth < 0) {
        if (error) *error = QStringLiteral("Maximum dimensions cannot be negative");
        return false;
    }
    
    return true;
}

QtTeardropParameters getDefaultParameters(QtTeardropTarget target) {
    QtTeardropParameters params;
    
    switch (target) {
    case QtTeardropTarget::Round:
        params.bestLengthRatio = 0.5;
        params.bestWidthRatio = 1.0;
        params.curvedEdges = true;
        break;
        
    case QtTeardropTarget::Rect:
        params.bestLengthRatio = 0.4;
        params.bestWidthRatio = 0.8;
        params.curvedEdges = false;
        break;
        
    case QtTeardropTarget::Track:
        params.bestLengthRatio = 0.3;
        params.bestWidthRatio = 0.6;
        params.curvedEdges = false;
        params.allowUseTwoTracks = true;
        break;
        
    default:
        break;
    }
    
    return params;
}

} // namespace QtTeardropUtils