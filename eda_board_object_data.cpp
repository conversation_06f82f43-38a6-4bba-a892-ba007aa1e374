/*
 * Qt-based reimplementation of KiCad BOARD_ITEM class implementation
 */

#include "eda_board_object_data.h"
#include "eda_board_object_container.h"
#include "eda_board_data.h"
#include "eda_footprint_data.h"

#include <QtCore/QDebug>
#include <QtCore/QLoggingCategory>
#include <QtCore/QMetaEnum>
#include <QtGui/QTransform>
#include <QtCore/QJsonObject>
#include <QtCore/QJsonArray>
#include <cmath>

Q_LOGGING_CATEGORY(qtBoardItemLog, "qt.pcbnew.boarditem")

// Anonymous namespace for internal utilities
namespace {
    constexpr double DEGREES_TO_RADIANS = M_PI / 180.0;
    constexpr double RADIANS_TO_DEGREES = 180.0 / M_PI;
    constexpr double EPSILON = 1e-9;
    
    // Layer name lookup table for performance
    const QHash<QtPcbLayerId, QString> LAYER_NAMES = {
        {QtPcbLayerId::FCu, "F.Cu"},
        {QtPcbLayerId::BCu, "B.Cu"},
        {QtPcbLayerId::In1Cu, "In1.Cu"},
        {QtPcbLayerId::In2Cu, "In2.Cu"},
        {QtPcbLayerId::In3Cu, "In3.Cu"},
        {QtPcbLayerId::In4Cu, "In4.Cu"},
        {QtPcbLayerId::In5Cu, "In5.Cu"},
        {QtPcbLayerId::In6Cu, "In6.Cu"},
        {QtPcbLayerId::In7Cu, "In7.Cu"},
        {QtPcbLayerId::In8Cu, "In8.Cu"},
        {QtPcbLayerId::In9Cu, "In9.Cu"},
        {QtPcbLayerId::In10Cu, "In10.Cu"},
        {QtPcbLayerId::In11Cu, "In11.Cu"},
        {QtPcbLayerId::In12Cu, "In12.Cu"},
        {QtPcbLayerId::In13Cu, "In13.Cu"},
        {QtPcbLayerId::In14Cu, "In14.Cu"},
        {QtPcbLayerId::In15Cu, "In15.Cu"},
        {QtPcbLayerId::In16Cu, "In16.Cu"},
        {QtPcbLayerId::In17Cu, "In17.Cu"},
        {QtPcbLayerId::In18Cu, "In18.Cu"},
        {QtPcbLayerId::In19Cu, "In19.Cu"},
        {QtPcbLayerId::In20Cu, "In20.Cu"},
        {QtPcbLayerId::In21Cu, "In21.Cu"},
        {QtPcbLayerId::In22Cu, "In22.Cu"},
        {QtPcbLayerId::In23Cu, "In23.Cu"},
        {QtPcbLayerId::In24Cu, "In24.Cu"},
        {QtPcbLayerId::In25Cu, "In25.Cu"},
        {QtPcbLayerId::In26Cu, "In26.Cu"},
        {QtPcbLayerId::In27Cu, "In27.Cu"},
        {QtPcbLayerId::In28Cu, "In28.Cu"},
        {QtPcbLayerId::In29Cu, "In29.Cu"},
        {QtPcbLayerId::In30Cu, "In30.Cu"},
        {QtPcbLayerId::FMask, "F.Mask"},
        {QtPcbLayerId::BMask, "B.Mask"},
        {QtPcbLayerId::FSilkS, "F.SilkS"},
        {QtPcbLayerId::BSilkS, "B.SilkS"},
        {QtPcbLayerId::FAdhes, "F.Adhes"},
        {QtPcbLayerId::BAdhes, "B.Adhes"},
        {QtPcbLayerId::FPaste, "F.Paste"},
        {QtPcbLayerId::BPaste, "B.Paste"},
        {QtPcbLayerId::EdgeCuts, "Edge.Cuts"},
        {QtPcbLayerId::Margin, "Margin"},
        {QtPcbLayerId::FCrtYd, "F.CrtYd"},
        {QtPcbLayerId::BCrtYd, "B.CrtYd"},
        {QtPcbLayerId::FFab, "F.Fab"},
        {QtPcbLayerId::BFab, "B.Fab"},
        {QtPcbLayerId::User1, "User.1"},
        {QtPcbLayerId::User2, "User.2"},
        {QtPcbLayerId::User3, "User.3"},
        {QtPcbLayerId::User4, "User.4"},
        {QtPcbLayerId::User5, "User.5"},
        {QtPcbLayerId::User6, "User.6"},
        {QtPcbLayerId::User7, "User.7"},
        {QtPcbLayerId::User8, "User.8"},
        {QtPcbLayerId::User9, "User.9"}
    };
    
    // Check if a point is approximately equal to another
    bool isApproximatelyEqual(const QPointF& a, const QPointF& b, double epsilon = EPSILON) {
        return std::abs(a.x() - b.x()) < epsilon && std::abs(a.y() - b.y()) < epsilon;
    }
    
    // Rotate a point around another point
    QPointF rotatePoint(const QPointF& point, const QPointF& center, double angleRadians) {
        if (std::abs(angleRadians) < EPSILON) return point;
        
        QPointF translated = point - center;
        double cos_a = std::cos(angleRadians);
        double sin_a = std::sin(angleRadians);
        
        return QPointF(
            translated.x() * cos_a - translated.y() * sin_a,
            translated.x() * sin_a + translated.y() * cos_a
        ) + center;
    }
    
    // Mirror a point around a center point
    QPointF mirrorPoint(const QPointF& point, const QPointF& center, QtFlipDirection direction) {
        if (direction == QtFlipDirection::LeftRight) {
            return QPointF(2 * center.x() - point.x(), point.y());
        } else {
            return QPointF(point.x(), 2 * center.y() - point.y());
        }
    }
}

//=============================================================================
// STATIC MEMBER DEFINITIONS
//=============================================================================
const QPointF EDA_BOARD_OBJECT_DATA::s_zeroOffset(0.0, 0.0);
const QPointF EDA_BOARD_OBJECT_DATA::ZeroOffset(0.0, 0.0);
QtDeletedBoardItem* QtDeletedBoardItem::s_instance = nullptr;

// QtLayerSet static methods are now defined in qt_temporary_implementations.h

//=============================================================================
// EDA_BOARD_OBJECT_DATA IMPLEMENTATION
//=============================================================================
EDA_BOARD_OBJECT_DATA::EDA_BOARD_OBJECT_DATA(EDA_BOARD_OBJECT_CONTAINER* parent, QtKicadType type, 
                        QtPcbLayerId layer)
    : EDA_OBJECT_DATA(type)
    , m_layer(layer)
    , m_isKnockout(false)
    , m_isLocked(false)
    , m_group(nullptr)
{

}

EDA_BOARD_OBJECT_DATA::EDA_BOARD_OBJECT_DATA(const EDA_BOARD_OBJECT_DATA& other)
    : EDA_OBJECT_DATA(other)
    , m_layer(other.m_layer)
    , m_isKnockout(other.m_isKnockout)
    , m_isLocked(other.m_isLocked)
    , m_group(nullptr)  // Don't copy group relationship
{
}

EDA_BOARD_OBJECT_DATA::~EDA_BOARD_OBJECT_DATA() {
    // Ensure we're not in a group when destroyed
    if (m_group) {
        qCWarning(qtBoardItemLog) << "EDA_BOARD_OBJECT_DATA destroyed while still in group!";
    }
}

//=============================================================================
// LAYER MANAGEMENT
//=============================================================================
void EDA_BOARD_OBJECT_DATA::setLayer(QtPcbLayerId layer) {
    if (m_layer != layer) {
        QtPcbLayerId oldLayer = m_layer;
        m_layer = layer;

 
    }
}

QtLayerSet EDA_BOARD_OBJECT_DATA::getLayerSet() const {
    if (m_layer == QtPcbLayerId::UndefinedLayer) {
        return QtLayerSet();
    }
    return QtLayerSet(m_layer);
}

void EDA_BOARD_OBJECT_DATA::setLayerSet(const QtLayerSet& layers) {
    QVector<QtPcbLayerId> layerSeq = layers.sequence();
    
    if (layerSeq.count() == 1) {
        setLayer(layerSeq[0]);
        return;
    }
    
    qCWarning(qtBoardItemLog) << "Attempted to setLayerSet() on a single-layer object."
                             << "Layer count:" << layerSeq.count();
    
    // Derived classes that support multiple layers must override this method
}

QString EDA_BOARD_OBJECT_DATA::getLayerName() const {
    const EDA_BOARD_DATA* board = getBoard();
    if (board) {
        return board->getLayerName(m_layer);
    }
    
    // Return standard name if no parent board
    return layerName(m_layer);
}

QString EDA_BOARD_OBJECT_DATA::layerMaskDescription() const {
    const EDA_BOARD_DATA* board = getBoard();
    if (!board) {
        return "no board";
    }
    
    QtLayerSet layers = getLayerSet() & board->getEnabledLayers();
    QtLayerSet copperLayers = layers & QtLayerSet::allCopperLayers();
    QtLayerSet techLayers = layers & QtLayerSet::allTechLayers();
    
    // Check if all copper layers are included
    if (copperLayers.count() == board->getCopperLayerCount()) {
        return "all copper layers";
    }
    
    // Find first layer in any of the test sets
    for (const QtLayerSet& testLayers : {copperLayers, techLayers, layers}) {
        QVector<QtPcbLayerId> layerSeq = testLayers.sequence();
        if (!layerSeq.isEmpty()) {
            QString layerInfo = board->getLayerName(layerSeq.first());
            if (testLayers.count() > 1) {
                layerInfo += " and others";
            }
            return layerInfo;
        }
    }
    
    return "no layers";
}

bool EDA_BOARD_OBJECT_DATA::isOnLayer(QtPcbLayerId layer) const {
    return m_layer == layer;
}

bool EDA_BOARD_OBJECT_DATA::isOnCopperLayer() const {
    return isCopperLayer(m_layer);
}

bool EDA_BOARD_OBJECT_DATA::isSideSpecific() const {
    QtLayerSet sideSpecificLayers = QtLayerSet::sideSpecificMask();
    if ((getLayerSet() & sideSpecificLayers).any()) {
        return true;
    }
    
    // Additional board-specific side checking could be added here
    return false;
}

int EDA_BOARD_OBJECT_DATA::boardLayerCount() const {
    const EDA_BOARD_DATA* board = getBoard();
    return board ? board->getLayerSet().count() : static_cast<int>(QtPcbLayerId::LayerCount);
}

int EDA_BOARD_OBJECT_DATA::boardCopperLayerCount() const {
    const EDA_BOARD_DATA* board = getBoard();
    return board ? board->getCopperLayerCount() : 32;
}

QtLayerSet EDA_BOARD_OBJECT_DATA::boardLayerSet() const {
    const EDA_BOARD_DATA* board = getBoard();
    return board ? board->getEnabledLayers() : QtLayerSet::allLayers();
}

//=============================================================================
// POSITION AND GEOMETRY
//=============================================================================
QPointF EDA_BOARD_OBJECT_DATA::getCenter() const {
    return getBoundingRect().center();
}

void EDA_BOARD_OBJECT_DATA::setX(double x) {
    QPointF pos = getPosition();
    pos.setX(x);
    setPosition(pos);
}

void EDA_BOARD_OBJECT_DATA::setY(double y) {
    QPointF pos = getPosition();
    pos.setY(y);
    setPosition(pos);
}

//=============================================================================
// KNOCKOUT AND LOCKING
//=============================================================================
void EDA_BOARD_OBJECT_DATA::setKnockout(bool knockout) {
    if (m_isKnockout != knockout) {
        m_isKnockout = knockout;

        
    }
}

bool EDA_BOARD_OBJECT_DATA::isLocked() const {
    // Check if parent group is locked first
    if (m_group && m_group->isLocked()) {
        return true;
    }
    
    const EDA_BOARD_DATA* board = getBoard();
    // TODO: Add board use check when EDA_BOARD_DATA::getBoardUse() is implemented
    return board && m_isLocked;
}

void EDA_BOARD_OBJECT_DATA::setLocked(bool locked) {
    if (m_isLocked != locked) {
        m_isLocked = locked;

        
    }
}

//=============================================================================
// PARENT AND BOARD ACCESS
//=============================================================================
EDA_BOARD_OBJECT_CONTAINER* EDA_BOARD_OBJECT_DATA::getParent() const {
    return dynamic_cast<EDA_BOARD_OBJECT_CONTAINER*>( EDA_OBJECT_DATA::getEdaParent() );
}

EDA_FOOTPRINT_DATA* EDA_BOARD_OBJECT_DATA::getParentFootprint() const {
    EDA_BOARD_OBJECT_CONTAINER* ancestor = getParent();
    
    // Skip through containers like groups and generators
    while (ancestor) {
        QtKicadType type = ancestor->getType();
        if (type == QtKicadType::PcbGroup || type == QtKicadType::PcbGenerator || 
            type == QtKicadType::PcbTable) {
            ancestor = ancestor->getParent();
        } else {
            break;
        }
    }
    
    if (ancestor && ancestor->getType() == QtKicadType::PcbFootprint) {
        return dynamic_cast<EDA_FOOTPRINT_DATA*>(ancestor);
    }
    
    return nullptr;
}

const EDA_BOARD_DATA* EDA_BOARD_OBJECT_DATA::getBoard() const {
    if (getType() == QtKicadType::Pcb) {
        return dynamic_cast<const EDA_BOARD_DATA*>(this);
    }
    
    EDA_BOARD_OBJECT_CONTAINER* parent = getParent();
    return parent ? parent->getBoard() : nullptr;
}

EDA_BOARD_DATA* EDA_BOARD_OBJECT_DATA::getBoard() {
    if (getType() == QtKicadType::Pcb) {
        return dynamic_cast<EDA_BOARD_DATA*>(this);
    }
    
    EDA_BOARD_OBJECT_CONTAINER* parent = getParent();
    return parent ? parent->getBoard() : nullptr;
}

QString EDA_BOARD_OBJECT_DATA::getParentAsString() const {
    EDA_FOOTPRINT_DATA* fp = dynamic_cast<EDA_FOOTPRINT_DATA*>(getParent());
    if (fp) {
        return fp->getReference();
    }
    
    EDA_OBJECT_DATA* parent = getParent();
    return parent ? parent->getUuid().toString() : QString();
}

//=============================================================================
// FOOTPRINT RELATIVE POSITIONING
//=============================================================================
QPointF EDA_BOARD_OBJECT_DATA::getFPRelativePosition() const {
    QPointF pos = getPosition();
    
    EDA_FOOTPRINT_DATA* parentFP = getParentFootprint();
    if (parentFP) {
        pos -= parentFP->getPosition();
        
        // Rotate by negative of footprint orientation
        double angle = -parentFP->getOrientationRadians();
        if (std::abs(angle) > EPSILON) {
            pos = ::rotatePoint(pos, QPointF(0, 0), angle);
        }
    }
    
    return pos;
}

void EDA_BOARD_OBJECT_DATA::setFPRelativePosition(const QPointF& position) {
    QPointF pos = position;
    
    EDA_FOOTPRINT_DATA* parentFP = getParentFootprint();
    if (parentFP) {
        // Rotate by footprint orientation
        double angle = parentFP->getOrientationRadians();
        if (std::abs(angle) > EPSILON) {
            pos = ::rotatePoint(pos, QPointF(0, 0), angle);
        }
        
        pos += parentFP->getPosition();
    }
    
    setPosition(pos);
}

//=============================================================================
// GEOMETRIC TRANSFORMATIONS
//=============================================================================
void EDA_BOARD_OBJECT_DATA::move(const QPointF& moveVector) {
    qCWarning(qtBoardItemLog) << "Virtual EDA_BOARD_OBJECT_DATA::move called for" << getClass()
                             << "- should be overridden in derived class";
}

void EDA_BOARD_OBJECT_DATA::rotate(const QPointF& rotationCenter, double angleRadians) {
    qCWarning(qtBoardItemLog) << "Virtual EDA_BOARD_OBJECT_DATA::rotate called for" << getClass()
                             << "- should be overridden in derived class";
}

void EDA_BOARD_OBJECT_DATA::flip(const QPointF& center, QtFlipDirection flipDirection) {
    qCWarning(qtBoardItemLog) << "Virtual EDA_BOARD_OBJECT_DATA::flip called for" << getClass()
                             << "- should be overridden in derived class";
}

void EDA_BOARD_OBJECT_DATA::mirror(const QPointF& center, QtFlipDirection flipDirection) {
    qCWarning(qtBoardItemLog) << "Virtual EDA_BOARD_OBJECT_DATA::mirror called for" << getClass()
                             << "- should be overridden in derived class";
}

//=============================================================================
// SHAPE AND GEOMETRY
//=============================================================================
std::shared_ptr<QtShape> EDA_BOARD_OBJECT_DATA::getEffectiveShape(QtPcbLayerId layer, QtFlashing flash) const {
    static std::shared_ptr<QtShape> emptyShape;
    
    // qCWarning(qtBoardItemLog) << "EDA_BOARD_OBJECT_DATA::getEffectiveShape() not implemented for" << getClass();
    return emptyShape;
}

std::shared_ptr<QtShapeSegment> EDA_BOARD_OBJECT_DATA::getEffectiveHoleShape() const {
    static std::shared_ptr<QtShapeSegment> emptyShape;
    
    // qCWarning(qtBoardItemLog) << "EDA_BOARD_OBJECT_DATA::getEffectiveHoleShape() not implemented for" << getClass();
    return emptyShape;
}

//=============================================================================
// STROKE PROPERTIES
//=============================================================================
//QtStrokeParams EDA_BOARD_OBJECT_DATA::getStroke() const {
//    qCWarning(qtBoardItemLog) << "EDA_BOARD_OBJECT_DATA::getStroke() not implemented for" << getClass();
//    
//    // Return default stroke parameters
//    QtStrokeParams defaultStroke;
//    defaultStroke.setWidth(0.15); // Default line width in mm
//    return defaultStroke;
//}

void EDA_BOARD_OBJECT_DATA::setStroke(const QtStrokeParams& stroke) {
    qCWarning(qtBoardItemLog) << "EDA_BOARD_OBJECT_DATA::setStroke() not implemented for" << getClass();
}

//=============================================================================
// DUPLICATION AND DATA SWAPPING
//=============================================================================
EDA_BOARD_OBJECT_DATA* EDA_BOARD_OBJECT_DATA::duplicate() const {
    EDA_BOARD_OBJECT_DATA* dupe = dynamic_cast<EDA_BOARD_OBJECT_DATA*>(clone());
    if (dupe) {
        // Note: UUID management may need to be implemented based on actual requirements
        
        if (dupe->getParentGroup()) {
            dupe->getParentGroup()->addItem(dupe);
        }
    }
    
    return dupe;
}

void EDA_BOARD_OBJECT_DATA::swapItemData(EDA_BOARD_OBJECT_DATA* other) {
    if (!other) return;
    
    EDA_OBJECT_DATA* parent = getParent();
    QtPcbGroup* group = getParentGroup();
    
    // Temporarily clear group relationships
    setParentGroup(nullptr);
    other->setParentGroup(nullptr);
    
    // Perform the data swap
    swapData(other);
    
    // Restore pointers
    setEdaParent(parent);
    setParentGroup(group);
}

void EDA_BOARD_OBJECT_DATA::swapData(EDA_BOARD_OBJECT_DATA* other) {
    if (!other) return;
    
    // Base implementation swaps core board item data
    std::swap(m_layer, other->m_layer);
    std::swap(m_isKnockout, other->m_isKnockout);
    std::swap(m_isLocked, other->m_isLocked);
    

}

//=============================================================================
// POLYGON TRANSFORMATION
//=============================================================================
void EDA_BOARD_OBJECT_DATA::transformShapeToPolygon(QtShapePolySet& buffer, QtPcbLayerId layer,
                                         double clearance, double error, 
                                         QtErrorLoc errorLocation, bool ignoreLineWidth) const {
    qCWarning(qtBoardItemLog) << "EDA_BOARD_OBJECT_DATA::transformShapeToPolygon() called on unsupported object:" << getClass();
}

//=============================================================================
// DELETION
//=============================================================================
void EDA_BOARD_OBJECT_DATA::deleteStructure() {
    EDA_BOARD_OBJECT_CONTAINER* parent = getParent();
    if (parent) {
        parent->removeItem(this);  // Use removeItem instead of remove
    }
    
    delete this;
}

//=============================================================================
// COMPARISON AND SORTING
//=============================================================================
bool EDA_BOARD_OBJECT_DATA::PtrComparator::operator()(const EDA_BOARD_OBJECT_DATA* a, const EDA_BOARD_OBJECT_DATA* b) const {
    if (!a || !b) return a < b;
    
    // Compare by type first
    if (a->getType() != b->getType()) {
        return static_cast<int>(a->getType()) < static_cast<int>(b->getType());
    }
    
    // Compare by layer set
    if (a->getLayerSet() != b->getLayerSet()) {
        QVector<QtPcbLayerId> aLayers = a->getLayerSet().sequence();
        QVector<QtPcbLayerId> bLayers = b->getLayerSet().sequence();
        
        for (int i = 0; i < std::min(aLayers.size(), bLayers.size()); ++i) {
            if (aLayers[i] != bLayers[i]) {
                return static_cast<int>(aLayers[i]) < static_cast<int>(bLayers[i]);
            }
        }
        
        return aLayers.size() < bLayers.size();
    }
    
    // Compare by UUID
    if (a->getUuid() != b->getUuid()) {
        return a->getUuid() < b->getUuid();
    }
    
    // Final fallback to pointer comparison
    return a < b;
}

//=============================================================================
// STATIC UTILITIES
//=============================================================================
const QPointF& EDA_BOARD_OBJECT_DATA::zeroOffset() {
    return s_zeroOffset;
}

bool EDA_BOARD_OBJECT_DATA::isValidLayer(QtPcbLayerId layer) {
    return layer != QtPcbLayerId::UndefinedLayer && 
           layer != QtPcbLayerId::UnselectedLayer &&
           static_cast<int>(layer) >= 0 &&
           static_cast<int>(layer) < static_cast<int>(QtPcbLayerId::LayerCount);
}

QString EDA_BOARD_OBJECT_DATA::layerName(QtPcbLayerId layer) {
    return LAYER_NAMES.value(layer, "Unknown Layer");
}

QtLayerSet EDA_BOARD_OBJECT_DATA::copperLayerSet() {
    return QtLayerSet::allCopperLayers();
}

QtLayerSet EDA_BOARD_OBJECT_DATA::techLayerSet() {
    return QtLayerSet::allTechLayers();
}

//=============================================================================
// QTDELETED_BOARD_ITEM IMPLEMENTATION
//=============================================================================
QtDeletedBoardItem* QtDeletedBoardItem::getInstance() {
    if (!s_instance) {
        s_instance = new QtDeletedBoardItem();
    }
    return s_instance;
}

QString QtDeletedBoardItem::getItemDescription(bool full) const {
    Q_UNUSED(full)
    return "(Deleted Item)";
}

//=============================================================================
// HELPER FUNCTION IMPLEMENTATIONS
//=============================================================================
// These functions are already defined as inline in the header file

