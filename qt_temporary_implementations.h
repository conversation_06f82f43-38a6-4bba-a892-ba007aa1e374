/*
 * Temporary implementations for classes not yet migrated to Qt
 * 
 * This file contains minimal implementations of classes that are used by
 * multiple migrated classes but are not themselves in the migration scope.
 * These implementations ensure compilation and provide basic functionality.
 * 
 * NOTE: These are placeholder implementations. When the actual classes
 * are migrated, they should replace these temporary versions.
 */

#ifndef QT_TEMPORARY_IMPLEMENTATIONS_H
#define QT_TEMPORARY_IMPLEMENTATIONS_H

// Ensure QtPcbLayerId is defined first to avoid circular dependencies
#ifndef QTPCBLAYERID_DEFINED
#define QTPCBLAYERID_DEFINED
enum class QtPcbLayerId : int
{
    // Special layers
    UndefinedLayer = -1,
    UnselectedLayer = -2,

    // Copper layers
    FCu = 0,     // Front copper
    BCu = 2,     // Back copper
    In1Cu = 4,   // Inner layer 1
    In2Cu = 6,   // Inner layer 2
    In3Cu = 8,   // Inner layer 3
    In4Cu = 10,  // Inner layer 4
    In5Cu = 12,  // Inner layer 5
    In6Cu = 14,  // Inner layer 6
    In7Cu = 16,  // Inner layer 7
    In8Cu = 18,  // Inner layer 8
    In9Cu = 20,  // Inner layer 9
    In10Cu = 22, // Inner layer 10
    In11Cu = 24, // Inner layer 11
    In12Cu = 26, // Inner layer 12
    In13Cu = 28, // Inner layer 13
    In14Cu = 30, // Inner layer 14
    In15Cu = 32, // Inner layer 15
    In16Cu = 34, // Inner layer 16
    In17Cu = 36, // Inner layer 17
    In18Cu = 38, // Inner layer 18
    In19Cu = 40, // Inner layer 19
    In20Cu = 42, // Inner layer 20
    In21Cu = 44, // Inner layer 21
    In22Cu = 46, // Inner layer 22
    In23Cu = 48, // Inner layer 23
    In24Cu = 50, // Inner layer 24
    In25Cu = 52, // Inner layer 25
    In26Cu = 54, // Inner layer 26
    In27Cu = 56, // Inner layer 27
    In28Cu = 58, // Inner layer 28
    In29Cu = 60, // Inner layer 29
    In30Cu = 62, // Inner layer 30

    // Mask layers
    FMask = 1, // Front solder mask
    BMask = 3, // Back solder mask

    // Silkscreen layers
    FSilkS = 5, // Front silkscreen
    BSilkS = 7, // Back silkscreen

    // Technical layers
    FAdhes = 9,  // Front adhesive
    BAdhes = 11, // Back adhesive
    FPaste = 13, // Front solder paste
    BPaste = 15, // Back solder paste

    // Edge cuts
    EdgeCuts = 17, // Board outline

    // Margin
    Margin = 19, // Keep-out area

    // Courtyard layers
    FCrtYd = 21, // Front courtyard
    BCrtYd = 23, // Back courtyard

    // Fabrication layers
    FFab = 25, // Front fabrication
    BFab = 27, // Back fabrication

    // User layers
    User1 = 29, // User layer 1
    User2 = 31, // User layer 2
    User3 = 33, // User layer 3
    User4 = 35, // User layer 4
    User5 = 37, // User layer 5
    User6 = 39, // User layer 6
    User7 = 41, // User layer 7
    User8 = 43, // User layer 8
    User9 = 45, // User layer 9

    // Total count
    LayerCount = 46
};
#endif // QTPCBLAYERID_DEFINED

// Legacy layer constants for compatibility
constexpr QtPcbLayerId F_SilkS = QtPcbLayerId::FSilkS;
constexpr QtPcbLayerId B_SilkS = QtPcbLayerId::BSilkS;
constexpr QtPcbLayerId F_SILKS = QtPcbLayerId::FSilkS;
constexpr QtPcbLayerId B_SILKS = QtPcbLayerId::BSilkS;
constexpr QtPcbLayerId F_Fab = QtPcbLayerId::FFab;
constexpr QtPcbLayerId B_Fab = QtPcbLayerId::BFab;
constexpr QtPcbLayerId F_Cu = QtPcbLayerId::FCu;
constexpr QtPcbLayerId B_Cu = QtPcbLayerId::BCu;
constexpr QtPcbLayerId UNDEFINED_LAYER = QtPcbLayerId::UndefinedLayer;

// Flip direction enumeration
enum class QtFlipDirection : int
{
    FlipLeftRight = 0,
    FlipTopBottom = 1,
    LeftRight = 0,     // Alias for compatibility
    UpDown = 1         // Alias for compatibility
};

// Legacy flip direction constants
constexpr QtFlipDirection FLIP_LEFT_RIGHT = QtFlipDirection::FlipLeftRight;
constexpr QtFlipDirection LeftRight = QtFlipDirection::LeftRight;
constexpr QtFlipDirection LEFT_RIGHT = QtFlipDirection::LeftRight;

#include <QObject>
#include <QString>
#include <QPoint>
#include <QPointF>
#include <QSize>
#include <QRect>
#include <QPolygonF>
#include <QVector>
#include <QMap>
#include <QUuid>
#include <QVector2D>
#include <memory>

// Forward declarations to avoid circular dependencies
class EDA_BOARD_DATA;
class EDA_BOARD_OBJECT_DATA;
class EDA_NET_DATA;
// class EDA_PAD_DATA; // Defined in eda_pad_data.h
class EDA_ARC_DATA;

// Forward declarations for enums defined in other headers
enum class QtAddMode : int;
enum class QtRemoveMode : int;

//==============================================================================
// Basic Type Definitions - Used across multiple classes
//==============================================================================

// Angle representation - used in many geometry operations
struct QtEdaAngle {
    double value;
    QtEdaAngle(double v = 0.0) : value(v) {}
    operator double() const { return value; }
    QtEdaAngle& operator+=(const QtEdaAngle& other) { value += other.value; return *this; }
    bool operator==(const QtEdaAngle& other) const { return value == other.value; }
    bool operator!=(const QtEdaAngle& other) const { return value != other.value; }
    void normalize() { while(value >= 360.0) value -= 360.0; while(value < 0.0) value += 360.0; }
    double asRadians() const { return value * M_PI / 180.0; }
};

// 3D vector for footprint models
struct QtVector3D {
    double x, y, z;
    QtVector3D(double _x = 1.0, double _y = 1.0, double _z = 1.0) : x(_x), y(_y), z(_z) {}
    bool operator==(const QtVector3D& other) const {
        return x == other.x && y == other.y && z == other.z;
    }
};

// Library identifier for components
struct QtLibId {
    QString libNickname;
    QString libItemName;
    
    QString format() const { return libNickname + ":" + libItemName; }
    void parse(const QString& id) {
        int colonPos = id.indexOf(':');
        if (colonPos != -1) {
            libNickname = id.left(colonPos);
            libItemName = id.mid(colonPos + 1);
        } else {
            libNickname.clear();
            libItemName = id;
        }
    }
    QString getUniStringLibNickname() const { return libNickname; }
    QString getUniStringLibItemName() const { return libItemName; }

    bool operator==(const QtLibId& other) const {
        return libNickname == other.libNickname && libItemName == other.libItemName;
    }

    bool operator!=(const QtLibId& other) const {
        return !(*this == other);
    }
};

// Path of unique identifiers in hierarchy
struct QtKiidPath {
    QVector<QUuid> path;
};

//==============================================================================
// Board Design Settings - Used by many classes for default values
//==============================================================================
class QtBoardDesignSettings {
public:
    virtual ~QtBoardDesignSettings() = default;
    
    // Track settings
    virtual int getTrackWidth() const { return 250000; }  // 0.25mm default
    virtual int getCurrentTrackWidth() const { return 250000; }
    virtual int getDiffPairWidth() const { return 200000; }
    virtual int getDiffPairGap() const { return 200000; }
    virtual bool getUseConnectedTrackWidth() const { return false; }
    
    // Via settings
    virtual int getViaDiameter() const { return 600000; }  // 0.6mm default
    virtual int getViaDrill() const { return 300000; }     // 0.3mm default
    virtual int getCurrentViaDiameter() const { return 600000; }
    virtual int getCurrentViaDrill() const { return 300000; }
    virtual int getCurrentViaType() const { return 0; }  // Through via
    
    // Text settings
    virtual QSize getTextSize(int layer) const { return QSize(1000000, 1000000); }  // 1mm x 1mm
    virtual int getTextThickness(int layer) const { return 150000; }  // 0.15mm
    virtual bool getTextItalic(int layer) const { return false; }
    virtual bool getTextUpright(int layer) const { return true; }
    
    // Line settings
    virtual int getLineThickness(int layer) const { return 100000; }  // 0.1mm
    virtual int getLineThickness(QtPcbLayerId layer) const { return getLineThickness(static_cast<int>(layer)); }
    
    // Solder mask settings
    virtual int getSolderMaskExpansion() const { return 100000; }  // 0.1mm default
    
    // Net settings (if exists)
    std::shared_ptr<class QtNetSettings> m_NetSettings;
};

//==============================================================================
// Shape Classes - Core geometry representations
//==============================================================================

// Polygon set for complex shapes
class QtShapePolySet {
public:
    using VertexIndex = struct { int outline; int vertex; };
    
    QtShapePolySet() = default;
    QtShapePolySet(const QtShapePolySet& other) : m_polygons(other.m_polygons) {}
    
    // Basic operations
    bool isEmpty() const { return m_polygons.isEmpty(); }
    void clear() { m_polygons.clear(); }
    
    // Polygon management
    void newOutline() { m_polygons.append(QPolygonF()); }
    void append(const QPointF& pt) { 
        if (!m_polygons.isEmpty()) 
            m_polygons.last().append(pt); 
    }
    void append(double x, double y) { append(QPointF(x, y)); }
    void append(const QPolygonF& poly) { m_polygons.append(poly); }
    void append(const QtShapePolySet& other) { m_polygons.append(other.m_polygons); }
    void addPolygon(const QPolygonF& poly) { m_polygons.append(poly); }
    
    // Vertex access
    int vertexCount() const {
        int count = 0;
        for (const auto& poly : m_polygons) count += poly.size();
        return count;
    }
    
    QPointF cVertex(int index) const {
        int current = 0;
        for (const auto& poly : m_polygons) {
            if (index < current + poly.size()) {
                return poly[index - current];
            }
            current += poly.size();
        }
        return QPointF();
    }
    
    QPointF cVertex(const VertexIndex& index) const {
        if (index.outline >= 0 && index.outline < m_polygons.size() &&
            index.vertex >= 0 && index.vertex < m_polygons[index.outline].size()) {
            return m_polygons[index.outline][index.vertex];
        }
        return QPointF();
    }
    
    void setVertex(const VertexIndex& index, const QPointF& pt) {
        if (index.outline >= 0 && index.outline < m_polygons.size() &&
            index.vertex >= 0 && index.vertex < m_polygons[index.outline].size()) {
            m_polygons[index.outline][index.vertex] = pt;
        }
    }
    
    // Geometric operations
    QRectF bbox() const {
        if (m_polygons.isEmpty()) return QRectF();
        QRectF rect;
        bool first = true;
        for (const auto& poly : m_polygons) {
            for (const auto& pt : poly) {
                if (first) {
                    rect = QRectF(pt, pt);
                    first = false;
                } else {
                    rect = rect.united(QRectF(pt, pt));
                }
            }
        }
        return rect;
    }
    
    double area() const {
        double total = 0.0;
        for (const auto& poly : m_polygons) {
            if (poly.size() < 3) continue;
            double a = 0.0;
            for (int i = 0; i < poly.size(); ++i) {
                int j = (i + 1) % poly.size();
                a += poly[i].x() * poly[j].y() - poly[j].x() * poly[i].y();
            }
            total += qAbs(a) / 2.0;
        }
        return total;
    }
    
    void move(const QPointF& offset) {
        for (auto& poly : m_polygons) {
            for (auto& pt : poly) {
                pt += offset;
            }
        }
    }
    
    void rotate(double angleRadians, const QPointF& center) {
        double cosA = qCos(angleRadians);
        double sinA = qSin(angleRadians);

        for (auto& poly : m_polygons) {
            for (auto& pt : poly) {
                QPointF rel = pt - center;
                pt.setX(center.x() + rel.x() * cosA - rel.y() * sinA);
                pt.setY(center.y() + rel.x() * sinA + rel.y() * cosA);
            }
        }
    }

    void mirror(const QPointF& center, int flipDirection) {
        for (auto& poly : m_polygons) {
            for (auto& pt : poly) {
                if (flipDirection == 0) { // Horizontal flip
                    pt.setX(2 * center.x() - pt.x());
                } else { // Vertical flip
                    pt.setY(2 * center.y() - pt.y());
                }
            }
        }
    }
    
    void simplify() {
        // Simplification logic would go here
    }
    
    void inflate(int clearance, int cornerStrategy = 0, int maxError = 0) {
        // Basic inflation - in real implementation would handle corner strategies
        Q_UNUSED(cornerStrategy)
        Q_UNUSED(maxError)
        // This is a placeholder - actual implementation would properly inflate polygons
    }
    
    // Collision detection
    bool collideVertex(const QPointF& point, VertexIndex* index = nullptr, int accuracy = 0) const;
    bool collideEdge(const QPointF& point, VertexIndex* index = nullptr, int accuracy = 0) const;
    bool collide(const QPointF& point, int accuracy = 0) const;
    
    // Data access
    const QVector<QPolygonF>& getPolygons() const { return m_polygons; }
    
private:
    QVector<QPolygonF> m_polygons;
};

// Basic shapes
class QtShape {
public:
    virtual ~QtShape() = default;
};



class QtShapeCircle : public QtShape {
public:
    QtShapeCircle(const QPoint& center, int radius) 
        : m_center(center), m_radius(radius) {}
    
    QPoint getCenter() const { return m_center; }
    int getRadius() const { return m_radius; }
    
private:
    QPoint m_center;
    int m_radius;
};


class QtShapeSegment : public QtShape {
public:
    QtShapeSegment() = default;
    QtShapeSegment(const QPointF& start, const QPointF& end, int width = 0) 
        : m_start(start), m_end(end), m_width(width) {}
    
    QPointF getStart() const { return m_start; }
    QPointF getEnd() const { return m_end; }
    int getWidth() const { return m_width; }
    void setStart(const QPointF& start) { m_start = start; }
    void setEnd(const QPointF& end) { m_end = end; }
    void setWidth(int width) { m_width = width; }
    
private:
    QPointF m_start;
    QPointF m_end;
    int m_width = 0;
};

class QtShapeArc : public QtShape {
public:
    QtShapeArc() = default;
    QtShapeArc(const QPointF& center, const QPointF& start, double angle, int width = 0)
        : m_center(center), m_start(start), m_angle(angle), m_width(width) {}
    
    QPointF getCenter() const { return m_center; }
    QPointF getStart() const { return m_start; }
    double getAngle() const { return m_angle; }
    int getWidth() const { return m_width; }
    void setCenter(const QPointF& center) { m_center = center; }
    void setStart(const QPointF& start) { m_start = start; }
    void setAngle(double angle) { m_angle = angle; }
    void setWidth(int width) { m_width = width; }
    
private:
    QPointF m_center;
    QPointF m_start;
    double m_angle = 0.0;
    int m_width = 0;
};

class QtShapeCompound : public QtShape {
public:
    void addShape(std::shared_ptr<QtShape> shape) { m_shapes.append(shape); }
    const QVector<std::shared_ptr<QtShape>>& getShapes() const { return m_shapes; }
    
private:
    QVector<std::shared_ptr<QtShape>> m_shapes;
};

//==============================================================================
// Drawing/UI Related Classes
//==============================================================================

class QtMsgPanelItem {
public:
    QtMsgPanelItem(const QString& label, const QString& value) 
        : m_label(label), m_value(value) {}
    
    QString getLabel() const { return m_label; }
    QString getValue() const { return m_value; }
    
private:
    QString m_label;
    QString m_value;
};

class QtUnitsProvider {
public:
    virtual ~QtUnitsProvider() = default;
    virtual QString messageTextFromValue(double value, bool addUnits = true) const {
        Q_UNUSED(addUnits)
        return QString::number(value / 1000000.0, 'f', 3) + " mm";
    }
    virtual QString messageTextFromValue(int value, bool addUnits = true) const {
        return messageTextFromValue(static_cast<double>(value), addUnits);
    }
};

class QtEdaDrawFrame : public QtUnitsProvider {
public:
    virtual QString getName() const { return "QtEdaDrawFrame"; }
};

class QtProgressReporter {
public:
    virtual ~QtProgressReporter() = default;
    virtual void report(const QString& message) { Q_UNUSED(message) }
    virtual void setCurrentProgress(double progress) { Q_UNUSED(progress) }
    virtual void keepRefreshing(bool refresh) { Q_UNUSED(refresh) }
};

//==============================================================================
// Connectivity and DRC Classes
//==============================================================================

class QtDrcConstraint {
public:
    virtual ~QtDrcConstraint() = default;
    virtual bool hasMinValue() const { return false; }
    virtual int getMinValue() const { return 0; }
    virtual QString getName() const { return QString(); }
};

class QtBoardCommit {
public:
    virtual ~QtBoardCommit() = default;
    virtual void add(EDA_BOARD_OBJECT_DATA* item) { Q_UNUSED(item) }
    virtual void modify(EDA_BOARD_OBJECT_DATA* item) { Q_UNUSED(item) }
    virtual void removed(EDA_NET_DATA* item) { Q_UNUSED(item) }
};

//==============================================================================
// Search and View Classes
//==============================================================================

// QtEdaSearchData is defined in eda_object_data.h

class QtKigfxView {
public:
    virtual ~QtKigfxView() = default;
};

class QtView : public QtKigfxView {
public:
    virtual ~QtView() = default;
    
    // Add missing view methods
    virtual double viewGetLOD(int layer) const {
        Q_UNUSED(layer)
        return 1.0; // Default level of detail
    }
    
    // Add missing methods from compilation errors
    virtual double getScale() const {
        return 1.0; // Default scale
    }
    
    virtual double toWorld(double screenValue) const {
        return screenValue; // Default 1:1 conversion
    }
    
    virtual bool isLayerVisible(int layer) const {
        Q_UNUSED(layer)
        return true; // Default visible
    }
};

//==============================================================================
// Other Utility Classes
//==============================================================================

class QtNetclass {
public:
    QString getName() const { return m_name; }
    void setName(const QString& name) { m_name = name; }
    
private:
    QString m_name;
};

class QtNetSettings {
public:
    virtual ~QtNetSettings() = default;
};

class QtPageInfo {
public:
    virtual ~QtPageInfo() = default;
};

class QtTitleBlock {
public:
    virtual ~QtTitleBlock() = default;
};

class QtPcbPlotParams {
public:
    virtual ~QtPcbPlotParams() = default;
};

class QtIsolatedIslands {
public:
    virtual ~QtIsolatedIslands() = default;
};

class QtBox2I {
public:
    QtBox2I() = default;
    QtBox2I(const QPoint& pos, const QSize& size) : m_rect(pos, size) {}
    QtBox2I(const QRectF& rectF) : m_rect(rectF.toRect()) {}  // Convert QRectF to QRect
    
    void inflate(int amount) { m_rect.adjust(-amount, -amount, amount, amount); }
    bool contains(const QPoint& pt) const { return m_rect.contains(pt); }
    bool contains(const QtBox2I& other) const { return m_rect.contains(other.m_rect); }
    bool intersects(const QtBox2I& other) const { return m_rect.intersects(other.m_rect); }
    
private:
    QRect m_rect;
};

class QtGeometryUtils {
public:
    static double distance(const QPointF& p1, const QPointF& p2) {
        QPointF diff = p2 - p1;
        return qSqrt(diff.x() * diff.x() + diff.y() * diff.y());
    }
};

class QtKifontMetrics {
public:
    virtual ~QtKifontMetrics() = default;
};

class QtStroke {
public:
    double getWidth() const { return m_width; }
    void setWidth(double width) { m_width = width; }
    
private:
    double m_width = 100000;  // 0.1mm default
};

// QtPadAttribute and QtPadProperty are defined in eda_pad_data.h
// No forward declarations needed - use #include "eda_pad_data.h" when needed

// Placeholder for painters
class QtPcbPainter {
public:
    virtual ~QtPcbPainter() = default;
};

// Placeholder for projects
class QtProject {
public:
    virtual ~QtProject() = default;
};

// Template class for min/opt/max values
template<typename T>
class QtMinOptMax {
public:
    T min, opt, max;
    
    QtMinOptMax() : min(T()), opt(T()), max(T()) {}
    QtMinOptMax(T minVal, T optVal, T maxVal) : min(minVal), opt(optVal), max(maxVal) {}
    
    T Min() const { return min; }
    T Opt() const { return opt; }
    T Max() const { return max; }
    
    void SetMin(T val) { min = val; }
    void SetOpt(T val) { opt = val; }
    void SetMax(T val) { max = val; }
};


class QtPadStack {
public:
    virtual ~QtPadStack() = default;
};

class QtDrillInfo {
public:
    virtual ~QtDrillInfo() = default;
};

// QtPadShape is defined as enum class in eda_padstack_data.h - removed from temporary implementations

class QtEdaItemFlags {
public:
    virtual ~QtEdaItemFlags() = default;
};

class QtDrawFrame : public QtEdaDrawFrame {
public:
    virtual ~QtDrawFrame() = default;
};

class QtPcbEditFrame : public QtEdaDrawFrame {
public:
    virtual ~QtPcbEditFrame() = default;
    
    // Add missing methods used in EDA_BRD_TEXT_DATA
    virtual QString ellipsizeStatusText(const QString& text) const {
        return text; // Simple implementation - just return the text as-is
    }
};

// Forward declaration - QtEdaSearchMatchMode enum class is defined in eda_object_data.h
enum class QtEdaSearchMatchMode;

// Search data class for text matching
class QtEdaSearchData {
public:
    QtEdaSearchData(const QString& searchText = QString());
    virtual ~QtEdaSearchData() = default;
    
    const QString& getSearchText() const { return m_searchText; }
    void setSearchText(const QString& text) { m_searchText = text; }
    
    // Search string access (required by eda_object_data.cpp)
    const QString& findString;  // Reference to m_searchText
    
    // Replace functionality (required by eda_object_data.cpp)
    bool searchAndReplace = false;
    QString replaceString;
    
    // Match mode (required by eda_object_data.cpp)
    QtEdaSearchMatchMode matchMode;
    
    // Additional search criteria
    bool matchCase() const { return m_matchCase; }
    void setMatchCase(bool match) { m_matchCase = match; }
    
    bool wholeWord() const { return m_wholeWord; }
    void setWholeWord(bool whole) { m_wholeWord = whole; }
    
private:
    QString m_searchText;
    bool m_matchCase = false;
    bool m_wholeWord = false;
};

// PCB arc type - type alias to the actual implementation
using QtPcbArc = EDA_ARC_DATA;

// Forward declaration - QtPcbGroup will be properly implemented when needed
class QtPcbGroup;

//==============================================================================
// Layer Management and Zone Classes
//==============================================================================


// Unified QtLayerSet - used across all files
#ifndef QTLAYERSET_DEFINED
#define QTLAYERSET_DEFINED
class QtLayerSet {
private:
    uint64_t m_layers = 0;

public:
    QtLayerSet() = default;
    QtLayerSet(const QtLayerSet& other) : m_layers(other.m_layers) {}
    QtLayerSet& operator=(const QtLayerSet& other) { m_layers = other.m_layers; return *this; }

    // Single layer constructor
    explicit QtLayerSet(QtPcbLayerId layer) { set(layer); }

    // Factory methods for list construction to avoid constructor ambiguity
    static QtLayerSet fromList(const QList<QtPcbLayerId>& layers) {
        QtLayerSet result;
        for(const auto& l : layers) result.set(l);
        return result;
    }

    static QtLayerSet fromVector(const QVector<QtPcbLayerId>& layers) {
        QtLayerSet result;
        for(const auto& l : layers) result.set(l);
        return result;
    }

    ~QtLayerSet() = default;
    
    // Set/clear individual layers
    void set(QtPcbLayerId layer) {
        if (static_cast<int>(layer) >= 0 && static_cast<int>(layer) < 64) {
            m_layers |= (1ULL << static_cast<int>(layer));
        }
    }
    
    void reset(QtPcbLayerId layer) {
        if (static_cast<int>(layer) >= 0 && static_cast<int>(layer) < 64) {
            m_layers &= ~(1ULL << static_cast<int>(layer));
        }
    }

    void clear() {
        m_layers = 0;
    }
    
    bool test(QtPcbLayerId layer) const {
        if (static_cast<int>(layer) >= 0 && static_cast<int>(layer) < 64) {
            return (m_layers & (1ULL << static_cast<int>(layer))) != 0;
        }
        return false;
    }
    
    // Set operations
    QtLayerSet& operator|=(const QtLayerSet& other) { m_layers |= other.m_layers; return *this; }
    QtLayerSet& operator&=(const QtLayerSet& other) { m_layers &= other.m_layers; return *this; }
    QtLayerSet operator&(const QtLayerSet& other) const { QtLayerSet result; result.m_layers = m_layers & other.m_layers; return result; }
    QtLayerSet operator|(const QtLayerSet& other) const { QtLayerSet result; result.m_layers = m_layers | other.m_layers; return result; }
    bool operator!=(const QtLayerSet& other) const { return m_layers != other.m_layers; }
    bool operator==(const QtLayerSet& other) const { return m_layers == other.m_layers; }
    
    // Utility methods
    int count() const { 
        int cnt = 0;
        for(int i = 0; i < 64; i++) if((m_layers >> i) & 1) cnt++;
        return cnt;
    }
    
    bool any() const {
        return m_layers != 0;
    }
    
    bool empty() const {
        return m_layers == 0;
    }
    
    QList<QtPcbLayerId> sequence() const {
        QList<QtPcbLayerId> result;
        for(int i = 0; i < 64; i++) {
            if((m_layers >> i) & 1) {
                result.append(static_cast<QtPcbLayerId>(i));
            }
        }
        return result;
    }
    
    // Static layer set generators
    static QtLayerSet allLayers() {
        QtLayerSet result;
        result.m_layers = 0xFFFFFFFFFFFFFFFFULL;
        return result;
    }
    
    static QtLayerSet allCopperLayers() {
        QtLayerSet result;
        for(int i = 0; i <= 62; i += 2) { // Copper layers are even numbers
            result.set(static_cast<QtPcbLayerId>(i));
        }
        return result;
    }
    
    static QtLayerSet allTechLayers() {
        QtLayerSet result;
        result.set(QtPcbLayerId::FMask);
        result.set(QtPcbLayerId::BMask);
        result.set(QtPcbLayerId::FSilkS);
        result.set(QtPcbLayerId::BSilkS);
        result.set(QtPcbLayerId::FAdhes);
        result.set(QtPcbLayerId::BAdhes);
        result.set(QtPcbLayerId::FPaste);
        result.set(QtPcbLayerId::BPaste);
        result.set(QtPcbLayerId::EdgeCuts);
        result.set(QtPcbLayerId::Margin);
        result.set(QtPcbLayerId::FCrtYd);
        result.set(QtPcbLayerId::BCrtYd);
        result.set(QtPcbLayerId::FFab);
        result.set(QtPcbLayerId::BFab);
        return result;
    }
    
    static QtLayerSet sideSpecificMask() {
        QtLayerSet result;
        result.set(QtPcbLayerId::FMask);
        result.set(QtPcbLayerId::BMask);
        result.set(QtPcbLayerId::FSilkS);
        result.set(QtPcbLayerId::BSilkS);
        result.set(QtPcbLayerId::FAdhes);
        result.set(QtPcbLayerId::BAdhes);
        result.set(QtPcbLayerId::FPaste);
        result.set(QtPcbLayerId::BPaste);
        result.set(QtPcbLayerId::FCrtYd);
        result.set(QtPcbLayerId::BCrtYd);
        result.set(QtPcbLayerId::FFab);
        result.set(QtPcbLayerId::BFab);
        return result;
    }
};

#endif // QTLAYERSET_DEFINED

// GAL Layer ID enum for UI elements
enum class QtLayerGalId {
    ViaHolesTop = 100,
    ViaHolesBottom = 101,
    ViasNetnames = 102,
    PadsNetnames = 103,
    TracksNetnames = 104
};

// Layer utilities class
class QtLayerIdUtils {
public:
    static QtPcbLayerId flipLayer(QtPcbLayerId layer, int copperCount) {
        // Simple flip logic - in real implementation would handle all layer types
        switch (layer) {
            case QtPcbLayerId::FCu: return QtPcbLayerId::BCu;
            case QtPcbLayerId::BCu: return QtPcbLayerId::FCu;
            case QtPcbLayerId::FSilkS: return QtPcbLayerId::BSilkS;
            case QtPcbLayerId::BSilkS: return QtPcbLayerId::FSilkS;
            default: return layer; // Other layers don't flip
        }
    }
    
    static bool isElementVisible(QtPcbLayerId layer, bool visible = true) {
        Q_UNUSED(layer)
        return visible; // Simplified implementation
    }
    
    static QtPcbLayerId getNetNameLayer(QtPcbLayerId layer) {
        // Return appropriate layer for net names
        switch (layer) {
            case QtPcbLayerId::FCu: return QtPcbLayerId::FSilkS;
            case QtPcbLayerId::BCu: return QtPcbLayerId::BSilkS;
            default: return layer;
        }
    }
    
    static bool isCopperLayer(QtPcbLayerId layer) {
        return (layer >= QtPcbLayerId::FCu && layer <= QtPcbLayerId::In30Cu);
    }
    
    static int getLayerOrder(QtPcbLayerId layer) {
        return static_cast<int>(layer);
    }
    
    static QtPcbLayerId getLayerFromOrder(int order) {
        return static_cast<QtPcbLayerId>(order);
    }
};

// Zone layer override settings for via/pad connections
struct QtZoneLayerOverride {
    enum class Mode {
        None = 0,           ///< No override
        ForceFlashed,       ///< Force via to be flashed on this layer
        ForceNotFlashed     ///< Force via to be not flashed on this layer
    };
    
    Mode mode = Mode::None;
    
    bool operator==(const QtZoneLayerOverride& other) const {
        return mode == other.mode;
    }
};

// View class extensions are now part of the main QtView class above

// Global utility functions
namespace QtKicadUtils {
    inline bool isElementVisible(QtPcbLayerId layer, const QtView* view = nullptr) {
        Q_UNUSED(view)
        return QtLayerIdUtils::isElementVisible(layer);
    }
    
    inline QtPcbLayerId getNetNameLayer(QtPcbLayerId layer) {
        return QtLayerIdUtils::getNetNameLayer(layer);
    }
    
    // LOD function with standard signature
    inline double viewGetLOD(int layer, const QtView* view) {
        if (view) {
            return view->viewGetLOD(layer);
        }
        return 1.0; // Default if no view
    }
}

// Additional utility functions for shape operations
inline bool isOnEdge() {
    return false; // Placeholder implementation
}

inline QtPcbLayerId flipLayer(QtPcbLayerId layer, int copperCount = 2) {
    return QtLayerIdUtils::flipLayer(layer, copperCount);
}

// Utility functions for layer identification
inline bool isBackLayer(QtPcbLayerId layer) {
    switch (layer) {
        case QtPcbLayerId::BCu:
        case QtPcbLayerId::BSilkS:
        case QtPcbLayerId::BMask:
        case QtPcbLayerId::BAdhes:
        case QtPcbLayerId::BPaste:
        case QtPcbLayerId::BCrtYd:
        case QtPcbLayerId::BFab:
            return true;
        default:
            return false;
    }
}

// Utility functions for geometric operations
inline QPointF rotatePoint(const QPointF& point, const QPointF& center, double angleRadians) {
    double cosAngle = qCos(angleRadians);
    double sinAngle = qSin(angleRadians);
    QPointF rel = point - center;
    return QPointF(center.x() + rel.x() * cosAngle - rel.y() * sinAngle,
                   center.y() + rel.x() * sinAngle + rel.y() * cosAngle);
}

inline double mirrorValue(double value, double center) {
    return 2.0 * center - value;
}


enum class QtKicadType : int
{
    // From general usage and constructor/function parameters
    Board,
    BoardConnectedItem,
    BoardItemContainer,
    EDA_TEXT_T,
    PCB_TEXTBOX_T,
    NotUsed,
    Pcb,
    PcbArc,
    PcbFootprint,
    PcbGenerator,
    PcbGroup,
    PcbPad,
    PcbShape,
    PcbTable,
    PcbTrace,
    PcbVia,
    PcbZone,
    ZoneT, // Likely an alias for PcbZoneT

    // From s_typeNames initialization (likely the canonical names)
    PcbT,
    ScreenT,
    PcbFootprintT,
    PcbPadT,
    PcbShapeT,
    PcbReferenceImageT,
    PcbFieldT,
    PcbGeneratorT,
    PcbTextT,
    PcbTextboxT,
    PcbTableT,
    PcbTablecellT,
    PcbTraceT,
    PcbViaT,
    PcbArcT,
    PcbMarkerT,
    PcbDimensionT,
    PcbZoneT,
    PcbGroupT,
    PcbTargetT,
    TypeNotInit,

    // From isType() checks (specialized scan types)
    PcbLocateBoardEdge,
    PcbShapeLocateArc,
    PcbShapeLocateCircle,
    PcbShapeLocateRect,
    PcbShapeLocateSegment,
    PcbShapeLocatePoly,
    PcbShapeLocateBezier,
    PCB_LOCATE_UVIA_T,
    PCB_LOCATE_STDVIA_T,
    PCB_LOCATE_BBVIA_T,
    SchLocateAnyT,

    // Legacy or alternative names
    PCB_TRACE_T,
    PCB_VIA_T
};

// Legacy type alias for backward compatibility
using QtKicadT = QtKicadType;

// Individual type constants for backward compatibility
constexpr QtKicadType PCB_TEXTBOX_T = QtKicadType::PcbTextboxT;
constexpr QtKicadType PCB_LOCATE_TEXT_T = QtKicadType::PcbTextT;
constexpr QtKicadType PCB_FOOTPRINT_T = QtKicadType::PcbFootprintT;
constexpr QtKicadType PCB_TEXT_T = QtKicadType::PcbTextT;
constexpr QtKicadType PCB_SHAPE_T = QtKicadType::PcbShapeT;
constexpr QtKicadType PCB_PAD_T = QtKicadType::PcbPadT;
constexpr QtKicadType PCB_ZONE_T = QtKicadType::PcbZoneT;
constexpr QtKicadType PCB_GROUP_T = QtKicadType::PcbGroupT;
constexpr QtKicadType PCB_TABLE_T = QtKicadType::PcbTableT;
constexpr QtKicadType PCB_REFERENCE_IMAGE_T = QtKicadType::PcbReferenceImageT;
constexpr QtKicadType PCB_FIELD_T = QtKicadType::PcbFieldT;
constexpr QtKicadType PCB_DIM_ALIGNED_T = QtKicadType::PcbDimensionT;
constexpr QtKicadType PCB_DIM_LEADER_T = QtKicadType::PcbDimensionT;
constexpr QtKicadType PCB_DIM_CENTER_T = QtKicadType::PcbDimensionT;
constexpr QtKicadType PCB_DIM_RADIAL_T = QtKicadType::PcbDimensionT;
constexpr QtKicadType PCB_DIM_ORTHOGONAL_T = QtKicadType::PcbDimensionT;

// Flashing enumeration for via/pad rendering
enum class QtFlashing : int
{
    Default = 0,
    Always,
    Never
};

// Error location enumeration for shape conversion
enum class QtErrorLoc : int
{
    ErrorInside = 0,
    ErrorOutside = 1,
    Inside = 0,      // Alias for compatibility
    Outside = 1      // Alias for compatibility
};

// Add global constants for enum access
constexpr QtFlashing Default = QtFlashing::Default;
constexpr QtErrorLoc Inside = QtErrorLoc::Inside;
constexpr QtErrorLoc Outside = QtErrorLoc::Outside;

// Type aliases for backward compatibility
using FlipDirection = QtFlipDirection;
using ErrorLoc = QtErrorLoc;
using PcbLayerId = QtPcbLayerId;

// Text alignment enumerations
enum class GrTextHAlignT : int {
    LEFT = 0,
    CENTER,
    RIGHT
};

enum class GrTextVAlignT : int {
    TOP = 0,
    CENTER,
    BOTTOM
};

// Global constants for text alignment
constexpr GrTextHAlignT LEFT = GrTextHAlignT::LEFT;
constexpr GrTextHAlignT CENTER = GrTextHAlignT::CENTER;
constexpr GrTextHAlignT RIGHT = GrTextHAlignT::RIGHT;
constexpr GrTextVAlignT TOP = GrTextVAlignT::TOP;
constexpr GrTextVAlignT BOTTOM = GrTextVAlignT::BOTTOM;

// Bitmaps enumeration for menu icons
enum class QtBitmaps : int
{
    TextDefault = 0,
    TextBold,
    TextItalic,
    Via,
    ViaBuried,
    ViaMicro,
    DummyItem
};

// Type alias for backward compatibility
using Bitmaps = QtBitmaps;

//==============================================================================
// Font and Text Metrics - Used by text classes
//==============================================================================

// Temporary QtFontMetrics implementation
class QtFontMetrics {
public:
    QtFontMetrics() = default;
    ~QtFontMetrics() = default;
    
    // Basic measurements
    int height() const { return 12; }
    int width(const QString& text) const { return text.length() * 8; }
    int ascent() const { return 10; }
    int descent() const { return 2; }
    QSize size(const QString& text) const { return QSize(width(text), height()); }
};

// Angle constants - commonly used in PCB rotation
#ifndef ANGLE_0
#define ANGLE_0     0.0
#define ANGLE_90    90.0
#define ANGLE_180   180.0
#define ANGLE_270   270.0
#endif


//==============================================================================
// Zone-specific enums
//==============================================================================

// Zone connection type is defined in eda_padstack_data.h

// Zone border style
enum class QtZoneBorderStyle : int
{
    NO_HATCH = 0,
    DIAGONAL_EDGE,
    DIAGONAL_FULL,
    DIAGONAL = DIAGONAL_FULL  // Alias
};

// Legacy line segment class
class QtSeg {
public:
    QtSeg() = default;
    QtSeg(const QPointF& start, const QPointF& end) : m_start(start), m_end(end) {}
    
    QPointF m_start;
    QPointF m_end;
};

//==============================================================================
// Shape Collision Support
//==============================================================================

// Add collision methods to QtShapePolySet
inline bool QtShapePolySet::collideVertex(const QPointF& point, VertexIndex* index, int accuracy) const {
    Q_UNUSED(point)
    Q_UNUSED(index)
    Q_UNUSED(accuracy)
    return false; // Placeholder
}

inline bool QtShapePolySet::collideEdge(const QPointF& point, VertexIndex* index, int accuracy) const {
    Q_UNUSED(point)
    Q_UNUSED(index)
    Q_UNUSED(accuracy)
    return false; // Placeholder
}

inline bool QtShapePolySet::collide(const QPointF& point, int accuracy) const {
    Q_UNUSED(point)
    Q_UNUSED(accuracy)
    return false; // Placeholder
}

//==============================================================================
// Global Helper Functions
//==============================================================================
inline QtPcbLayerId getNetNameLayer(QtPcbLayerId layer) {
    // Simple implementation - just return the same layer for now
    return layer;
}

// QtEdaSearchData constructor implementation is in the source files that include eda_object_data.h

// Add mode and remove mode constants for backward compatibility
// These will be properly defined when eda_board_object_container.h is included
#ifndef QTADDMODE_CONSTANTS_DEFINED
#define QTADDMODE_CONSTANTS_DEFINED

// Forward declaration of the actual enums - avoid circular include
enum class QtAddMode : int;
enum class QtRemoveMode : int;

#endif // QTADDMODE_CONSTANTS_DEFINED

#endif // QT_TEMPORARY_IMPLEMENTATIONS_H