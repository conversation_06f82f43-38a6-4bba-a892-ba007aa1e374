/*
 * Qt-based reimplementation of KiCad EDA_TEXT class
 * 
 * This implementation provides comprehensive text handling using Qt's
 * advanced typography system with full Unicode support, advanced font
 * rendering, and integrated internationalization capabilities.
 */

#include "eda_text_data.h"
#include <QtCore/QDebug>
#include <QtCore/QJsonDocument>
#include <QtCore/QJsonObject>
#include <QtCore/QJsonArray>
#include <QtCore/QRegularExpression>
#include <QtCore/QLoggingCategory>
#include <QtGui/QPainter>
#include <QtGui/QFontDatabase>
#include <QtGui/QTextDocument>
#include <QtGui/QAbstractTextDocumentLayout>
#include <QtWidgets/QApplication>
#include <algorithm>
#include <cmath>

Q_LOGGING_CATEGORY(lcEDA_TEXT_DATA, "qt.kicad.eda_text")

namespace {
    // Constants for text processing and limits
    constexpr double EPSILON = 1e-9;
    constexpr double DEFAULT_LINE_SPACING = 1.0;
    constexpr int DEFAULT_STROKE_WIDTH = 0;
    constexpr double DEGREES_TO_RADIANS = M_PI / 180.0;
    constexpr double RADIANS_TO_DEGREES = 180.0 / M_PI;
    
    // Default text size in points (converted from mils)
    constexpr double DEFAULT_TEXT_SIZE_PT = 3.5;  // ~50 mils
    
    // Utility functions for geometric calculations
    inline double distance(const QPointF& p1, const QPointF& p2) {
        QPointF delta = p2 - p1;
        return std::sqrt(delta.x() * delta.x() + delta.y() * delta.y());
    }
    
    inline QPointF rotatePoint(const QPointF& point, const QPointF& center, double angleRadians) {
        double cos_a = std::cos(angleRadians);
        double sin_a = std::sin(angleRadians);
        QPointF translated = point - center;
        return QPointF(
            translated.x() * cos_a - translated.y() * sin_a,
            translated.x() * sin_a + translated.y() * cos_a
        ) + center;
    }
    
    // Text variable processing
    QString processTextVariables(const QString& text) {
        QString result = text;
        // Simple text variable processing - replace ${VAR} patterns
        QRegularExpression varRegex(R"(\$\{([^}]+)\})");
        QRegularExpressionMatchIterator matches = varRegex.globalMatch(text);
        
        while (matches.hasNext()) {
            QRegularExpressionMatch match = matches.next();
            QString varName = match.captured(1);
            QString varValue = qgetenv(varName.toLocal8Bit());
            if (!varValue.isEmpty()) {
                result.replace(match.captured(0), varValue);
            }
        }
        return result;
    }
    
    // Font utilities
    QFont createFont(const QString& family, double sizePt, bool bold, bool italic) {
        QFont font(family, qRound(sizePt));
        font.setBold(bold);
        font.setItalic(italic);
        return font;
    }
    
    // Calculate effective stroke width for bold text
    int calculateBoldStrokeWidth(double textSize) {
        return std::max(1, qRound(textSize * 0.15));  // 15% of text size
    }
}

//=============================================================================
// QTEXTATTRIBUTES IMPLEMENTATION
//=============================================================================

bool QtTextAttributes::operator==(const QtTextAttributes& other) const {
    return fontFamily == other.fontFamily &&
           horizontalAlign == other.horizontalAlign &&
           verticalAlign == other.verticalAlign &&
           qAbs(angleRadians - other.angleRadians) < EPSILON &&
           qAbs(lineSpacing - other.lineSpacing) < EPSILON &&
           strokeWidth == other.strokeWidth &&
           italic == other.italic &&
           bold == other.bold &&
           underlined == other.underlined &&
           color == other.color &&
           mirrored == other.mirrored &&
           multiline == other.multiline &&
           qAbs(size.width() - other.size.width()) < EPSILON &&
           qAbs(size.height() - other.size.height()) < EPSILON &&
           keepUpright == other.keepUpright;
}

//=============================================================================
// EDA_TEXT_DATA IMPLEMENTATION
//=============================================================================

EDA_TEXT_DATA::EDA_TEXT_DATA(const QString& text)
    : m_text(text)
    , m_shownTextHasVars(false)
    , m_position(0.0, 0.0)
    , m_visible(true)
    , m_renderCacheFont(QApplication::font())
    , m_renderCacheAngle(0.0)
    , m_renderCacheOffset(0.0, 0.0)
    , m_textLayout(nullptr)
    , m_layoutValid(false)
    , m_boundingRectValid(false)
{
    // Initialize with default attributes
    m_attributes.font = createFont("Arial", DEFAULT_TEXT_SIZE_PT, false, false);
    m_attributes.fontFamily = "Arial";
    m_attributes.size = QSizeF(DEFAULT_TEXT_SIZE_PT * 0.3527, DEFAULT_TEXT_SIZE_PT * 0.3527); // Convert pt to mm
    m_attributes.color = QColor(Qt::black);
    
    cacheShownText();
    
}

EDA_TEXT_DATA::EDA_TEXT_DATA(const EDA_TEXT_DATA& other)
    : m_text(other.m_text)
    , m_shownText(other.m_shownText)
    , m_shownTextHasVars(other.m_shownTextHasVars)
    , m_position(other.m_position)
    , m_visible(other.m_visible)
    , m_attributes(other.m_attributes)
    , m_unresolvedFontName(other.m_unresolvedFontName)
    , m_hyperlink(other.m_hyperlink)
    , m_renderCacheFont(other.m_renderCacheFont)
    , m_renderCacheText(other.m_renderCacheText)
    , m_renderCacheAngle(other.m_renderCacheAngle)
    , m_renderCacheOffset(other.m_renderCacheOffset)
    , m_bboxCache(other.m_bboxCache)
    , m_textLayout(nullptr)
    , m_layoutValid(false)
    , m_boundingRectValid(false)
{
}

EDA_TEXT_DATA::~EDA_TEXT_DATA()
{
    delete m_textLayout;
}

//=============================================================================
// TEXT CONTENT AND PROCESSING
//=============================================================================

QString EDA_TEXT_DATA::getShownText(bool allowExtraText, int depth) const {
    Q_UNUSED(allowExtraText)
    Q_UNUSED(depth)
    
    if (m_shownText.isEmpty() || depth > 10) {  // Prevent infinite recursion
        return m_text;
    }
    
    return m_shownText;
}

void EDA_TEXT_DATA::setText(const QString& text) {
    if (m_text != text) {
        m_text = text;
        cacheShownText();
        invalidateCache();
    }
}

void EDA_TEXT_DATA::copyText(const EDA_TEXT_DATA& source) {
    setText(source.m_text);
}

void EDA_TEXT_DATA::clear() {
    setText(QString());
}

//=============================================================================
// TYPOGRAPHY AND FONT MANAGEMENT  
//=============================================================================

void EDA_TEXT_DATA::setFont(const QFont& font) {
    if (m_attributes.font != font) {
        m_attributes.font = font;
        m_attributes.fontFamily = font.family();
        invalidateCache();
    }
}

void EDA_TEXT_DATA::setFontFamily(const QString& family) {
    if (m_attributes.fontFamily != family) {
        m_attributes.fontFamily = family;
        m_attributes.font.setFamily(family);
        invalidateCache();
    }
}

int EDA_TEXT_DATA::getFontIndex() const {
    QFontDatabase db;
    QStringList families = db.families();
    return families.indexOf(m_attributes.fontFamily);
}

void EDA_TEXT_DATA::setFontIndex(int index) {
    QFontDatabase db;
    QStringList families = db.families();
    if (index >= 0 && index < families.size()) {
        setFontFamily(families[index]);
    }
}

void EDA_TEXT_DATA::setUnresolvedFontName(const QString& fontName) {
    m_unresolvedFontName = fontName;
}

bool EDA_TEXT_DATA::resolveFont(const QStringList& embeddedFonts) {
    if (m_unresolvedFontName.isEmpty()) {
        return true;
    }
    
    QFontDatabase db;
    QStringList availableFonts = db.families();
    
    // First try embedded fonts
    for (const QString& embeddedFont : embeddedFonts) {
        if (embeddedFont.contains(m_unresolvedFontName, Qt::CaseInsensitive)) {
            setFontFamily(embeddedFont);
            m_unresolvedFontName.clear();
            return true;
        }
    }
    
    // Then try system fonts
    if (availableFonts.contains(m_unresolvedFontName, Qt::CaseInsensitive)) {
        setFontFamily(m_unresolvedFontName);
        m_unresolvedFontName.clear();
        return true;
    }
    
    // Fall back to default font
    qCWarning(lcEDA_TEXT_DATA) << "Could not resolve font:" << m_unresolvedFontName;
    return false;
}

QString EDA_TEXT_DATA::getFontName() const {
    return m_attributes.fontFamily;
}

//=============================================================================
// TEXT SIZE AND DIMENSIONS
//=============================================================================

void EDA_TEXT_DATA::setTextSize(const QSizeF& size, bool enforceMinSize) {
    QSizeF newSize = size;
    
    if (enforceMinSize) {
        newSize.setWidth(std::max(newSize.width(), QtTextLimits::MIN_SIZE_MM));
        newSize.setHeight(std::max(newSize.height(), QtTextLimits::MIN_SIZE_MM));
        newSize.setWidth(std::min(newSize.width(), QtTextLimits::MAX_SIZE_MM));
        newSize.setHeight(std::min(newSize.height(), QtTextLimits::MAX_SIZE_MM));
    }
    
    if (m_attributes.size != newSize) {
        m_attributes.size = newSize;
        
        // Update Qt font size (convert mm to points: 1mm ≈ 2.835 pt)
        double fontSizePt = newSize.height() * 2.835;
        m_attributes.font.setPointSizeF(fontSizePt);
        
        invalidateCache();
    }
}

void EDA_TEXT_DATA::setTextSize(double width, double height, bool enforceMinSize) {
    setTextSize(QSizeF(width, height), enforceMinSize);
}

void EDA_TEXT_DATA::setTextWidth(double width) {
    setTextSize(QSizeF(width, m_attributes.size.height()));
}

void EDA_TEXT_DATA::setTextHeight(double height) {
    setTextSize(QSizeF(m_attributes.size.width(), height));
}

//=============================================================================
// POSITION AND TRANSFORMATION
//=============================================================================

void EDA_TEXT_DATA::setPosition(const QPointF& position) {
    if (m_position != position) {
        m_position = position;
        invalidateCache();
    }
}

void EDA_TEXT_DATA::setPosition(double x, double y) {
    setPosition(QPointF(x, y));
}

void EDA_TEXT_DATA::setX(double x) {
    setPosition(QPointF(x, m_position.y()));
}

void EDA_TEXT_DATA::setY(double y) {
    setPosition(QPointF(m_position.x(), y));
}

void EDA_TEXT_DATA::offset(const QPointF& offset) {
    setPosition(m_position + offset);
}

//=============================================================================
// ROTATION AND ANGLE MANAGEMENT
//=============================================================================

void EDA_TEXT_DATA::setAngleRadians(double radians) {
    // Normalize angle to [0, 2π)
    double normalizedAngle = std::fmod(radians, 2.0 * M_PI);
    if (normalizedAngle < 0) {
        normalizedAngle += 2.0 * M_PI;
    }
    
    if (std::abs(m_attributes.angleRadians - normalizedAngle) > EPSILON) {
        m_attributes.angleRadians = normalizedAngle;
        invalidateCache();
    }
}

void EDA_TEXT_DATA::setAngleDegrees(double degrees) {
    setAngleRadians(degrees * DEGREES_TO_RADIANS);
}

//=============================================================================
// TEXT STYLE PROPERTIES
//=============================================================================

void EDA_TEXT_DATA::setBold(bool bold) {
    if (m_attributes.bold != bold) {
        // For Qt fonts, adjust stroke width when toggling bold
        if (bold) {
            m_attributes.storedStrokeWidth = m_attributes.strokeWidth;
            m_attributes.strokeWidth = calculateBoldStrokeWidth(m_attributes.size.height());
        } else {
            // Restore original stroke width
            if (m_attributes.storedStrokeWidth > 0) {
                m_attributes.strokeWidth = m_attributes.storedStrokeWidth;
            }
        }
        
        setBoldFlag(bold);
    }
}

void EDA_TEXT_DATA::setBoldFlag(bool bold) {
    if (m_attributes.bold != bold) {
        m_attributes.bold = bold;
        m_attributes.font.setBold(bold);
        invalidateCache();
    }
}

void EDA_TEXT_DATA::setItalic(bool italic) {
    setItalicFlag(italic);
}

void EDA_TEXT_DATA::setItalicFlag(bool italic) {
    if (m_attributes.italic != italic) {
        m_attributes.italic = italic;
        m_attributes.font.setItalic(italic);
        invalidateCache();
    }
}

void EDA_TEXT_DATA::setUnderlined(bool underlined) {
    if (m_attributes.underlined != underlined) {
        m_attributes.underlined = underlined;
        m_attributes.font.setUnderline(underlined);
        invalidateCache();
    }
}

QString EDA_TEXT_DATA::getStyleName() const {
    QStringList styles;
    if (m_attributes.bold) styles << "Bold";
    if (m_attributes.italic) styles << "Italic";
    if (m_attributes.underlined) styles << "Underlined";
    
    return styles.isEmpty() ? "Normal" : styles.join("+");
}

//=============================================================================
// STROKE AND COLOR PROPERTIES
//=============================================================================

void EDA_TEXT_DATA::setStrokeWidth(int width) {
    if (m_attributes.strokeWidth != width) {
        m_attributes.strokeWidth = width;
        invalidateCache();
    }
}

int EDA_TEXT_DATA::getEffectiveStrokeWidth(int defaultWidth) const {
    return m_attributes.strokeWidth > 0 ? m_attributes.strokeWidth : defaultWidth;
}

void EDA_TEXT_DATA::setTextColor(const QColor& color) {
    if (m_attributes.color != color) {
        m_attributes.color = color;
    }
}

//=============================================================================
// VISIBILITY AND TRANSFORMATION PROPERTIES
//=============================================================================

void EDA_TEXT_DATA::setVisible(bool visible) {
    if (m_visible != visible) {
        m_visible = visible;
    }
}

void EDA_TEXT_DATA::setMirrored(bool mirrored) {
    if (m_attributes.mirrored != mirrored) {
        m_attributes.mirrored = mirrored;
        invalidateCache();
    }
}

void EDA_TEXT_DATA::setKeepUpright(bool keepUpright) {
    if (m_attributes.keepUpright != keepUpright) {
        m_attributes.keepUpright = keepUpright;
        invalidateCache();
    }
}

//=============================================================================
// TEXT ALIGNMENT AND LAYOUT
//=============================================================================

void EDA_TEXT_DATA::setHorizontalAlign(QtTextHAlign align) {
    if (m_attributes.horizontalAlign != align) {
        m_attributes.horizontalAlign = align;
        invalidateCache();
    }
}

void EDA_TEXT_DATA::setVerticalAlign(QtTextVAlign align) {
    if (m_attributes.verticalAlign != align) {
        m_attributes.verticalAlign = align;
        invalidateCache();
    }
}

void EDA_TEXT_DATA::flipHorizontalAlignment() {
    setHorizontalAlign(getFlippedAlignment(m_attributes.horizontalAlign));
}

void EDA_TEXT_DATA::setMultiline(bool multiline) {
    if (m_attributes.multiline != multiline) {
        m_attributes.multiline = multiline;
        invalidateCache();
    }
}

void EDA_TEXT_DATA::setLineSpacing(double spacing) {
    if (std::abs(m_attributes.lineSpacing - spacing) > EPSILON) {
        m_attributes.lineSpacing = spacing;
        invalidateCache();
    }
}

double EDA_TEXT_DATA::getInterline() const {
    QFontMetrics metrics(m_attributes.font);
    return metrics.lineSpacing() * m_attributes.lineSpacing;
}

//=============================================================================
// TEXT ATTRIBUTES MANAGEMENT
//=============================================================================

void EDA_TEXT_DATA::setAttributes(const QtTextAttributes& attributes) {
    if (m_attributes != attributes) {
        m_attributes = attributes;
        invalidateCache();
    }
}

void EDA_TEXT_DATA::setAttributes(const EDA_TEXT_DATA& source, bool includePosition) {
    m_attributes = source.m_attributes;
    if (includePosition) {
        setPosition(source.m_position);
    }
    
    invalidateCache();
}

void EDA_TEXT_DATA::swapAttributes(EDA_TEXT_DATA& other) {
    std::swap(m_attributes, other.m_attributes);
    invalidateCache();
    other.invalidateCache();
}

void EDA_TEXT_DATA::swapText(EDA_TEXT_DATA& other) {
    std::swap(m_text, other.m_text);
    std::swap(m_shownText, other.m_shownText);
    std::swap(m_shownTextHasVars, other.m_shownTextHasVars);
    
    invalidateCache();
    other.invalidateCache();
    
}

//=============================================================================
// HYPERLINK SUPPORT
//=============================================================================

void EDA_TEXT_DATA::setHyperlink(const QUrl& url) {
    if (m_hyperlink != url) {
        m_hyperlink = url;
    }
}

void EDA_TEXT_DATA::setHyperlink(const QString& url) {
    setHyperlink(QUrl(url));
}

void EDA_TEXT_DATA::removeHyperlink() {
    setHyperlink(QUrl());
}

bool EDA_TEXT_DATA::isValidHyperlink(const QUrl& url) {
    return url.isValid() && !url.isEmpty();
}

bool EDA_TEXT_DATA::isValidHyperlink(const QString& url) {
    return isValidHyperlink(QUrl(url));
}

bool EDA_TEXT_DATA::isInternalPageLink(const QString& href, QString* destination) {
    QRegularExpression pageRegex(R"(#page(\d+))");
    QRegularExpressionMatch match = pageRegex.match(href);
    
    if (match.hasMatch()) {
        if (destination) {
            *destination = match.captured(1);
        }
        return true;
    }
    return false;
}

QString EDA_TEXT_DATA::createPageLink(const QString& destination) {
    return QString("#page%1").arg(destination);
}

//=============================================================================
// GEOMETRIC CALCULATIONS AND HIT TESTING
//=============================================================================

QRectF EDA_TEXT_DATA::getBoundingRect(int line) const {
    Q_UNUSED(line)  // TODO: Implement per-line bounding rect
    
    if (!m_boundingRectValid) {
        ensureLayoutValid();
        
        QFontMetrics metrics(m_attributes.font);
        QRectF textRect = metrics.boundingRect(getShownText());
        
        // Apply transformations
        if (std::abs(m_attributes.angleRadians) > EPSILON) {
            QTransform transform;
            transform.translate(m_position.x(), m_position.y());
            transform.rotate(m_attributes.angleDegrees());
            transform.translate(-m_position.x(), -m_position.y());
            textRect = transform.mapRect(textRect);
        }
        
        // Apply alignment offset
        QPointF alignOffset(0, 0);
        switch (m_attributes.horizontalAlign) {
            case QtTextHAlign::Center:
                alignOffset.setX(-textRect.width() / 2);
                break;
            case QtTextHAlign::Right:
                alignOffset.setX(-textRect.width());
                break;
            default:
                break;
        }
        
        switch (m_attributes.verticalAlign) {
            case QtTextVAlign::Center:
                alignOffset.setY(-textRect.height() / 2);
                break;
            case QtTextVAlign::Bottom:
                alignOffset.setY(-textRect.height());
                break;
            default:
                break;
        }
        
        textRect.translate(m_position + alignOffset);
        m_cachedBoundingRect = textRect;
        m_boundingRectValid = true;
    }
    
    return m_cachedBoundingRect;
}

bool EDA_TEXT_DATA::hitTest(const QPointF& point, double accuracy) const {
    QRectF bbox = getBoundingRect();
    if (accuracy > 0) {
        bbox = bbox.adjusted(-accuracy, -accuracy, accuracy, accuracy);
    }
    return bbox.contains(point);
}

bool EDA_TEXT_DATA::hitTest(const QRectF& rect, bool contained, double accuracy) const {
    QRectF bbox = getBoundingRect();
    if (accuracy > 0) {
        bbox = bbox.adjusted(-accuracy, -accuracy, accuracy, accuracy);
    }
    
    if (contained) {
        return rect.contains(bbox);
    } else {
        return rect.intersects(bbox);
    }
}

//=============================================================================
// MULTILINE TEXT OPERATIONS
//=============================================================================

QVector<QPointF> EDA_TEXT_DATA::getLinePositions(int lineCount) const {
    QVector<QPointF> positions;
    
    if (!m_attributes.multiline || lineCount <= 1) {
        positions.append(m_position);
        return positions;
    }
    
    double interline = getInterline();
    QPointF startPos = m_position;
    
    // Adjust for vertical alignment
    if (m_attributes.verticalAlign == QtTextVAlign::Center) {
        startPos.setY(startPos.y() - (lineCount - 1) * interline / 2);
    } else if (m_attributes.verticalAlign == QtTextVAlign::Bottom) {
        startPos.setY(startPos.y() - (lineCount - 1) * interline);
    }
    
    for (int i = 0; i < lineCount; ++i) {
        QPointF linePos = startPos + QPointF(0, i * interline);
        
        // Apply rotation if needed
        if (std::abs(m_attributes.angleRadians) > EPSILON) {
            linePos = rotatePoint(linePos, m_position, m_attributes.angleRadians);
        }
        
        positions.append(linePos);
    }
    
    return positions;
}

int EDA_TEXT_DATA::getLineCount() const {
    if (!m_attributes.multiline) {
        return 1;
    }
    return getShownText().split('\n').count();
}

QStringList EDA_TEXT_DATA::getTextLines() const {
    if (!m_attributes.multiline) {
        return QStringList() << getShownText();
    }
    return getShownText().split('\n');
}

//=============================================================================
// SEARCH AND REPLACE FUNCTIONALITY
//=============================================================================

bool EDA_TEXT_DATA::replace(const QtSearchData& searchData) {
    // TODO: Implement proper search and replace
    Q_UNUSED(searchData)
    return false;
}

bool EDA_TEXT_DATA::matches(const QString& searchText, const QtSearchData& searchData) const {
    // TODO: Implement proper text matching
    Q_UNUSED(searchData)
    return getShownText().contains(searchText, Qt::CaseInsensitive);
}

//=============================================================================
// COMPARISON AND SIMILARITY
//=============================================================================

int EDA_TEXT_DATA::compare(const EDA_TEXT_DATA& other) const {
    // Compare text content first
    int textResult = m_text.compare(other.m_text);
    if (textResult != 0) return textResult;
    
    // Compare position
    if (m_position.x() < other.m_position.x()) return -1;
    if (m_position.x() > other.m_position.x()) return 1;
    if (m_position.y() < other.m_position.y()) return -1;
    if (m_position.y() > other.m_position.y()) return 1;
    
    // Compare attributes
    if (m_attributes.size.width() < other.m_attributes.size.width()) return -1;
    if (m_attributes.size.width() > other.m_attributes.size.width()) return 1;
    if (m_attributes.size.height() < other.m_attributes.size.height()) return -1;
    if (m_attributes.size.height() > other.m_attributes.size.height()) return 1;
    
    return 0;
}

double EDA_TEXT_DATA::similarity(const EDA_TEXT_DATA& other) const {
    double textSim = 1.0 - levenshteinDistance(other);
    double posSim = 1.0 / (1.0 + distance(m_position, other.m_position));
    double sizeSim = 1.0 / (1.0 + std::abs(m_attributes.size.width() - other.m_attributes.size.width()));
    
    return (textSim + posSim + sizeSim) / 3.0;
}

double EDA_TEXT_DATA::levenshteinDistance(const EDA_TEXT_DATA& other) const {
    const QString& s1 = m_text;
    const QString& s2 = other.m_text;
    
    if (s1.isEmpty()) return s2.length();
    if (s2.isEmpty()) return s1.length();
    
    QVector<QVector<int>> matrix(s1.length() + 1, QVector<int>(s2.length() + 1));
    
    for (int i = 0; i <= s1.length(); ++i) matrix[i][0] = i;
    for (int j = 0; j <= s2.length(); ++j) matrix[0][j] = j;
    
    for (int i = 1; i <= s1.length(); ++i) {
        for (int j = 1; j <= s2.length(); ++j) {
            int cost = (s1[i-1] == s2[j-1]) ? 0 : 1;
            matrix[i][j] = std::min({
                matrix[i-1][j] + 1,      // deletion
                matrix[i][j-1] + 1,      // insertion
                matrix[i-1][j-1] + cost  // substitution
            });
        }
    }
    
    int maxLen = std::max(s1.length(), s2.length());
    return maxLen > 0 ? static_cast<double>(matrix[s1.length()][s2.length()]) / maxLen : 0.0;
}

//=============================================================================
// FORMATTING AND DEFAULT STATE
//=============================================================================

bool EDA_TEXT_DATA::isDefaultFormatting() const {
    QtTextAttributes defaultAttrs;
    return m_attributes == defaultAttrs && m_visible;
}

//=============================================================================
// SERIALIZATION SUPPORT
//=============================================================================

QJsonObject EDA_TEXT_DATA::serialize() const {
    return toJson();
}

bool EDA_TEXT_DATA::deserialize(const QJsonObject& json) {
    return fromJson(json);
}

QJsonObject EDA_TEXT_DATA::toJson() const {
    QJsonObject json;
    
    json["text"] = m_text;
    json["position"] = QJsonArray{m_position.x(), m_position.y()};
    json["visible"] = m_visible;
    
    if (hasHyperlink()) {
        json["hyperlink"] = m_hyperlink.toString();
    }
    
    // Serialize attributes
    QJsonObject attrs;
    attrs["fontFamily"] = m_attributes.fontFamily;
    attrs["horizontalAlign"] = static_cast<int>(m_attributes.horizontalAlign);
    attrs["verticalAlign"] = static_cast<int>(m_attributes.verticalAlign);
    attrs["angleRadians"] = m_attributes.angleRadians;
    attrs["lineSpacing"] = m_attributes.lineSpacing;
    attrs["strokeWidth"] = m_attributes.strokeWidth;
    attrs["italic"] = m_attributes.italic;
    attrs["bold"] = m_attributes.bold;
    attrs["underlined"] = m_attributes.underlined;
    attrs["color"] = m_attributes.color.name();
    attrs["mirrored"] = m_attributes.mirrored;
    attrs["multiline"] = m_attributes.multiline;
    attrs["size"] = QJsonArray{m_attributes.size.width(), m_attributes.size.height()};
    attrs["keepUpright"] = m_attributes.keepUpright;
    
    json["attributes"] = attrs;
    
    return json;
}

bool EDA_TEXT_DATA::fromJson(const QJsonObject& json) {
    if (!json.contains("text") || !json.contains("position") || !json.contains("attributes")) {
        return false;
    }
    
    // Load basic properties
    m_text = json["text"].toString();
    QJsonArray posArray = json["position"].toArray();
    if (posArray.size() == 2) {
        m_position = QPointF(posArray[0].toDouble(), posArray[1].toDouble());
    }
    
    m_visible = json.contains("visible") ? json.value("visible").toBool() : true;
    
    if (json.contains("hyperlink")) {
        m_hyperlink = QUrl(json["hyperlink"].toString());
    }
    
    // Load attributes
    QJsonObject attrs = json["attributes"].toObject();
    m_attributes.fontFamily = attrs.contains("fontFamily") ? attrs.value("fontFamily").toString() : "Arial";
    m_attributes.horizontalAlign = static_cast<QtTextHAlign>(attrs.contains("horizontalAlign") ? attrs.value("horizontalAlign").toInt() : 0);
    m_attributes.verticalAlign = static_cast<QtTextVAlign>(attrs.contains("verticalAlign") ? attrs.value("verticalAlign").toInt() : 0);
    m_attributes.angleRadians = attrs.contains("angleRadians") ? attrs.value("angleRadians").toDouble() : 0.0;
    m_attributes.lineSpacing = attrs.contains("lineSpacing") ? attrs.value("lineSpacing").toDouble() : 1.0;
    m_attributes.strokeWidth = attrs.contains("strokeWidth") ? attrs.value("strokeWidth").toInt() : 0;
    m_attributes.italic = attrs.contains("italic") ? attrs.value("italic").toBool() : false;
    m_attributes.bold = attrs.contains("bold") ? attrs.value("bold").toBool() : false;
    m_attributes.underlined = attrs.contains("underlined") ? attrs.value("underlined").toBool() : false;
    m_attributes.color = QColor(attrs.contains("color") ? attrs.value("color").toString() : "#000000");
    m_attributes.mirrored = attrs.contains("mirrored") ? attrs.value("mirrored").toBool() : false;
    m_attributes.multiline = attrs.contains("multiline") ? attrs.value("multiline").toBool() : false;
    m_attributes.keepUpright = attrs.contains("keepUpright") ? attrs.value("keepUpright").toBool() : false;
    
    QJsonArray sizeArray = attrs["size"].toArray();
    if (sizeArray.size() == 2) {
        m_attributes.size = QSizeF(sizeArray[0].toDouble(), sizeArray[1].toDouble());
    }
    
    // Update font with loaded attributes
    m_attributes.font = createFont(m_attributes.fontFamily, 
                                   m_attributes.size.height() * 2.835,  // Convert mm to pt
                                   m_attributes.bold, 
                                   m_attributes.italic);
    m_attributes.font.setUnderline(m_attributes.underlined);
    
    cacheShownText();
    invalidateCache();
    
    return true;
}

//=============================================================================
// UTILITY METHODS
//=============================================================================

EDA_TEXT_DATA* EDA_TEXT_DATA::clone() const {
    return new EDA_TEXT_DATA(*this);
}

QtTextHAlign EDA_TEXT_DATA::mapHorizontalAlign(int align) {
    if (align < static_cast<int>(QtTextHAlign::Left)) return QtTextHAlign::Left;
    if (align > static_cast<int>(QtTextHAlign::Indeterminate)) return QtTextHAlign::Indeterminate;
    return static_cast<QtTextHAlign>(align);
}

QtTextVAlign EDA_TEXT_DATA::mapVerticalAlign(int align) {
    if (align < static_cast<int>(QtTextVAlign::Top)) return QtTextVAlign::Top;
    if (align > static_cast<int>(QtTextVAlign::Indeterminate)) return QtTextVAlign::Indeterminate;
    return static_cast<QtTextVAlign>(align);
}

QString EDA_TEXT_DATA::alignmentToString(QtTextHAlign align) {
    switch (align) {
        case QtTextHAlign::Left: return "Left";
        case QtTextHAlign::Center: return "Center";
        case QtTextHAlign::Right: return "Right";
        case QtTextHAlign::Indeterminate: return "Indeterminate";
    }
    return "Unknown";
}

QString EDA_TEXT_DATA::alignmentToString(QtTextVAlign align) {
    switch (align) {
        case QtTextVAlign::Top: return "Top";
        case QtTextVAlign::Center: return "Center";
        case QtTextVAlign::Bottom: return "Bottom";
        case QtTextVAlign::Indeterminate: return "Indeterminate";
    }
    return "Unknown";
}

//=============================================================================
// PROTECTED HELPER METHODS
//=============================================================================

QFont EDA_TEXT_DATA::getDrawFont() const {
    return m_attributes.font;
}

QFontMetrics EDA_TEXT_DATA::getFontMetrics() const {
    return QFontMetrics(m_attributes.font);
}

void EDA_TEXT_DATA::cacheShownText() {
    if (m_text.isEmpty()) {
        m_shownText.clear();
        m_shownTextHasVars = false;
    } else {
        m_shownText = processTextVariables(m_text);
        m_shownTextHasVars = m_text.contains("${");
    }
}

void EDA_TEXT_DATA::updateDerivedProperties() {
    // Update any computed properties that depend on other attributes
    applyFontStyle();
}


void EDA_TEXT_DATA::applyFontStyle() {
    m_attributes.font.setBold(m_attributes.bold);
    m_attributes.font.setItalic(m_attributes.italic);
    m_attributes.font.setUnderline(m_attributes.underlined);
}

void EDA_TEXT_DATA::enforceTextSizeLimits() {
    QSizeF size = m_attributes.size;
    size.setWidth(qBound(QtTextLimits::MIN_SIZE_MM, size.width(), QtTextLimits::MAX_SIZE_MM));
    size.setHeight(qBound(QtTextLimits::MIN_SIZE_MM, size.height(), QtTextLimits::MAX_SIZE_MM));
    
    if (size != m_attributes.size) {
        m_attributes.size = size;
        double fontSizePt = size.height() * 2.835;  // Convert mm to pt
        m_attributes.font.setPointSizeF(fontSizePt);
    }
}

//=============================================================================
// CACHING AND PERFORMANCE OPTIMIZATION
//=============================================================================

void EDA_TEXT_DATA::clearRenderCache() {
    m_renderCacheFont = QFont();
    m_renderCacheText.clear();
    m_renderCacheAngle = 0.0;
    m_renderCacheOffset = QPointF(0, 0);
}

void EDA_TEXT_DATA::clearBoundingBoxCache() {
    m_bboxCache.clear();
    m_boundingRectValid = false;
}

void EDA_TEXT_DATA::setupRenderCache(const QString& resolvedText, const QFont& font,
                                double angle, const QPointF& offset) {
    m_renderCacheText = resolvedText;
    m_renderCacheFont = font;
    m_renderCacheAngle = angle;
    m_renderCacheOffset = offset;
}

void EDA_TEXT_DATA::invalidateLayout() {
    delete m_textLayout;
    m_textLayout = nullptr;
    m_layoutValid = false;
    clearBoundingBoxCache();
}

void EDA_TEXT_DATA::ensureLayoutValid() const {
    if (!m_layoutValid) {
        updateTextLayout();
    }
}

void EDA_TEXT_DATA::updateTextLayout() const {
    delete m_textLayout;
    m_textLayout = new QTextLayout(getShownText(), m_attributes.font);
    
    m_textLayout->beginLayout();
    QTextLine line = m_textLayout->createLine();
    if (line.isValid()) {
        line.setLineWidth(1000);  // Large width for single line
        line.setPosition(QPointF(0, 0));
    }
    m_textLayout->endLayout();
    
    m_layoutValid = true;
}
